[{"D:\\FlashCardManager\\flashcard-manager\\src\\index.tsx": "1", "D:\\FlashCardManager\\flashcard-manager\\src\\reportWebVitals.ts": "2", "D:\\FlashCardManager\\flashcard-manager\\src\\App.tsx": "3", "D:\\FlashCardManager\\flashcard-manager\\src\\utils\\storage.ts": "4", "D:\\FlashCardManager\\flashcard-manager\\src\\components\\FlashcardStats.tsx": "5", "D:\\FlashCardManager\\flashcard-manager\\src\\components\\FlashcardManager.tsx": "6", "D:\\FlashCardManager\\flashcard-manager\\src\\components\\QuizMode.tsx": "7", "D:\\FlashCardManager\\flashcard-manager\\src\\components\\ThemeToggle.tsx": "8", "D:\\FlashCardManager\\flashcard-manager\\src\\utils\\audio.ts": "9"}, {"size": 554, "mtime": 1750789403370, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1750789401175, "results": "12", "hashOfConfig": "11"}, {"size": 5543, "mtime": 1750877096486, "results": "13", "hashOfConfig": "11"}, {"size": 884, "mtime": 1750876907661, "results": "14", "hashOfConfig": "11"}, {"size": 6323, "mtime": 1750877042665, "results": "15", "hashOfConfig": "11"}, {"size": 7437, "mtime": 1750876958867, "results": "16", "hashOfConfig": "11"}, {"size": 7885, "mtime": 1750877005245, "results": "17", "hashOfConfig": "11"}, {"size": 2100, "mtime": 1750877060725, "results": "18", "hashOfConfig": "11"}, {"size": 1670, "mtime": 1750876920554, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ki9mls", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\FlashCardManager\\flashcard-manager\\src\\index.tsx", [], [], "D:\\FlashCardManager\\flashcard-manager\\src\\reportWebVitals.ts", [], [], "D:\\FlashCardManager\\flashcard-manager\\src\\App.tsx", [], [], "D:\\FlashCardManager\\flashcard-manager\\src\\utils\\storage.ts", [], [], "D:\\FlashCardManager\\flashcard-manager\\src\\components\\FlashcardStats.tsx", [], [], "D:\\FlashCardManager\\flashcard-manager\\src\\components\\FlashcardManager.tsx", [], [], "D:\\FlashCardManager\\flashcard-manager\\src\\components\\QuizMode.tsx", ["47"], [], "D:\\FlashCardManager\\flashcard-manager\\src\\components\\ThemeToggle.tsx", [], [], "D:\\FlashCardManager\\flashcard-manager\\src\\utils\\audio.ts", [], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 23, "column": 6, "nodeType": "50", "endLine": 23, "endColumn": 36, "suggestions": "51"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'selectRandomCard'. Either include it or remove the dependency array.", "ArrayExpression", ["52"], {"desc": "53", "fix": "54"}, "Update the dependencies array to be: [flashcards, currentCardIndex, selectRandomCard]", {"range": "55", "text": "56"}, [929, 959], "[flashcards, currentCardIndex, selectRandomCard]"]