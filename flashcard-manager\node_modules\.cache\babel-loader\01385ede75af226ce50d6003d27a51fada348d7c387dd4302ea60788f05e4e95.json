{"ast": null, "code": "import { polarToCartesian } from '../PolarUtils';\n/**\n * Only applicable for radial layouts\n * @param {Object} activeCoordinate ChartCoordinate\n * @returns {Object} RadialCursorPoints\n */\nexport function getRadialCursorPoints(activeCoordinate) {\n  var {\n    cx,\n    cy,\n    radius,\n    startAngle,\n    endAngle\n  } = activeCoordinate;\n  var startPoint = polarToCartesian(cx, cy, radius, startAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, endAngle);\n  return {\n    points: [startPoint, endPoint],\n    cx,\n    cy,\n    radius,\n    startAngle,\n    endAngle\n  };\n}", "map": {"version": 3, "names": ["polarToCartesian", "getRadialCursorPoints", "activeCoordinate", "cx", "cy", "radius", "startAngle", "endAngle", "startPoint", "endPoint", "points"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/util/cursor/getRadialCursorPoints.js"], "sourcesContent": ["import { polarToCartesian } from '../PolarUtils';\n/**\n * Only applicable for radial layouts\n * @param {Object} activeCoordinate ChartCoordinate\n * @returns {Object} RadialCursorPoints\n */\nexport function getRadialCursorPoints(activeCoordinate) {\n  var {\n    cx,\n    cy,\n    radius,\n    startAngle,\n    endAngle\n  } = activeCoordinate;\n  var startPoint = polarToCartesian(cx, cy, radius, startAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, endAngle);\n  return {\n    points: [startPoint, endPoint],\n    cx,\n    cy,\n    radius,\n    startAngle,\n    endAngle\n  };\n}"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,gBAAgB,EAAE;EACtD,IAAI;IACFC,EAAE;IACFC,EAAE;IACFC,MAAM;IACNC,UAAU;IACVC;EACF,CAAC,GAAGL,gBAAgB;EACpB,IAAIM,UAAU,GAAGR,gBAAgB,CAACG,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAEC,UAAU,CAAC;EAC7D,IAAIG,QAAQ,GAAGT,gBAAgB,CAACG,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAEE,QAAQ,CAAC;EACzD,OAAO;IACLG,MAAM,EAAE,CAACF,UAAU,EAAEC,QAAQ,CAAC;IAC9BN,EAAE;IACFC,EAAE;IACFC,MAAM;IACNC,UAAU;IACVC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}