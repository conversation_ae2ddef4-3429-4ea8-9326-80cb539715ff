{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { computeLinePoints } from '../../cartesian/Line';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getBandSizeOfAxis, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar pickLineSettings = (_state, _xAxisId, _yAxisId, _isPanorama, lineSettings) => lineSettings;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedLineSettings = createSelector([selectUnfilteredCartesianItems, pickLineSettings], (graphicalItems, lineSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'line' && lineSettingsFromProps.dataKey === cgis.dataKey && lineSettingsFromProps.data === cgis.data)) {\n    /*\n     * now, at least one of the lines has the same dataKey as the one in props.\n     * Is this a perfect match? Maybe not because we could theoretically have two different Lines with the same dataKey\n     * and the same stackId and the same data but still different lines, yes,\n     * but the chances of that happening are ... lowish.\n     *\n     * A proper fix would be to store the lineSettings in a state too, and compare references directly instead of enumerating the properties.\n     */\n    return lineSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectLinePoints = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectSynchronisedLineSettings, selectBandSize, selectChartDataWithIndexesIfNotInPanorama], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, lineSettings, bandSize, _ref) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (lineSettings == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    data\n  } = lineSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeLinePoints({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  });\n});", "map": {"version": 3, "names": ["createSelector", "computeLinePoints", "selectChartDataWithIndexesIfNotInPanorama", "selectChartLayout", "selectAxisWithScale", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "getBandSizeOfAxis", "isCategoricalAxis", "selectXAxisWithScale", "state", "xAxisId", "_yAxisId", "isPanorama", "selectXAxisTicks", "selectYAxisWithScale", "_xAxisId", "yAxisId", "selectYAxisTicks", "selectBandSize", "layout", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "pickLineSettings", "_state", "_isPanorama", "lineSettings", "selectSynchronisedLineSettings", "graphicalItems", "lineSettingsFromProps", "some", "cgis", "type", "dataKey", "data", "undefined", "selectLinePoints", "bandSize", "_ref", "chartData", "dataStartIndex", "dataEndIndex", "length", "displayedData", "slice"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/selectors/lineSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeLinePoints } from '../../cartesian/Line';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getBandSizeOfAxis, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar pickLineSettings = (_state, _xAxisId, _yAxisId, _isPanorama, lineSettings) => lineSettings;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedLineSettings = createSelector([selectUnfilteredCartesianItems, pickLineSettings], (graphicalItems, lineSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'line' && lineSettingsFromProps.dataKey === cgis.dataKey && lineSettingsFromProps.data === cgis.data)) {\n    /*\n     * now, at least one of the lines has the same dataKey as the one in props.\n     * Is this a perfect match? Maybe not because we could theoretically have two different Lines with the same dataKey\n     * and the same stackId and the same data but still different lines, yes,\n     * but the chances of that happening are ... lowish.\n     *\n     * A proper fix would be to store the lineSettings in a state too, and compare references directly instead of enumerating the properties.\n     */\n    return lineSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectLinePoints = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectSynchronisedLineSettings, selectBandSize, selectChartDataWithIndexesIfNotInPanorama], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, lineSettings, bandSize, _ref) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (lineSettings == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    data\n  } = lineSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeLinePoints({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  });\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,mBAAmB,EAAEC,0BAA0B,EAAEC,8BAA8B,QAAQ,iBAAiB;AACjH,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAC5E,IAAIC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKT,mBAAmB,CAACM,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAC7H,IAAIC,gBAAgB,GAAGA,CAACJ,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKR,0BAA0B,CAACK,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAChI,IAAIE,oBAAoB,GAAGA,CAACL,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKT,mBAAmB,CAACM,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAC7H,IAAIK,gBAAgB,GAAGA,CAACR,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKR,0BAA0B,CAACK,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAChI,IAAIM,cAAc,GAAGnB,cAAc,CAAC,CAACG,iBAAiB,EAAEM,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,CAAC,EAAE,CAACE,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,KAAK;EACzL,IAAIhB,iBAAiB,CAACY,MAAM,EAAE,OAAO,CAAC,EAAE;IACtC,OAAOb,iBAAiB,CAACc,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;EACpD;EACA,OAAOhB,iBAAiB,CAACe,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;AACpD,CAAC,CAAC;AACF,IAAIC,gBAAgB,GAAGA,CAACC,MAAM,EAAEV,QAAQ,EAAEJ,QAAQ,EAAEe,WAAW,EAAEC,YAAY,KAAKA,YAAY;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,8BAA8B,GAAG7B,cAAc,CAAC,CAACM,8BAA8B,EAAEmB,gBAAgB,CAAC,EAAE,CAACK,cAAc,EAAEC,qBAAqB,KAAK;EACjJ,IAAID,cAAc,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,MAAM,IAAIH,qBAAqB,CAACI,OAAO,KAAKF,IAAI,CAACE,OAAO,IAAIJ,qBAAqB,CAACK,IAAI,KAAKH,IAAI,CAACG,IAAI,CAAC,EAAE;IACnJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,OAAOL,qBAAqB;EAC9B;EACA,OAAOM,SAAS;AAClB,CAAC,CAAC;AACF,OAAO,IAAIC,gBAAgB,GAAGtC,cAAc,CAAC,CAACG,iBAAiB,EAAEM,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,EAAEW,8BAA8B,EAAEV,cAAc,EAAEjB,yCAAyC,CAAC,EAAE,CAACkB,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEI,YAAY,EAAEW,QAAQ,EAAEC,IAAI,KAAK;EAC3T,IAAI;IACFC,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGH,IAAI;EACR,IAAIZ,YAAY,IAAI,IAAI,IAAIP,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAID,UAAU,CAACqB,MAAM,KAAK,CAAC,IAAIpB,UAAU,CAACoB,MAAM,KAAK,CAAC,IAAIL,QAAQ,IAAI,IAAI,EAAE;IAChL,OAAOF,SAAS;EAClB;EACA,IAAI;IACFF,OAAO;IACPC;EACF,CAAC,GAAGR,YAAY;EAChB,IAAIiB,aAAa;EACjB,IAAIT,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACQ,MAAM,GAAG,CAAC,EAAE;IACnCC,aAAa,GAAGT,IAAI;EACtB,CAAC,MAAM;IACLS,aAAa,GAAGJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,KAAK,CAACJ,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIE,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOR,SAAS;EAClB;EACA,OAAOpC,iBAAiB,CAAC;IACvBmB,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVW,OAAO;IACPI,QAAQ;IACRM;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}