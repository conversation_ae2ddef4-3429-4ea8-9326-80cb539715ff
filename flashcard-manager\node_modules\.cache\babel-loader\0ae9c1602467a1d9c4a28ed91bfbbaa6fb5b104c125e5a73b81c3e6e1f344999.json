{"ast": null, "code": "import { useEffect, useState } from 'react';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectContainerScale } from '../state/selectors/containerSelectors';\nimport { setScale } from '../state/layoutSlice';\nimport { isWellBehavedNumber } from './isWellBehavedNumber';\nexport function useReportScale() {\n  var dispatch = useAppDispatch();\n  var [ref, setRef] = useState(null);\n  var scale = useAppSelector(selectContainerScale);\n  useEffect(() => {\n    if (ref == null) {\n      return;\n    }\n    var rect = ref.getBoundingClientRect();\n    var newScale = rect.width / ref.offsetWidth;\n    if (isWellBehavedNumber(newScale) && newScale !== scale) {\n      dispatch(setScale(newScale));\n    }\n  }, [ref, dispatch, scale]);\n  return setRef;\n}", "map": {"version": 3, "names": ["useEffect", "useState", "useAppDispatch", "useAppSelector", "selectContainerScale", "setScale", "isWellBehavedNumber", "useReportScale", "dispatch", "ref", "setRef", "scale", "rect", "getBoundingClientRect", "newScale", "width", "offsetWidth"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/util/useReportScale.js"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectContainerScale } from '../state/selectors/containerSelectors';\nimport { setScale } from '../state/layoutSlice';\nimport { isWellBehavedNumber } from './isWellBehavedNumber';\nexport function useReportScale() {\n  var dispatch = useAppDispatch();\n  var [ref, setRef] = useState(null);\n  var scale = useAppSelector(selectContainerScale);\n  useEffect(() => {\n    if (ref == null) {\n      return;\n    }\n    var rect = ref.getBoundingClientRect();\n    var newScale = rect.width / ref.offsetWidth;\n    if (isWellBehavedNumber(newScale) && newScale !== scale) {\n      dispatch(setScale(newScale));\n    }\n  }, [ref, dispatch, scale]);\n  return setRef;\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,IAAIC,QAAQ,GAAGN,cAAc,CAAC,CAAC;EAC/B,IAAI,CAACO,GAAG,EAAEC,MAAM,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAClC,IAAIU,KAAK,GAAGR,cAAc,CAACC,oBAAoB,CAAC;EAChDJ,SAAS,CAAC,MAAM;IACd,IAAIS,GAAG,IAAI,IAAI,EAAE;MACf;IACF;IACA,IAAIG,IAAI,GAAGH,GAAG,CAACI,qBAAqB,CAAC,CAAC;IACtC,IAAIC,QAAQ,GAAGF,IAAI,CAACG,KAAK,GAAGN,GAAG,CAACO,WAAW;IAC3C,IAAIV,mBAAmB,CAACQ,QAAQ,CAAC,IAAIA,QAAQ,KAAKH,KAAK,EAAE;MACvDH,QAAQ,CAACH,QAAQ,CAACS,QAAQ,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAACL,GAAG,EAAED,QAAQ,EAAEG,KAAK,CAAC,CAAC;EAC1B,OAAOD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}