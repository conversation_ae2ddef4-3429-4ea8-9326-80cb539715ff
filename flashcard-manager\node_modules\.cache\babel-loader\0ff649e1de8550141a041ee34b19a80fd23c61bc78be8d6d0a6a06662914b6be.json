{"ast": null, "code": "import { combineReducers, configureStore } from '@reduxjs/toolkit';\nimport { optionsReducer } from './optionsSlice';\nimport { tooltipReducer } from './tooltipSlice';\nimport { chartDataReducer } from './chartDataSlice';\nimport { chartLayoutReducer } from './layoutSlice';\nimport { mouseClickMiddleware, mouseMoveMiddleware } from './mouseEventsMiddleware';\nimport { reduxDevtoolsJsonStringifyReplacer } from './reduxDevtoolsJsonStringifyReplacer';\nimport { cartesianAxisReducer } from './cartesianAxisSlice';\nimport { graphicalItemsReducer } from './graphicalItemsSlice';\nimport { referenceElementsReducer } from './referenceElementsSlice';\nimport { brushReducer } from './brushSlice';\nimport { legendReducer } from './legendSlice';\nimport { rootPropsReducer } from './rootPropsSlice';\nimport { polarAxisReducer } from './polarAxisSlice';\nimport { polarOptionsReducer } from './polarOptionsSlice';\nimport { keyboardEventsMiddleware } from './keyboardEventsMiddleware';\nimport { externalEventsMiddleware } from './externalEventsMiddleware';\nimport { touchEventMiddleware } from './touchEventsMiddleware';\nvar rootReducer = combineReducers({\n  brush: brushReducer,\n  cartesianAxis: cartesianAxisReducer,\n  chartData: chartDataReducer,\n  graphicalItems: graphicalItemsReducer,\n  layout: chartLayoutReducer,\n  legend: legendReducer,\n  options: optionsReducer,\n  polarAxis: polarAxisReducer,\n  polarOptions: polarOptionsReducer,\n  referenceElements: referenceElementsReducer,\n  rootProps: rootPropsReducer,\n  tooltip: tooltipReducer\n});\nexport var createRechartsStore = function createRechartsStore(preloadedState) {\n  var chartName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Chart';\n  return configureStore({\n    reducer: rootReducer,\n    // redux-toolkit v1 types are unhappy with the preloadedState type. Remove the `as any` when bumping to v2\n    preloadedState: preloadedState,\n    // @ts-expect-error redux-toolkit v1 types are unhappy with the middleware array. Remove this comment when bumping to v2\n    middleware: getDefaultMiddleware => getDefaultMiddleware({\n      serializableCheck: false\n    }).concat([mouseClickMiddleware.middleware, mouseMoveMiddleware.middleware, keyboardEventsMiddleware.middleware, externalEventsMiddleware.middleware, touchEventMiddleware.middleware]),\n    devTools: {\n      serialize: {\n        replacer: reduxDevtoolsJsonStringifyReplacer\n      },\n      name: \"recharts-\".concat(chartName)\n    }\n  });\n};", "map": {"version": 3, "names": ["combineReducers", "configureStore", "optionsReducer", "tooltipReducer", "chartDataReducer", "chartLayoutReducer", "mouseClickMiddleware", "mouseMoveMiddleware", "reduxDevtoolsJsonStringifyReplacer", "cartesianAxisReducer", "graphicalItemsReducer", "referenceElementsReducer", "brushReducer", "legendReducer", "rootPropsReducer", "polarAxisReducer", "polarOptionsReducer", "keyboardEventsMiddleware", "externalEventsMiddleware", "touchEventMiddleware", "rootReducer", "brush", "cartesianAxis", "chartData", "graphicalItems", "layout", "legend", "options", "polarAxis", "polarOptions", "referenceElements", "rootProps", "tooltip", "createRechartsStore", "preloadedState", "chartName", "arguments", "length", "undefined", "reducer", "middleware", "getDefaultMiddleware", "serializableCheck", "concat", "devTools", "serialize", "replacer", "name"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/store.js"], "sourcesContent": ["import { combineReducers, configureStore } from '@reduxjs/toolkit';\nimport { optionsReducer } from './optionsSlice';\nimport { tooltipReducer } from './tooltipSlice';\nimport { chartDataReducer } from './chartDataSlice';\nimport { chartLayoutReducer } from './layoutSlice';\nimport { mouseClickMiddleware, mouseMoveMiddleware } from './mouseEventsMiddleware';\nimport { reduxDevtoolsJsonStringifyReplacer } from './reduxDevtoolsJsonStringifyReplacer';\nimport { cartesianAxisReducer } from './cartesianAxisSlice';\nimport { graphicalItemsReducer } from './graphicalItemsSlice';\nimport { referenceElementsReducer } from './referenceElementsSlice';\nimport { brushReducer } from './brushSlice';\nimport { legendReducer } from './legendSlice';\nimport { rootPropsReducer } from './rootPropsSlice';\nimport { polarAxisReducer } from './polarAxisSlice';\nimport { polarOptionsReducer } from './polarOptionsSlice';\nimport { keyboardEventsMiddleware } from './keyboardEventsMiddleware';\nimport { externalEventsMiddleware } from './externalEventsMiddleware';\nimport { touchEventMiddleware } from './touchEventsMiddleware';\nvar rootReducer = combineReducers({\n  brush: brushReducer,\n  cartesianAxis: cartesianAxisReducer,\n  chartData: chartDataReducer,\n  graphicalItems: graphicalItemsReducer,\n  layout: chartLayoutReducer,\n  legend: legendReducer,\n  options: optionsReducer,\n  polarAxis: polarAxisReducer,\n  polarOptions: polarOptionsReducer,\n  referenceElements: referenceElementsReducer,\n  rootProps: rootPropsReducer,\n  tooltip: tooltipReducer\n});\nexport var createRechartsStore = function createRechartsStore(preloadedState) {\n  var chartName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Chart';\n  return configureStore({\n    reducer: rootReducer,\n    // redux-toolkit v1 types are unhappy with the preloadedState type. Remove the `as any` when bumping to v2\n    preloadedState: preloadedState,\n    // @ts-expect-error redux-toolkit v1 types are unhappy with the middleware array. Remove this comment when bumping to v2\n    middleware: getDefaultMiddleware => getDefaultMiddleware({\n      serializableCheck: false\n    }).concat([mouseClickMiddleware.middleware, mouseMoveMiddleware.middleware, keyboardEventsMiddleware.middleware, externalEventsMiddleware.middleware, touchEventMiddleware.middleware]),\n    devTools: {\n      serialize: {\n        replacer: reduxDevtoolsJsonStringifyReplacer\n      },\n      name: \"recharts-\".concat(chartName)\n    }\n  });\n};"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,QAAQ,kBAAkB;AAClE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACnF,SAASC,kCAAkC,QAAQ,sCAAsC;AACzF,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,wBAAwB,QAAQ,0BAA0B;AACnE,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,aAAa,QAAQ,eAAe;AAC7C,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,WAAW,GAAGpB,eAAe,CAAC;EAChCqB,KAAK,EAAET,YAAY;EACnBU,aAAa,EAAEb,oBAAoB;EACnCc,SAAS,EAAEnB,gBAAgB;EAC3BoB,cAAc,EAAEd,qBAAqB;EACrCe,MAAM,EAAEpB,kBAAkB;EAC1BqB,MAAM,EAAEb,aAAa;EACrBc,OAAO,EAAEzB,cAAc;EACvB0B,SAAS,EAAEb,gBAAgB;EAC3Bc,YAAY,EAAEb,mBAAmB;EACjCc,iBAAiB,EAAEnB,wBAAwB;EAC3CoB,SAAS,EAAEjB,gBAAgB;EAC3BkB,OAAO,EAAE7B;AACX,CAAC,CAAC;AACF,OAAO,IAAI8B,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,cAAc,EAAE;EAC5E,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO;EAC3F,OAAOnC,cAAc,CAAC;IACpBsC,OAAO,EAAEnB,WAAW;IACpB;IACAc,cAAc,EAAEA,cAAc;IAC9B;IACAM,UAAU,EAAEC,oBAAoB,IAAIA,oBAAoB,CAAC;MACvDC,iBAAiB,EAAE;IACrB,CAAC,CAAC,CAACC,MAAM,CAAC,CAACrC,oBAAoB,CAACkC,UAAU,EAAEjC,mBAAmB,CAACiC,UAAU,EAAEvB,wBAAwB,CAACuB,UAAU,EAAEtB,wBAAwB,CAACsB,UAAU,EAAErB,oBAAoB,CAACqB,UAAU,CAAC,CAAC;IACvLI,QAAQ,EAAE;MACRC,SAAS,EAAE;QACTC,QAAQ,EAAEtC;MACZ,CAAC;MACDuC,IAAI,EAAE,WAAW,CAACJ,MAAM,CAACR,SAAS;IACpC;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}