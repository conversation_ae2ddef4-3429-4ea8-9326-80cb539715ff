{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isSymbol = require('../predicate/isSymbol.js');\nconst regexIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/;\nconst regexIsPlainProp = /^\\w*$/;\nfunction isKey(value, object) {\n  if (Array.isArray(value)) {\n    return false;\n  }\n  if (typeof value === 'number' || typeof value === 'boolean' || value == null || isSymbol.isSymbol(value)) {\n    return true;\n  }\n  return typeof value === 'string' && (regexIsPlainProp.test(value) || !regexIsDeepProp.test(value)) || object != null && Object.hasOwn(object, value);\n}\nexports.isKey = isKey;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isSymbol", "require", "regexIsDeepProp", "regexIsPlainProp", "is<PERSON>ey", "object", "Array", "isArray", "test", "hasOwn"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/dist/compat/_internal/isKey.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isSymbol = require('../predicate/isSymbol.js');\n\nconst regexIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/;\nconst regexIsPlainProp = /^\\w*$/;\nfunction isKey(value, object) {\n    if (Array.isArray(value)) {\n        return false;\n    }\n    if (typeof value === 'number' || typeof value === 'boolean' || value == null || isSymbol.isSymbol(value)) {\n        return true;\n    }\n    return ((typeof value === 'string' && (regexIsPlainProp.test(value) || !regexIsDeepProp.test(value))) ||\n        (object != null && Object.hasOwn(object, value)));\n}\n\nexports.isKey = isKey;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AAEpD,MAAMC,eAAe,GAAG,kDAAkD;AAC1E,MAAMC,gBAAgB,GAAG,OAAO;AAChC,SAASC,KAAKA,CAACL,KAAK,EAAEM,MAAM,EAAE;EAC1B,IAAIC,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,EAAE;IACtB,OAAO,KAAK;EAChB;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,IAAIA,KAAK,IAAI,IAAI,IAAIC,QAAQ,CAACA,QAAQ,CAACD,KAAK,CAAC,EAAE;IACtG,OAAO,IAAI;EACf;EACA,OAAS,OAAOA,KAAK,KAAK,QAAQ,KAAKI,gBAAgB,CAACK,IAAI,CAACT,KAAK,CAAC,IAAI,CAACG,eAAe,CAACM,IAAI,CAACT,KAAK,CAAC,CAAC,IAC/FM,MAAM,IAAI,IAAI,IAAIX,MAAM,CAACe,MAAM,CAACJ,MAAM,EAAEN,KAAK,CAAE;AACxD;AAEAH,OAAO,CAACQ,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}