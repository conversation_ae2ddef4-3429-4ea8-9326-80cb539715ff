{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isDeepKey = require('../_internal/isDeepKey.js');\nconst isIndex = require('../_internal/isIndex.js');\nconst isArguments = require('../predicate/isArguments.js');\nconst toPath = require('../util/toPath.js');\nfunction has(object, path) {\n  let resolvedPath;\n  if (Array.isArray(path)) {\n    resolvedPath = path;\n  } else if (typeof path === 'string' && isDeepKey.isDeepKey(path) && object?.[path] == null) {\n    resolvedPath = toPath.toPath(path);\n  } else {\n    resolvedPath = [path];\n  }\n  if (resolvedPath.length === 0) {\n    return false;\n  }\n  let current = object;\n  for (let i = 0; i < resolvedPath.length; i++) {\n    const key = resolvedPath[i];\n    if (current == null || !Object.hasOwn(current, key)) {\n      const isSparseIndex = (Array.isArray(current) || isArguments.isArguments(current)) && isIndex.isIndex(key) && key < current.length;\n      if (!isSparseIndex) {\n        return false;\n      }\n    }\n    current = current[key];\n  }\n  return true;\n}\nexports.has = has;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "<PERSON><PERSON><PERSON><PERSON>ey", "require", "isIndex", "isArguments", "to<PERSON><PERSON>", "has", "object", "path", "<PERSON><PERSON><PERSON>", "Array", "isArray", "length", "current", "i", "key", "hasOwn", "isSparseIndex"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/dist/compat/object/has.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isDeepKey = require('../_internal/isDeepKey.js');\nconst isIndex = require('../_internal/isIndex.js');\nconst isArguments = require('../predicate/isArguments.js');\nconst toPath = require('../util/toPath.js');\n\nfunction has(object, path) {\n    let resolvedPath;\n    if (Array.isArray(path)) {\n        resolvedPath = path;\n    }\n    else if (typeof path === 'string' && isDeepKey.isDeepKey(path) && object?.[path] == null) {\n        resolvedPath = toPath.toPath(path);\n    }\n    else {\n        resolvedPath = [path];\n    }\n    if (resolvedPath.length === 0) {\n        return false;\n    }\n    let current = object;\n    for (let i = 0; i < resolvedPath.length; i++) {\n        const key = resolvedPath[i];\n        if (current == null || !Object.hasOwn(current, key)) {\n            const isSparseIndex = (Array.isArray(current) || isArguments.isArguments(current)) && isIndex.isIndex(key) && key < current.length;\n            if (!isSparseIndex) {\n                return false;\n            }\n        }\n        current = current[key];\n    }\n    return true;\n}\n\nexports.has = has;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAMC,OAAO,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAClD,MAAME,WAAW,GAAGF,OAAO,CAAC,6BAA6B,CAAC;AAC1D,MAAMG,MAAM,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAE3C,SAASI,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACvB,IAAIC,YAAY;EAChB,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IACrBC,YAAY,GAAGD,IAAI;EACvB,CAAC,MACI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIP,SAAS,CAACA,SAAS,CAACO,IAAI,CAAC,IAAID,MAAM,GAAGC,IAAI,CAAC,IAAI,IAAI,EAAE;IACtFC,YAAY,GAAGJ,MAAM,CAACA,MAAM,CAACG,IAAI,CAAC;EACtC,CAAC,MACI;IACDC,YAAY,GAAG,CAACD,IAAI,CAAC;EACzB;EACA,IAAIC,YAAY,CAACG,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAO,KAAK;EAChB;EACA,IAAIC,OAAO,GAAGN,MAAM;EACpB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,YAAY,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC1C,MAAMC,GAAG,GAAGN,YAAY,CAACK,CAAC,CAAC;IAC3B,IAAID,OAAO,IAAI,IAAI,IAAI,CAAClB,MAAM,CAACqB,MAAM,CAACH,OAAO,EAAEE,GAAG,CAAC,EAAE;MACjD,MAAME,aAAa,GAAG,CAACP,KAAK,CAACC,OAAO,CAACE,OAAO,CAAC,IAAIT,WAAW,CAACA,WAAW,CAACS,OAAO,CAAC,KAAKV,OAAO,CAACA,OAAO,CAACY,GAAG,CAAC,IAAIA,GAAG,GAAGF,OAAO,CAACD,MAAM;MAClI,IAAI,CAACK,aAAa,EAAE;QAChB,OAAO,KAAK;MAChB;IACJ;IACAJ,OAAO,GAAGA,OAAO,CAACE,GAAG,CAAC;EAC1B;EACA,OAAO,IAAI;AACf;AAEAlB,OAAO,CAACS,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}