{"ast": null, "code": "import none from \"./none.js\";\nexport default function (series) {\n  var peaks = series.map(peak);\n  return none(series).sort(function (a, b) {\n    return peaks[a] - peaks[b];\n  });\n}\nfunction peak(series) {\n  var i = -1,\n    j = 0,\n    n = series.length,\n    vi,\n    vj = -Infinity;\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n  return j;\n}", "map": {"version": 3, "names": ["none", "series", "peaks", "map", "peak", "sort", "a", "b", "i", "j", "n", "length", "vi", "vj", "Infinity"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-shape/src/order/appearance.js"], "sourcesContent": ["import none from \"./none.js\";\n\nexport default function(series) {\n  var peaks = series.map(peak);\n  return none(series).sort(function(a, b) { return peaks[a] - peaks[b]; });\n}\n\nfunction peak(series) {\n  var i = -1, j = 0, n = series.length, vi, vj = -Infinity;\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n  return j;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,KAAK,GAAGD,MAAM,CAACE,GAAG,CAACC,IAAI,CAAC;EAC5B,OAAOJ,IAAI,CAACC,MAAM,CAAC,CAACI,IAAI,CAAC,UAASC,CAAC,EAAEC,CAAC,EAAE;IAAE,OAAOL,KAAK,CAACI,CAAC,CAAC,GAAGJ,KAAK,CAACK,CAAC,CAAC;EAAE,CAAC,CAAC;AAC1E;AAEA,SAASH,IAAIA,CAACH,MAAM,EAAE;EACpB,IAAIO,CAAC,GAAG,CAAC,CAAC;IAAEC,CAAC,GAAG,CAAC;IAAEC,CAAC,GAAGT,MAAM,CAACU,MAAM;IAAEC,EAAE;IAAEC,EAAE,GAAG,CAACC,QAAQ;EACxD,OAAO,EAAEN,CAAC,GAAGE,CAAC,EAAE,IAAI,CAACE,EAAE,GAAG,CAACX,MAAM,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIK,EAAE,EAAEA,EAAE,GAAGD,EAAE,EAAEH,CAAC,GAAGD,CAAC;EAC7D,OAAOC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}