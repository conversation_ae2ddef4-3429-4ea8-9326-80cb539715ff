{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction toArray(value) {\n  return Array.isArray(value) ? value : Array.from(value);\n}\nexports.toArray = toArray;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "toArray", "Array", "isArray", "from"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/dist/compat/_internal/toArray.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction toArray(value) {\n    return Array.isArray(value) ? value : Array.from(value);\n}\n\nexports.toArray = toArray;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,OAAOA,CAACD,KAAK,EAAE;EACpB,OAAOE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAGE,KAAK,CAACE,IAAI,CAACJ,KAAK,CAAC;AAC3D;AAEAH,OAAO,CAACI,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}