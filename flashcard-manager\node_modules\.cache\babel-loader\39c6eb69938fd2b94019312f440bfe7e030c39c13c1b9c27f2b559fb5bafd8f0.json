{"ast": null, "code": "import { utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcTicks, utcTickInterval } from \"d3-time\";\nimport { utcFormat } from \"d3-time-format\";\nimport { calendar } from \"./time.js\";\nimport { initRange } from \"./init.js\";\nexport default function utcTime() {\n  return initRange.apply(calendar(utcTicks, utcTickInterval, utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}", "map": {"version": 3, "names": ["utcYear", "utcMonth", "utcWeek", "utcDay", "utcHour", "utcMinute", "utcSecond", "utcTicks", "utcTickInterval", "utcFormat", "calendar", "initRange", "utcTime", "apply", "domain", "Date", "UTC", "arguments"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-scale/src/utcTime.js"], "sourcesContent": ["import {utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcTicks, utcTickInterval} from \"d3-time\";\nimport {utcFormat} from \"d3-time-format\";\nimport {calendar} from \"./time.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function utcTime() {\n  return initRange.apply(calendar(utcTicks, utcTickInterval, utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}\n"], "mappings": "AAAA,SAAQA,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,eAAe,QAAO,SAAS;AACpH,SAAQC,SAAS,QAAO,gBAAgB;AACxC,SAAQC,QAAQ,QAAO,WAAW;AAClC,SAAQC,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,OAAOA,CAAA,EAAG;EAChC,OAAOD,SAAS,CAACE,KAAK,CAACH,QAAQ,CAACH,QAAQ,EAAEC,eAAe,EAAER,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEG,SAAS,CAAC,CAACK,MAAM,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAED,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC;AAC3M", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}