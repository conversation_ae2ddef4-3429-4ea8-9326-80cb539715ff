{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { computeScatterPoints } from '../../cartesian/Scatter';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectAxisWithScale, selectZAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectZAxis = (state, _xAxisId, _yAxisId, zAxisId) => selectZAxisWithScale(state, 'zAxis', zAxisId, false);\nvar pickScatterSettings = (_state, _xAxisId, _yAxisId, _zAxisId, scatterSettings) => scatterSettings;\nvar pickCells = (_state, _xAxisId, _yAxisId, _zAxisId, _scatterSettings, cells) => cells;\nvar scatterChartDataSelector = (state, xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectChartDataWithIndexesIfNotInPanorama(state, xAxisId, yAxisId, isPanorama);\nvar selectSynchronisedScatterSettings = createSelector([selectUnfilteredCartesianItems, pickScatterSettings], (graphicalItems, scatterSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'scatter' && scatterSettingsFromProps.dataKey === cgis.dataKey && scatterSettingsFromProps.data === cgis.data)) {\n    return scatterSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectScatterPoints = createSelector([scatterChartDataSelector, selectXAxisWithScale, selectXAxisTicks, selectYAxisWithScale, selectYAxisTicks, selectZAxis, selectSynchronisedScatterSettings, pickCells], (_ref, xAxis, xAxisTicks, yAxis, yAxisTicks, zAxis, scatterSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (scatterSettings == null) {\n    return undefined;\n  }\n  var displayedData;\n  if ((scatterSettings === null || scatterSettings === void 0 ? void 0 : scatterSettings.data) != null && scatterSettings.data.length > 0) {\n    displayedData = scatterSettings.data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || (xAxisTicks === null || xAxisTicks === void 0 ? void 0 : xAxisTicks.length) === 0 || (yAxisTicks === null || yAxisTicks === void 0 ? void 0 : yAxisTicks.length) === 0) {\n    return undefined;\n  }\n  return computeScatterPoints({\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  });\n});", "map": {"version": 3, "names": ["createSelector", "computeScatterPoints", "selectChartDataWithIndexesIfNotInPanorama", "selectAxisWithScale", "selectZAxisWithScale", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "selectXAxisWithScale", "state", "xAxisId", "_yAxisId", "_zAxisId", "_scatterSettings", "_cells", "isPanorama", "selectXAxisTicks", "selectYAxisWithScale", "_xAxisId", "yAxisId", "selectYAxisTicks", "selectZAxis", "zAxisId", "pickScatterSettings", "_state", "scatterSettings", "pick<PERSON>ells", "cells", "scatterChartDataSelector", "selectSynchronisedScatterSettings", "graphicalItems", "scatterSettingsFromProps", "some", "cgis", "type", "dataKey", "data", "undefined", "selectScatterPoints", "_ref", "xAxis", "xAxisTicks", "yAxis", "yAxisTicks", "zAxis", "chartData", "dataStartIndex", "dataEndIndex", "displayedData", "length", "slice"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/selectors/scatterSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeScatterPoints } from '../../cartesian/Scatter';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectAxisWithScale, selectZAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectZAxis = (state, _xAxisId, _yAxisId, zAxisId) => selectZAxisWithScale(state, 'zAxis', zAxisId, false);\nvar pickScatterSettings = (_state, _xAxisId, _yAxisId, _zAxisId, scatterSettings) => scatterSettings;\nvar pickCells = (_state, _xAxisId, _yAxisId, _zAxisId, _scatterSettings, cells) => cells;\nvar scatterChartDataSelector = (state, xAxisId, yAxisId, _zAxisId, _scatterSettings, _cells, isPanorama) => selectChartDataWithIndexesIfNotInPanorama(state, xAxisId, yAxisId, isPanorama);\nvar selectSynchronisedScatterSettings = createSelector([selectUnfilteredCartesianItems, pickScatterSettings], (graphicalItems, scatterSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'scatter' && scatterSettingsFromProps.dataKey === cgis.dataKey && scatterSettingsFromProps.data === cgis.data)) {\n    return scatterSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectScatterPoints = createSelector([scatterChartDataSelector, selectXAxisWithScale, selectXAxisTicks, selectYAxisWithScale, selectYAxisTicks, selectZAxis, selectSynchronisedScatterSettings, pickCells], (_ref, xAxis, xAxisTicks, yAxis, yAxisTicks, zAxis, scatterSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (scatterSettings == null) {\n    return undefined;\n  }\n  var displayedData;\n  if ((scatterSettings === null || scatterSettings === void 0 ? void 0 : scatterSettings.data) != null && scatterSettings.data.length > 0) {\n    displayedData = scatterSettings.data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || (xAxisTicks === null || xAxisTicks === void 0 ? void 0 : xAxisTicks.length) === 0 || (yAxisTicks === null || yAxisTicks === void 0 ? void 0 : yAxisTicks.length) === 0) {\n    return undefined;\n  }\n  return computeScatterPoints({\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  });\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,mBAAmB,EAAEC,oBAAoB,EAAEC,0BAA0B,EAAEC,8BAA8B,QAAQ,iBAAiB;AACvI,IAAIC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,UAAU,KAAKX,mBAAmB,CAACK,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEK,UAAU,CAAC;AACjK,IAAIC,gBAAgB,GAAGA,CAACP,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,UAAU,KAAKT,0BAA0B,CAACG,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEK,UAAU,CAAC;AACpK,IAAIE,oBAAoB,GAAGA,CAACR,KAAK,EAAES,QAAQ,EAAEC,OAAO,EAAEP,QAAQ,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,UAAU,KAAKX,mBAAmB,CAACK,KAAK,EAAE,OAAO,EAAEU,OAAO,EAAEJ,UAAU,CAAC;AACjK,IAAIK,gBAAgB,GAAGA,CAACX,KAAK,EAAES,QAAQ,EAAEC,OAAO,EAAEP,QAAQ,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,UAAU,KAAKT,0BAA0B,CAACG,KAAK,EAAE,OAAO,EAAEU,OAAO,EAAEJ,UAAU,CAAC;AACpK,IAAIM,WAAW,GAAGA,CAACZ,KAAK,EAAES,QAAQ,EAAEP,QAAQ,EAAEW,OAAO,KAAKjB,oBAAoB,CAACI,KAAK,EAAE,OAAO,EAAEa,OAAO,EAAE,KAAK,CAAC;AAC9G,IAAIC,mBAAmB,GAAGA,CAACC,MAAM,EAAEN,QAAQ,EAAEP,QAAQ,EAAEC,QAAQ,EAAEa,eAAe,KAAKA,eAAe;AACpG,IAAIC,SAAS,GAAGA,CAACF,MAAM,EAAEN,QAAQ,EAAEP,QAAQ,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEc,KAAK,KAAKA,KAAK;AACxF,IAAIC,wBAAwB,GAAGA,CAACnB,KAAK,EAAEC,OAAO,EAAES,OAAO,EAAEP,QAAQ,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,UAAU,KAAKZ,yCAAyC,CAACM,KAAK,EAAEC,OAAO,EAAES,OAAO,EAAEJ,UAAU,CAAC;AAC1L,IAAIc,iCAAiC,GAAG5B,cAAc,CAAC,CAACM,8BAA8B,EAAEgB,mBAAmB,CAAC,EAAE,CAACO,cAAc,EAAEC,wBAAwB,KAAK;EAC1J,IAAID,cAAc,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,IAAIH,wBAAwB,CAACI,OAAO,KAAKF,IAAI,CAACE,OAAO,IAAIJ,wBAAwB,CAACK,IAAI,KAAKH,IAAI,CAACG,IAAI,CAAC,EAAE;IAC5J,OAAOL,wBAAwB;EACjC;EACA,OAAOM,SAAS;AAClB,CAAC,CAAC;AACF,OAAO,IAAIC,mBAAmB,GAAGrC,cAAc,CAAC,CAAC2B,wBAAwB,EAAEpB,oBAAoB,EAAEQ,gBAAgB,EAAEC,oBAAoB,EAAEG,gBAAgB,EAAEC,WAAW,EAAEQ,iCAAiC,EAAEH,SAAS,CAAC,EAAE,CAACa,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEnB,eAAe,EAAEE,KAAK,KAAK;EACpS,IAAI;IACFkB,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGR,IAAI;EACR,IAAId,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAOY,SAAS;EAClB;EACA,IAAIW,aAAa;EACjB,IAAI,CAACvB,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACW,IAAI,KAAK,IAAI,IAAIX,eAAe,CAACW,IAAI,CAACa,MAAM,GAAG,CAAC,EAAE;IACvID,aAAa,GAAGvB,eAAe,CAACW,IAAI;EACtC,CAAC,MAAM;IACLY,aAAa,GAAGH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,KAAK,CAACJ,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIC,aAAa,IAAI,IAAI,IAAIR,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAID,UAAU,IAAI,IAAI,IAAIE,UAAU,IAAI,IAAI,IAAI,CAACF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACQ,MAAM,MAAM,CAAC,IAAI,CAACN,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACM,MAAM,MAAM,CAAC,EAAE;IACjR,OAAOZ,SAAS;EAClB;EACA,OAAOnC,oBAAoB,CAAC;IAC1B8C,aAAa;IACbR,KAAK;IACLE,KAAK;IACLE,KAAK;IACLnB,eAAe;IACfgB,UAAU;IACVE,UAAU;IACVhB;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}