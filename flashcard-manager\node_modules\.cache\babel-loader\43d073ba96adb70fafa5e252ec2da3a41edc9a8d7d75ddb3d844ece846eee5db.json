{"ast": null, "code": "import { MAX_VALUE_REG, MIN_VALUE_REG } from './ChartUtils';\nimport { isNumber } from './DataUtils';\nimport { isWellBehavedNumber } from './isWellBehavedNumber';\nexport function isWellFormedNumberDomain(v) {\n  if (Array.isArray(v) && v.length === 2) {\n    var [min, max] = v;\n    if (isWellBehavedNumber(min) && isWellBehavedNumber(max)) {\n      return true;\n    }\n  }\n  return false;\n}\nexport function extendDomain(providedDomain, boundaryDomain, allowDataOverflow) {\n  if (allowDataOverflow) {\n    // If the data are allowed to overflow - we're fine with whatever user provided\n    return providedDomain;\n  }\n  /*\n   * If the data are not allowed to overflow - we need to extend the domain.\n   * Means that effectively the user is allowed to make the domain larger\n   * but not smaller.\n   */\n  return [Math.min(providedDomain[0], boundaryDomain[0]), Math.max(providedDomain[1], boundaryDomain[1])];\n}\n\n/**\n * So Recharts allows users to provide their own domains,\n * but it also places some expectations on what the domain is.\n * We can improve on the typescript typing, but we also need a runtime test\n to observe that the user-provided domain is well-formed,\n * that is: an array with exactly two numbers.\n *\n * This function does not accept data as an argument.\n * This is to enable a performance optimization - if the domain is there,\n * and we know what it is without traversing all the data,\n * then we don't have to traverse all the data!\n *\n * If the user-provided domain is not well-formed,\n * this function will return undefined - in which case we should traverse the data to calculate the real domain.\n *\n * This function is for parsing the numerical domain only.\n *\n * @param userDomain external prop, user provided, before validation. Can have various shapes: array, function, special magical strings inside too.\n * @param allowDataOverflow boolean, provided by users. If true then the data domain wins\n *\n * @return [min, max] domain if it's well-formed; undefined if the domain is invalid\n */\nexport function numericalDomainSpecifiedWithoutRequiringData(userDomain, allowDataOverflow) {\n  if (!allowDataOverflow) {\n    // Cannot compute data overflow if the data is not provided\n    return undefined;\n  }\n  if (typeof userDomain === 'function') {\n    // The user function expects the data to be provided as an argument\n    return undefined;\n  }\n  if (Array.isArray(userDomain) && userDomain.length === 2) {\n    var [providedMin, providedMax] = userDomain;\n    var finalMin, finalMax;\n    if (isWellBehavedNumber(providedMin)) {\n      finalMin = providedMin;\n    } else if (typeof providedMin === 'function') {\n      // The user function expects the data to be provided as an argument\n      return undefined;\n    }\n    if (isWellBehavedNumber(providedMax)) {\n      finalMax = providedMax;\n    } else if (typeof providedMax === 'function') {\n      // The user function expects the data to be provided as an argument\n      return undefined;\n    }\n    var candidate = [finalMin, finalMax];\n    if (isWellFormedNumberDomain(candidate)) {\n      return candidate;\n    }\n  }\n  return undefined;\n}\n\n/**\n * So Recharts allows users to provide their own domains,\n * but it also places some expectations on what the domain is.\n * We can improve on the typescript typing, but we also need a runtime test\n * to observe that the user-provided domain is well-formed,\n * that is: an array with exactly two numbers.\n * If the user-provided domain is not well-formed,\n * this function will return undefined - in which case we should traverse the data to calculate the real domain.\n *\n * This function is for parsing the numerical domain only.\n *\n * You are probably thinking, why does domain need tick count?\n * Well it adjusts the domain based on where the \"nice ticks\" land, and nice ticks depend on the tick count.\n *\n * @param userDomain external prop, user provided, before validation. Can have various shapes: array, function, special magical strings inside too.\n * @param dataDomain calculated from data. Can be undefined, as an option for performance optimization\n * @param allowDataOverflow provided by users. If true then the data domain wins\n *\n * @return [min, max] domain if it's well-formed; undefined if the domain is invalid\n */\nexport function parseNumericalUserDomain(userDomain, dataDomain, allowDataOverflow) {\n  if (!allowDataOverflow && dataDomain == null) {\n    // Cannot compute data overflow if the data is not provided\n    return undefined;\n  }\n  if (typeof userDomain === 'function' && dataDomain != null) {\n    try {\n      var result = userDomain(dataDomain, allowDataOverflow);\n      if (isWellFormedNumberDomain(result)) {\n        return extendDomain(result, dataDomain, allowDataOverflow);\n      }\n    } catch (_unused) {\n      /* ignore the exception and compute domain from data later */\n    }\n  }\n  if (Array.isArray(userDomain) && userDomain.length === 2) {\n    var [providedMin, providedMax] = userDomain;\n    var finalMin, finalMax;\n    if (providedMin === 'auto') {\n      if (dataDomain != null) {\n        finalMin = Math.min(...dataDomain);\n      }\n    } else if (isNumber(providedMin)) {\n      finalMin = providedMin;\n    } else if (typeof providedMin === 'function') {\n      try {\n        if (dataDomain != null) {\n          finalMin = providedMin(dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[0]);\n        }\n      } catch (_unused2) {\n        /* ignore the exception and compute domain from data later */\n      }\n    } else if (typeof providedMin === 'string' && MIN_VALUE_REG.test(providedMin)) {\n      var match = MIN_VALUE_REG.exec(providedMin);\n      if (match == null || dataDomain == null) {\n        finalMin = undefined;\n      } else {\n        var value = +match[1];\n        finalMin = dataDomain[0] - value;\n      }\n    } else {\n      finalMin = dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[0];\n    }\n    if (providedMax === 'auto') {\n      if (dataDomain != null) {\n        finalMax = Math.max(...dataDomain);\n      }\n    } else if (isNumber(providedMax)) {\n      finalMax = providedMax;\n    } else if (typeof providedMax === 'function') {\n      try {\n        if (dataDomain != null) {\n          finalMax = providedMax(dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[1]);\n        }\n      } catch (_unused3) {\n        /* ignore the exception and compute domain from data later */\n      }\n    } else if (typeof providedMax === 'string' && MAX_VALUE_REG.test(providedMax)) {\n      var _match = MAX_VALUE_REG.exec(providedMax);\n      if (_match == null || dataDomain == null) {\n        finalMax = undefined;\n      } else {\n        var _value = +_match[1];\n        finalMax = dataDomain[1] + _value;\n      }\n    } else {\n      finalMax = dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[1];\n    }\n    var candidate = [finalMin, finalMax];\n    if (isWellFormedNumberDomain(candidate)) {\n      if (dataDomain == null) {\n        return candidate;\n      }\n      return extendDomain(candidate, dataDomain, allowDataOverflow);\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["MAX_VALUE_REG", "MIN_VALUE_REG", "isNumber", "isWellBehavedNumber", "isWellFormedNumberDomain", "v", "Array", "isArray", "length", "min", "max", "extendDomain", "providedDomain", "boundaryDomain", "allowDataOverflow", "Math", "numericalDomainSpecifiedWithoutRequiringData", "userDomain", "undefined", "providedMin", "providedMax", "finalMin", "finalMax", "candidate", "parseNumericalUserDomain", "dataDomain", "result", "_unused", "_unused2", "test", "match", "exec", "value", "_unused3", "_match", "_value"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/util/isDomainSpecifiedByUser.js"], "sourcesContent": ["import { MAX_VALUE_REG, MIN_VALUE_REG } from './ChartUtils';\nimport { isNumber } from './DataUtils';\nimport { isWellBehavedNumber } from './isWellBehavedNumber';\nexport function isWellFormedNumberDomain(v) {\n  if (Array.isArray(v) && v.length === 2) {\n    var [min, max] = v;\n    if (isWellBehavedNumber(min) && isWellBehavedNumber(max)) {\n      return true;\n    }\n  }\n  return false;\n}\nexport function extendDomain(providedDomain, boundaryDomain, allowDataOverflow) {\n  if (allowDataOverflow) {\n    // If the data are allowed to overflow - we're fine with whatever user provided\n    return providedDomain;\n  }\n  /*\n   * If the data are not allowed to overflow - we need to extend the domain.\n   * Means that effectively the user is allowed to make the domain larger\n   * but not smaller.\n   */\n  return [Math.min(providedDomain[0], boundaryDomain[0]), Math.max(providedDomain[1], boundaryDomain[1])];\n}\n\n/**\n * So Recharts allows users to provide their own domains,\n * but it also places some expectations on what the domain is.\n * We can improve on the typescript typing, but we also need a runtime test\n to observe that the user-provided domain is well-formed,\n * that is: an array with exactly two numbers.\n *\n * This function does not accept data as an argument.\n * This is to enable a performance optimization - if the domain is there,\n * and we know what it is without traversing all the data,\n * then we don't have to traverse all the data!\n *\n * If the user-provided domain is not well-formed,\n * this function will return undefined - in which case we should traverse the data to calculate the real domain.\n *\n * This function is for parsing the numerical domain only.\n *\n * @param userDomain external prop, user provided, before validation. Can have various shapes: array, function, special magical strings inside too.\n * @param allowDataOverflow boolean, provided by users. If true then the data domain wins\n *\n * @return [min, max] domain if it's well-formed; undefined if the domain is invalid\n */\nexport function numericalDomainSpecifiedWithoutRequiringData(userDomain, allowDataOverflow) {\n  if (!allowDataOverflow) {\n    // Cannot compute data overflow if the data is not provided\n    return undefined;\n  }\n  if (typeof userDomain === 'function') {\n    // The user function expects the data to be provided as an argument\n    return undefined;\n  }\n  if (Array.isArray(userDomain) && userDomain.length === 2) {\n    var [providedMin, providedMax] = userDomain;\n    var finalMin, finalMax;\n    if (isWellBehavedNumber(providedMin)) {\n      finalMin = providedMin;\n    } else if (typeof providedMin === 'function') {\n      // The user function expects the data to be provided as an argument\n      return undefined;\n    }\n    if (isWellBehavedNumber(providedMax)) {\n      finalMax = providedMax;\n    } else if (typeof providedMax === 'function') {\n      // The user function expects the data to be provided as an argument\n      return undefined;\n    }\n    var candidate = [finalMin, finalMax];\n    if (isWellFormedNumberDomain(candidate)) {\n      return candidate;\n    }\n  }\n  return undefined;\n}\n\n/**\n * So Recharts allows users to provide their own domains,\n * but it also places some expectations on what the domain is.\n * We can improve on the typescript typing, but we also need a runtime test\n * to observe that the user-provided domain is well-formed,\n * that is: an array with exactly two numbers.\n * If the user-provided domain is not well-formed,\n * this function will return undefined - in which case we should traverse the data to calculate the real domain.\n *\n * This function is for parsing the numerical domain only.\n *\n * You are probably thinking, why does domain need tick count?\n * Well it adjusts the domain based on where the \"nice ticks\" land, and nice ticks depend on the tick count.\n *\n * @param userDomain external prop, user provided, before validation. Can have various shapes: array, function, special magical strings inside too.\n * @param dataDomain calculated from data. Can be undefined, as an option for performance optimization\n * @param allowDataOverflow provided by users. If true then the data domain wins\n *\n * @return [min, max] domain if it's well-formed; undefined if the domain is invalid\n */\nexport function parseNumericalUserDomain(userDomain, dataDomain, allowDataOverflow) {\n  if (!allowDataOverflow && dataDomain == null) {\n    // Cannot compute data overflow if the data is not provided\n    return undefined;\n  }\n  if (typeof userDomain === 'function' && dataDomain != null) {\n    try {\n      var result = userDomain(dataDomain, allowDataOverflow);\n      if (isWellFormedNumberDomain(result)) {\n        return extendDomain(result, dataDomain, allowDataOverflow);\n      }\n    } catch (_unused) {\n      /* ignore the exception and compute domain from data later */\n    }\n  }\n  if (Array.isArray(userDomain) && userDomain.length === 2) {\n    var [providedMin, providedMax] = userDomain;\n    var finalMin, finalMax;\n    if (providedMin === 'auto') {\n      if (dataDomain != null) {\n        finalMin = Math.min(...dataDomain);\n      }\n    } else if (isNumber(providedMin)) {\n      finalMin = providedMin;\n    } else if (typeof providedMin === 'function') {\n      try {\n        if (dataDomain != null) {\n          finalMin = providedMin(dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[0]);\n        }\n      } catch (_unused2) {\n        /* ignore the exception and compute domain from data later */\n      }\n    } else if (typeof providedMin === 'string' && MIN_VALUE_REG.test(providedMin)) {\n      var match = MIN_VALUE_REG.exec(providedMin);\n      if (match == null || dataDomain == null) {\n        finalMin = undefined;\n      } else {\n        var value = +match[1];\n        finalMin = dataDomain[0] - value;\n      }\n    } else {\n      finalMin = dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[0];\n    }\n    if (providedMax === 'auto') {\n      if (dataDomain != null) {\n        finalMax = Math.max(...dataDomain);\n      }\n    } else if (isNumber(providedMax)) {\n      finalMax = providedMax;\n    } else if (typeof providedMax === 'function') {\n      try {\n        if (dataDomain != null) {\n          finalMax = providedMax(dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[1]);\n        }\n      } catch (_unused3) {\n        /* ignore the exception and compute domain from data later */\n      }\n    } else if (typeof providedMax === 'string' && MAX_VALUE_REG.test(providedMax)) {\n      var _match = MAX_VALUE_REG.exec(providedMax);\n      if (_match == null || dataDomain == null) {\n        finalMax = undefined;\n      } else {\n        var _value = +_match[1];\n        finalMax = dataDomain[1] + _value;\n      }\n    } else {\n      finalMax = dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[1];\n    }\n    var candidate = [finalMin, finalMax];\n    if (isWellFormedNumberDomain(candidate)) {\n      if (dataDomain == null) {\n        return candidate;\n      }\n      return extendDomain(candidate, dataDomain, allowDataOverflow);\n    }\n  }\n  return undefined;\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,aAAa,QAAQ,cAAc;AAC3D,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,OAAO,SAASC,wBAAwBA,CAACC,CAAC,EAAE;EAC1C,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAAIA,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IACtC,IAAI,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGL,CAAC;IAClB,IAAIF,mBAAmB,CAACM,GAAG,CAAC,IAAIN,mBAAmB,CAACO,GAAG,CAAC,EAAE;MACxD,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA,OAAO,SAASC,YAAYA,CAACC,cAAc,EAAEC,cAAc,EAAEC,iBAAiB,EAAE;EAC9E,IAAIA,iBAAiB,EAAE;IACrB;IACA,OAAOF,cAAc;EACvB;EACA;AACF;AACA;AACA;AACA;EACE,OAAO,CAACG,IAAI,CAACN,GAAG,CAACG,cAAc,CAAC,CAAC,CAAC,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEE,IAAI,CAACL,GAAG,CAACE,cAAc,CAAC,CAAC,CAAC,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AACzG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,4CAA4CA,CAACC,UAAU,EAAEH,iBAAiB,EAAE;EAC1F,IAAI,CAACA,iBAAiB,EAAE;IACtB;IACA,OAAOI,SAAS;EAClB;EACA,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE;IACpC;IACA,OAAOC,SAAS;EAClB;EACA,IAAIZ,KAAK,CAACC,OAAO,CAACU,UAAU,CAAC,IAAIA,UAAU,CAACT,MAAM,KAAK,CAAC,EAAE;IACxD,IAAI,CAACW,WAAW,EAAEC,WAAW,CAAC,GAAGH,UAAU;IAC3C,IAAII,QAAQ,EAAEC,QAAQ;IACtB,IAAInB,mBAAmB,CAACgB,WAAW,CAAC,EAAE;MACpCE,QAAQ,GAAGF,WAAW;IACxB,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;MAC5C;MACA,OAAOD,SAAS;IAClB;IACA,IAAIf,mBAAmB,CAACiB,WAAW,CAAC,EAAE;MACpCE,QAAQ,GAAGF,WAAW;IACxB,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;MAC5C;MACA,OAAOF,SAAS;IAClB;IACA,IAAIK,SAAS,GAAG,CAACF,QAAQ,EAAEC,QAAQ,CAAC;IACpC,IAAIlB,wBAAwB,CAACmB,SAAS,CAAC,EAAE;MACvC,OAAOA,SAAS;IAClB;EACF;EACA,OAAOL,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,wBAAwBA,CAACP,UAAU,EAAEQ,UAAU,EAAEX,iBAAiB,EAAE;EAClF,IAAI,CAACA,iBAAiB,IAAIW,UAAU,IAAI,IAAI,EAAE;IAC5C;IACA,OAAOP,SAAS;EAClB;EACA,IAAI,OAAOD,UAAU,KAAK,UAAU,IAAIQ,UAAU,IAAI,IAAI,EAAE;IAC1D,IAAI;MACF,IAAIC,MAAM,GAAGT,UAAU,CAACQ,UAAU,EAAEX,iBAAiB,CAAC;MACtD,IAAIV,wBAAwB,CAACsB,MAAM,CAAC,EAAE;QACpC,OAAOf,YAAY,CAACe,MAAM,EAAED,UAAU,EAAEX,iBAAiB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOa,OAAO,EAAE;MAChB;IAAA;EAEJ;EACA,IAAIrB,KAAK,CAACC,OAAO,CAACU,UAAU,CAAC,IAAIA,UAAU,CAACT,MAAM,KAAK,CAAC,EAAE;IACxD,IAAI,CAACW,WAAW,EAAEC,WAAW,CAAC,GAAGH,UAAU;IAC3C,IAAII,QAAQ,EAAEC,QAAQ;IACtB,IAAIH,WAAW,KAAK,MAAM,EAAE;MAC1B,IAAIM,UAAU,IAAI,IAAI,EAAE;QACtBJ,QAAQ,GAAGN,IAAI,CAACN,GAAG,CAAC,GAAGgB,UAAU,CAAC;MACpC;IACF,CAAC,MAAM,IAAIvB,QAAQ,CAACiB,WAAW,CAAC,EAAE;MAChCE,QAAQ,GAAGF,WAAW;IACxB,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;MAC5C,IAAI;QACF,IAAIM,UAAU,IAAI,IAAI,EAAE;UACtBJ,QAAQ,GAAGF,WAAW,CAACM,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/F;MACF,CAAC,CAAC,OAAOG,QAAQ,EAAE;QACjB;MAAA;IAEJ,CAAC,MAAM,IAAI,OAAOT,WAAW,KAAK,QAAQ,IAAIlB,aAAa,CAAC4B,IAAI,CAACV,WAAW,CAAC,EAAE;MAC7E,IAAIW,KAAK,GAAG7B,aAAa,CAAC8B,IAAI,CAACZ,WAAW,CAAC;MAC3C,IAAIW,KAAK,IAAI,IAAI,IAAIL,UAAU,IAAI,IAAI,EAAE;QACvCJ,QAAQ,GAAGH,SAAS;MACtB,CAAC,MAAM;QACL,IAAIc,KAAK,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC;QACrBT,QAAQ,GAAGI,UAAU,CAAC,CAAC,CAAC,GAAGO,KAAK;MAClC;IACF,CAAC,MAAM;MACLX,QAAQ,GAAGI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IAClF;IACA,IAAIL,WAAW,KAAK,MAAM,EAAE;MAC1B,IAAIK,UAAU,IAAI,IAAI,EAAE;QACtBH,QAAQ,GAAGP,IAAI,CAACL,GAAG,CAAC,GAAGe,UAAU,CAAC;MACpC;IACF,CAAC,MAAM,IAAIvB,QAAQ,CAACkB,WAAW,CAAC,EAAE;MAChCE,QAAQ,GAAGF,WAAW;IACxB,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;MAC5C,IAAI;QACF,IAAIK,UAAU,IAAI,IAAI,EAAE;UACtBH,QAAQ,GAAGF,WAAW,CAACK,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/F;MACF,CAAC,CAAC,OAAOQ,QAAQ,EAAE;QACjB;MAAA;IAEJ,CAAC,MAAM,IAAI,OAAOb,WAAW,KAAK,QAAQ,IAAIpB,aAAa,CAAC6B,IAAI,CAACT,WAAW,CAAC,EAAE;MAC7E,IAAIc,MAAM,GAAGlC,aAAa,CAAC+B,IAAI,CAACX,WAAW,CAAC;MAC5C,IAAIc,MAAM,IAAI,IAAI,IAAIT,UAAU,IAAI,IAAI,EAAE;QACxCH,QAAQ,GAAGJ,SAAS;MACtB,CAAC,MAAM;QACL,IAAIiB,MAAM,GAAG,CAACD,MAAM,CAAC,CAAC,CAAC;QACvBZ,QAAQ,GAAGG,UAAU,CAAC,CAAC,CAAC,GAAGU,MAAM;MACnC;IACF,CAAC,MAAM;MACLb,QAAQ,GAAGG,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IAClF;IACA,IAAIF,SAAS,GAAG,CAACF,QAAQ,EAAEC,QAAQ,CAAC;IACpC,IAAIlB,wBAAwB,CAACmB,SAAS,CAAC,EAAE;MACvC,IAAIE,UAAU,IAAI,IAAI,EAAE;QACtB,OAAOF,SAAS;MAClB;MACA,OAAOZ,YAAY,CAACY,SAAS,EAAEE,UAAU,EAAEX,iBAAiB,CAAC;IAC/D;EACF;EACA,OAAOI,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}