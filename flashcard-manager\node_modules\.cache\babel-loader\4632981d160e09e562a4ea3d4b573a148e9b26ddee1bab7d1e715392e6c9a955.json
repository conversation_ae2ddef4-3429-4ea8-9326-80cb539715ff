{"ast": null, "code": "function Natural(context) {\n  this._context = context;\n}\nNatural.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function () {\n    var x = this._x,\n      y = this._y,\n      n = x.length;\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n          py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n    if (this._line || this._line !== 0 && n === 1) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function (x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n    n = x.length - 1,\n    m,\n    a = new Array(n),\n    b = new Array(n),\n    r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\nexport default function (context) {\n  return new Natural(context);\n}", "map": {"version": 3, "names": ["Natural", "context", "_context", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_x", "_y", "lineEnd", "x", "y", "n", "length", "lineTo", "moveTo", "px", "controlPoints", "py", "i0", "i1", "bezierCurveTo", "closePath", "point", "push", "i", "m", "a", "Array", "b", "r"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-shape/src/curve/natural.js"], "sourcesContent": ["function Natural(context) {\n  this._context = context;\n}\n\nNatural.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        n = x.length;\n\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n            py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n\n    if (this._line || (this._line !== 0 && n === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n      n = x.length - 1,\n      m,\n      a = new Array(n),\n      b = new Array(n),\n      r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\n\nexport default function(context) {\n  return new Natural(context);\n}\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,OAAO,EAAE;EACxB,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,OAAO,CAACG,SAAS,GAAG;EAClBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,EAAE,GAAG,EAAE;EACd,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAIC,CAAC,GAAG,IAAI,CAACH,EAAE;MACXI,CAAC,GAAG,IAAI,CAACH,EAAE;MACXI,CAAC,GAAGF,CAAC,CAACG,MAAM;IAEhB,IAAID,CAAC,EAAE;MACL,IAAI,CAACT,KAAK,GAAG,IAAI,CAACH,QAAQ,CAACc,MAAM,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACX,QAAQ,CAACe,MAAM,CAACL,CAAC,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF,IAAIC,CAAC,KAAK,CAAC,EAAE;QACX,IAAI,CAACZ,QAAQ,CAACc,MAAM,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM;QACL,IAAIK,EAAE,GAAGC,aAAa,CAACP,CAAC,CAAC;UACrBQ,EAAE,GAAGD,aAAa,CAACN,CAAC,CAAC;QACzB,KAAK,IAAIQ,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGR,CAAC,EAAE,EAAEO,EAAE,EAAE,EAAEC,EAAE,EAAE;UAC3C,IAAI,CAACpB,QAAQ,CAACqB,aAAa,CAACL,EAAE,CAAC,CAAC,CAAC,CAACG,EAAE,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,CAACC,EAAE,CAAC,EAAEH,EAAE,CAAC,CAAC,CAAC,CAACG,EAAE,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,CAACC,EAAE,CAAC,EAAET,CAAC,CAACU,EAAE,CAAC,EAAET,CAAC,CAACS,EAAE,CAAC,CAAC;QACvF;MACF;IACF;IAEA,IAAI,IAAI,CAACjB,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAIS,CAAC,KAAK,CAAE,EAAE,IAAI,CAACZ,QAAQ,CAACsB,SAAS,CAAC,CAAC;IAC1E,IAAI,CAACnB,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;IAC3B,IAAI,CAACI,EAAE,GAAG,IAAI,CAACC,EAAE,GAAG,IAAI;EAC1B,CAAC;EACDe,KAAK,EAAE,SAAAA,CAASb,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAACJ,EAAE,CAACiB,IAAI,CAAC,CAACd,CAAC,CAAC;IAChB,IAAI,CAACF,EAAE,CAACgB,IAAI,CAAC,CAACb,CAAC,CAAC;EAClB;AACF,CAAC;;AAED;AACA,SAASM,aAAaA,CAACP,CAAC,EAAE;EACxB,IAAIe,CAAC;IACDb,CAAC,GAAGF,CAAC,CAACG,MAAM,GAAG,CAAC;IAChBa,CAAC;IACDC,CAAC,GAAG,IAAIC,KAAK,CAAChB,CAAC,CAAC;IAChBiB,CAAC,GAAG,IAAID,KAAK,CAAChB,CAAC,CAAC;IAChBkB,CAAC,GAAG,IAAIF,KAAK,CAAChB,CAAC,CAAC;EACpBe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,GAAGpB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;EAC1C,KAAKe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,CAAC,GAAG,CAAC,EAAE,EAAEa,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,GAAG,CAAC,EAAEI,CAAC,CAACJ,CAAC,CAAC,GAAG,CAAC,EAAEK,CAAC,CAACL,CAAC,CAAC,GAAG,CAAC,GAAGf,CAAC,CAACe,CAAC,CAAC,GAAG,CAAC,GAAGf,CAAC,CAACe,CAAC,GAAG,CAAC,CAAC;EAC9EE,CAAC,CAACf,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEkB,CAAC,CAAClB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC;EAC1D,KAAKa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,CAAC,EAAE,EAAEa,CAAC,EAAEC,CAAC,GAAGC,CAAC,CAACF,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,GAAG,CAAC,CAAC,EAAEI,CAAC,CAACJ,CAAC,CAAC,IAAIC,CAAC,EAAEI,CAAC,CAACL,CAAC,CAAC,IAAIC,CAAC,GAAGI,CAAC,CAACL,CAAC,GAAG,CAAC,CAAC;EAC5EE,CAAC,CAACf,CAAC,GAAG,CAAC,CAAC,GAAGkB,CAAC,CAAClB,CAAC,GAAG,CAAC,CAAC,GAAGiB,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC;EAC9B,KAAKa,CAAC,GAAGb,CAAC,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,GAAG,CAACK,CAAC,CAACL,CAAC,CAAC,GAAGE,CAAC,CAACF,CAAC,GAAG,CAAC,CAAC,IAAII,CAAC,CAACJ,CAAC,CAAC;EAC5DI,CAAC,CAACjB,CAAC,GAAG,CAAC,CAAC,GAAG,CAACF,CAAC,CAACE,CAAC,CAAC,GAAGe,CAAC,CAACf,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EAChC,KAAKa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,CAAC,GAAG,CAAC,EAAE,EAAEa,CAAC,EAAEI,CAAC,CAACJ,CAAC,CAAC,GAAG,CAAC,GAAGf,CAAC,CAACe,CAAC,GAAG,CAAC,CAAC,GAAGE,CAAC,CAACF,CAAC,GAAG,CAAC,CAAC;EAC1D,OAAO,CAACE,CAAC,EAAEE,CAAC,CAAC;AACf;AAEA,eAAe,UAAS9B,OAAO,EAAE;EAC/B,OAAO,IAAID,OAAO,CAACC,OAAO,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}