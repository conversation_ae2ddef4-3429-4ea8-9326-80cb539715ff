{"ast": null, "code": "import none from \"./none.js\";\nexport default function (series) {\n  return none(series).reverse();\n}", "map": {"version": 3, "names": ["none", "series", "reverse"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-shape/src/order/reverse.js"], "sourcesContent": ["import none from \"./none.js\";\n\nexport default function(series) {\n  return none(series).reverse();\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,MAAM,EAAE;EAC9B,OAAOD,IAAI,CAACC,MAAM,CAAC,CAACC,OAAO,CAAC,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}