{"ast": null, "code": "import { Basis } from \"./basis.js\";\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\nBundle.prototype = {\n  lineStart: function () {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function () {\n    var x = this._x,\n      y = this._y,\n      j = x.length - 1;\n    if (j > 0) {\n      var x0 = x[0],\n        y0 = y[0],\n        dx = x[j] - x0,\n        dy = y[j] - y0,\n        i = -1,\n        t;\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(this._beta * x[i] + (1 - this._beta) * (x0 + t * dx), this._beta * y[i] + (1 - this._beta) * (y0 + t * dy));\n      }\n    }\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function (x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\nexport default (function custom(beta) {\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n  bundle.beta = function (beta) {\n    return custom(+beta);\n  };\n  return bundle;\n})(0.85);", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Bundle", "context", "beta", "_basis", "_beta", "prototype", "lineStart", "_x", "_y", "lineEnd", "x", "y", "j", "length", "x0", "y0", "dx", "dy", "i", "t", "point", "push", "custom", "bundle"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-shape/src/curve/bundle.js"], "sourcesContent": ["import {Basis} from \"./basis.js\";\n\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\n\nBundle.prototype = {\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        j = x.length - 1;\n\n    if (j > 0) {\n      var x0 = x[0],\n          y0 = y[0],\n          dx = x[j] - x0,\n          dy = y[j] - y0,\n          i = -1,\n          t;\n\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(\n          this._beta * x[i] + (1 - this._beta) * (x0 + t * dx),\n          this._beta * y[i] + (1 - this._beta) * (y0 + t * dy)\n        );\n      }\n    }\n\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\nexport default (function custom(beta) {\n\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n\n  bundle.beta = function(beta) {\n    return custom(+beta);\n  };\n\n  return bundle;\n})(0.85);\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAEhC,SAASC,MAAMA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC7B,IAAI,CAACC,MAAM,GAAG,IAAIJ,KAAK,CAACE,OAAO,CAAC;EAChC,IAAI,CAACG,KAAK,GAAGF,IAAI;AACnB;AAEAF,MAAM,CAACK,SAAS,GAAG;EACjBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACL,MAAM,CAACG,SAAS,CAAC,CAAC;EACzB,CAAC;EACDG,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAIC,CAAC,GAAG,IAAI,CAACH,EAAE;MACXI,CAAC,GAAG,IAAI,CAACH,EAAE;MACXI,CAAC,GAAGF,CAAC,CAACG,MAAM,GAAG,CAAC;IAEpB,IAAID,CAAC,GAAG,CAAC,EAAE;MACT,IAAIE,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;QACTK,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;QACTK,EAAE,GAAGN,CAAC,CAACE,CAAC,CAAC,GAAGE,EAAE;QACdG,EAAE,GAAGN,CAAC,CAACC,CAAC,CAAC,GAAGG,EAAE;QACdG,CAAC,GAAG,CAAC,CAAC;QACNC,CAAC;MAEL,OAAO,EAAED,CAAC,IAAIN,CAAC,EAAE;QACfO,CAAC,GAAGD,CAAC,GAAGN,CAAC;QACT,IAAI,CAACT,MAAM,CAACiB,KAAK,CACf,IAAI,CAAChB,KAAK,GAAGM,CAAC,CAACQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACd,KAAK,KAAKU,EAAE,GAAGK,CAAC,GAAGH,EAAE,CAAC,EACpD,IAAI,CAACZ,KAAK,GAAGO,CAAC,CAACO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACd,KAAK,KAAKW,EAAE,GAAGI,CAAC,GAAGF,EAAE,CACrD,CAAC;MACH;IACF;IAEA,IAAI,CAACV,EAAE,GAAG,IAAI,CAACC,EAAE,GAAG,IAAI;IACxB,IAAI,CAACL,MAAM,CAACM,OAAO,CAAC,CAAC;EACvB,CAAC;EACDW,KAAK,EAAE,SAAAA,CAASV,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAACJ,EAAE,CAACc,IAAI,CAAC,CAACX,CAAC,CAAC;IAChB,IAAI,CAACF,EAAE,CAACa,IAAI,CAAC,CAACV,CAAC,CAAC;EAClB;AACF,CAAC;AAED,eAAe,CAAC,SAASW,MAAMA,CAACpB,IAAI,EAAE;EAEpC,SAASqB,MAAMA,CAACtB,OAAO,EAAE;IACvB,OAAOC,IAAI,KAAK,CAAC,GAAG,IAAIH,KAAK,CAACE,OAAO,CAAC,GAAG,IAAID,MAAM,CAACC,OAAO,EAAEC,IAAI,CAAC;EACpE;EAEAqB,MAAM,CAACrB,IAAI,GAAG,UAASA,IAAI,EAAE;IAC3B,OAAOoB,MAAM,CAAC,CAACpB,IAAI,CAAC;EACtB,CAAC;EAED,OAAOqB,MAAM;AACf,CAAC,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}