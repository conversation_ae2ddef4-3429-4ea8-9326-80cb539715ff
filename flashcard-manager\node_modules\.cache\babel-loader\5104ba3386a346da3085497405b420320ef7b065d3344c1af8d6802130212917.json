{"ast": null, "code": "var parseIsSsrByDefault = () => !(typeof window !== 'undefined' && window.document && Boolean(window.document.createElement) && window.setTimeout);\nexport var Global = {\n  isSsr: parseIsSsrByDefault()\n};", "map": {"version": 3, "names": ["parseIsSsrByDefault", "window", "document", "Boolean", "createElement", "setTimeout", "Global", "isSsr"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/util/Global.js"], "sourcesContent": ["var parseIsSsrByDefault = () => !(typeof window !== 'undefined' && window.document && Boolean(window.document.createElement) && window.setTimeout);\nexport var Global = {\n  isSsr: parseIsSsrByDefault()\n};"], "mappings": "AAAA,IAAIA,mBAAmB,GAAGA,CAAA,KAAM,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAIC,OAAO,CAACF,MAAM,CAACC,QAAQ,CAACE,aAAa,CAAC,IAAIH,MAAM,CAACI,UAAU,CAAC;AAClJ,OAAO,IAAIC,MAAM,GAAG;EAClBC,KAAK,EAAEP,mBAAmB,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}