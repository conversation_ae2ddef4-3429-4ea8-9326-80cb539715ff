{"ast": null, "code": "// src/index.ts\nexport * from \"redux\";\nimport { produce, current as current3, freeze, original as original2, isDraft as isDraft5 } from \"immer\";\nimport { createSelector, createSelectorCreator as createSelectorCreator2, lruMemoize, weakMapMemoize as weakMapMemoize2 } from \"reselect\";\n\n// src/createDraftSafeSelector.ts\nimport { current, isDraft } from \"immer\";\nimport { createSelectorCreator, weakMapMemoize } from \"reselect\";\nvar createDraftSafeSelectorCreator = (...args) => {\n  const createSelector2 = createSelectorCreator(...args);\n  const createDraftSafeSelector2 = Object.assign((...args2) => {\n    const selector = createSelector2(...args2);\n    const wrappedSelector = (value, ...rest) => selector(isDraft(value) ? current(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector;\n  }, {\n    withTypes: () => createDraftSafeSelector2\n  });\n  return createDraftSafeSelector2;\n};\nvar createDraftSafeSelector = /* @__PURE__ */createDraftSafeSelectorCreator(weakMapMemoize);\n\n// src/configureStore.ts\nimport { applyMiddleware, createStore, compose as compose2, combineReducers, isPlainObject as isPlainObject2 } from \"redux\";\n\n// src/devtoolsExtension.ts\nimport { compose } from \"redux\";\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return compose;\n  return compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop3) {\n    return noop3;\n  };\n};\n\n// src/getDefaultMiddleware.ts\nimport { thunk as thunkMiddleware, withExtraArgument } from \"redux-thunk\";\n\n// src/createAction.ts\nimport { isAction } from \"redux\";\n\n// src/tsHelpers.ts\nvar hasMatchFunction = v => {\n  return v && typeof v.match === \"function\";\n};\n\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator(...args) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : \"prepareAction did not return an object\");\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...(\"meta\" in prepared && {\n          meta: prepared.meta\n        }),\n        ...(\"error\" in prepared && {\n          error: prepared.error\n        })\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = action => isAction(action) && action.type === type;\n  return actionCreator;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action &&\n  // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return isAction(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\n\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  const splitType = type ? `${type}`.split(\"/\") : [];\n  const actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return `Detected an action creator with type \"${type || \"unknown\"}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nfunction createActionCreatorInvariantMiddleware(options = {}) {\n  if (process.env.NODE_ENV === \"production\") {\n    return () => next => action => next(action);\n  }\n  const {\n    isActionCreator: isActionCreator2 = isActionCreator\n  } = options;\n  return () => next => action => {\n    if (isActionCreator2(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}\n\n// src/utils.ts\nimport { produce as createNextState, isDraftable } from \"immer\";\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  let elapsed = 0;\n  return {\n    measureTime(fn) {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nvar Tuple = class _Tuple extends Array {\n  constructor(...items) {\n    super(...items);\n    Object.setPrototypeOf(this, _Tuple.prototype);\n  }\n  static get [Symbol.species]() {\n    return _Tuple;\n  }\n  concat(...arr) {\n    return super.concat.apply(this, arr);\n  }\n  prepend(...arr) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new _Tuple(...arr[0].concat(this));\n    }\n    return new _Tuple(...arr.concat(this));\n  }\n};\nfunction freezeDraftable(val) {\n  return isDraftable(val) ? createNextState(val, () => {}) : val;\n}\nfunction getOrInsertComputed(map, key, compute) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, compute(key)).get(key);\n}\n\n// src/immutableStateInvariantMiddleware.ts\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable, ignorePaths = [], obj, path = \"\", checkedObjects = /* @__PURE__ */new Set()) {\n  const tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable, ignoredPaths = [], trackedProperty, obj, sameParentRef = false, path = \"\") {\n  const prevObj = trackedProperty ? trackedProperty.value : void 0;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  const keysToDetect = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware(options = {}) {\n  if (process.env.NODE_ENV === \"production\") {\n    return () => next => action => next(action);\n  } else {\n    let stringify2 = function (obj, serializer, indent, decycler) {\n        return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);\n      },\n      getSerialize2 = function (serializer, decycler) {\n        let stack = [],\n          keys = [];\n        if (!decycler) decycler = function (_, value) {\n          if (stack[0] === value) return \"[Circular ~]\";\n          return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n        };\n        return function (key, value) {\n          if (stack.length > 0) {\n            var thisPos = stack.indexOf(this);\n            ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n            ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n            if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n          } else stack.push(value);\n          return serializer == null ? value : serializer.call(this, key, value);\n        };\n      };\n    var stringify = stringify2,\n      getSerialize = getSerialize2;\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return next => action => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(19) : `A state mutation was detected between dispatches, in the path '${result.path || \"\"}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(20) : `A state mutation was detected inside a dispatch, in the path: ${result.path || \"\"}. Take a look at the reducer(s) handling the action ${stringify2(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}\n\n// src/serializableStateInvariantMiddleware.ts\nimport { isAction as isAction2, isPlainObject } from \"redux\";\nfunction isPlain(val) {\n  const type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || isPlainObject(val);\n}\nfunction findNonSerializableValue(value, path = \"\", isSerializable = isPlain, getEntries, ignoredPaths = [], cache) {\n  let foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware(options = {}) {\n  if (process.env.NODE_ENV === \"production\") {\n    return () => next => action => next(action);\n  } else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = [\"meta.arg\", \"meta.baseQueryMeta\"],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache = !disableCache && WeakSet ? /* @__PURE__ */new WeakSet() : void 0;\n    return storeAPI => next => action => {\n      if (!isAction2(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}\n\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nvar buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== \"production\") {\n    if (immutableCheck) {\n      let immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      let serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n};\n\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = () => payload => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nvar createQueueWithTimer = timeout => {\n  return notify => {\n    setTimeout(notify, timeout);\n  };\n};\nvar autoBatchEnhancer = (options = {\n  type: \"raf\"\n}) => next => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = /* @__PURE__ */new Set();\n  const queueCallback = options.type === \"tick\" ? queueMicrotask : options.type === \"raf\" ?\n  // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n  typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10) : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach(l => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener2) {\n      const wrappedListener = () => notifying && listener2();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener2);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener2);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action) {\n      try {\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        return store.dispatch(action);\n      } finally {\n        notifying = true;\n      }\n    }\n  });\n};\n\n// src/getDefaultEnhancers.ts\nvar buildGetDefaultEnhancers = middlewareEnhancer => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === \"object\" ? autoBatch : void 0));\n  }\n  return enhancerArray;\n};\n\n// src/configureStore.ts\nfunction configureStore(options) {\n  const getDefaultMiddleware = buildGetDefaultMiddleware();\n  const {\n    reducer = void 0,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = void 0,\n    enhancers = void 0\n  } = options || {};\n  let rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if (isPlainObject2(reducer)) {\n    rootReducer = combineReducers(reducer);\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && middleware && typeof middleware !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"`middleware` field must be a callback\");\n  }\n  let finalMiddleware;\n  if (typeof middleware === \"function\") {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (process.env.NODE_ENV !== \"production\" && !Array.isArray(finalMiddleware)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : \"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (process.env.NODE_ENV !== \"production\" && finalMiddleware.some(item => typeof item !== \"function\")) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"each middleware provided to configureStore must be a function\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && duplicateMiddlewareCheck) {\n    let middlewareReferences = /* @__PURE__ */new Set();\n    finalMiddleware.forEach(middleware2 => {\n      if (middlewareReferences.has(middleware2)) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(42) : \"Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.\");\n      }\n      middlewareReferences.add(middleware2);\n    });\n  }\n  let finalCompose = compose2;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: process.env.NODE_ENV !== \"production\",\n      ...(typeof devTools === \"object\" && devTools)\n    });\n  }\n  const middlewareEnhancer = applyMiddleware(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);\n  if (process.env.NODE_ENV !== \"production\" && enhancers && typeof enhancers !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : \"`enhancers` field must be a callback\");\n  }\n  let storeEnhancers = typeof enhancers === \"function\" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (process.env.NODE_ENV !== \"production\" && !Array.isArray(storeEnhancers)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : \"`enhancers` callback must return an array\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && storeEnhancers.some(item => typeof item !== \"function\")) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"each enhancer provided to configureStore must be a function\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error(\"middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`\");\n  }\n  const composedEnhancer = finalCompose(...storeEnhancers);\n  return createStore(rootReducer, preloadedState, composedEnhancer);\n}\n\n// src/createReducer.ts\nimport { produce as createNextState2, isDraft as isDraft2, isDraftable as isDraftable2 } from \"immer\";\n\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  const actionsMap = {};\n  const actionMatchers = [];\n  let defaultCaseReducer;\n  const builder = {\n    addCase(typeOrActionCreator, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (actionMatchers.length > 0) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(26) : \"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(27) : \"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(28) : \"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(29) : `\\`builder.addCase\\` cannot be called with two reducers for the same action type '${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher(matcher, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(30) : \"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(31) : \"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nfunction createReducer(initialState, mapOrBuilderCallback) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n  let getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action) {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer: reducer2\n    }) => reducer2)];\n    if (caseReducers.filter(cr => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer) => {\n      if (caseReducer) {\n        if (isDraft2(previousState)) {\n          const draft = previousState;\n          const result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!isDraftable2(previousState)) {\n          const result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return createNextState2(previousState, draft => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n\n// src/matchers.ts\nvar matches = (matcher, action) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf(...matchers) {\n  return action => {\n    return matchers.some(matcher => matches(matcher, action));\n  };\n}\nfunction isAllOf(...matchers) {\n  return action => {\n    return matchers.every(matcher => matches(matcher, action));\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === \"string\";\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"pending\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.pending));\n}\nfunction isRejected(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.rejected));\n}\nfunction isRejectedWithValue(...asyncThunks) {\n  const hasFlag = action => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nfunction isFulfilled(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.fulfilled));\n}\nfunction isAsyncThunkAction(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return action => hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap(asyncThunk => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}\n\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = (size = 21) => {\n  let id = \"\";\n  let i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar FulfillWithMeta = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar miniSerializeError = value => {\n  if (typeof value === \"object\" && value !== null) {\n    const simpleError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar externalAbortMessage = \"External signal was aborted\";\nvar createAsyncThunk = /* @__PURE__ */(() => {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    const fulfilled = createAction(typePrefix + \"/fulfilled\", (payload, requestId, arg, meta) => ({\n      payload,\n      meta: {\n        ...(meta || {}),\n        arg,\n        requestId,\n        requestStatus: \"fulfilled\"\n      }\n    }));\n    const pending = createAction(typePrefix + \"/pending\", (requestId, arg, meta) => ({\n      payload: void 0,\n      meta: {\n        ...(meta || {}),\n        arg,\n        requestId,\n        requestStatus: \"pending\"\n      }\n    }));\n    const rejected = createAction(typePrefix + \"/rejected\", (error, requestId, arg, payload, meta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n      meta: {\n        ...(meta || {}),\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: \"rejected\",\n        aborted: error?.name === \"AbortError\",\n        condition: error?.name === \"ConditionError\"\n      }\n    }));\n    function actionCreator(arg, {\n      signal\n    } = {}) {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler;\n        let abortReason;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener(\"abort\", () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function () {\n          let finalAction;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              throw {\n                name: \"ConditionError\",\n                message: \"Aborted due to condition callback returning false.\"\n              };\n            }\n            const abortedPromise = new Promise((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: \"AbortError\",\n                  message: abortReason || \"Aborted\"\n                });\n              };\n              abortController.signal.addEventListener(\"abort\", abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })));\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: (value, meta) => {\n                return new RejectWithValue(value, meta);\n              },\n              fulfillWithValue: (value, meta) => {\n                return new FulfillWithMeta(value, meta);\n              }\n            })).then(result => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener(\"abort\", abortHandler);\n            }\n          }\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = () => createAsyncThunk2;\n  return createAsyncThunk2;\n})();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n\n// src/createSlice.ts\nvar asyncThunkSymbol = /* @__PURE__ */Symbol.for(\"rtk-slice-createasyncthunk\");\nvar asyncThunkCreator = {\n  [asyncThunkSymbol]: createAsyncThunk\n};\nvar ReducerType = /* @__PURE__ */(ReducerType2 => {\n  ReducerType2[\"reducer\"] = \"reducer\";\n  ReducerType2[\"reducerWithPrepare\"] = \"reducerWithPrepare\";\n  ReducerType2[\"asyncThunk\"] = \"asyncThunk\";\n  return ReducerType2;\n})(ReducerType || {});\nfunction getType(slice, actionKey) {\n  return `${slice}/${actionKey}`;\n}\nfunction buildCreateSlice({\n  creators\n} = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice2(options) {\n    const {\n      name,\n      reducerPath = name\n    } = options;\n    if (!name) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"`name` is a required option for createSlice\");\n    }\n    if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\n      if (options.initialState === void 0) {\n        console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n      }\n    }\n    const reducers = (typeof options.reducers === \"function\" ? options.reducers(buildReducerCreators()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods = {\n      addCase(typeOrActionCreator, reducer2) {\n        const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"`context.addCase` cannot be called with an empty action type\");\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"`context.addCase` cannot be called with two reducers for the same action type: \" + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer2;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer2) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer: reducer2\n        });\n        return contextMethods;\n      },\n      exposeAction(name2, actionCreator) {\n        context.actionCreators[name2] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name2, reducer2) {\n        context.sliceCaseReducersByName[name2] = reducer2;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach(reducerName => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === \"function\"\n      };\n      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (typeof options.extraReducers === \"object\") {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, builder => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key]);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = state => state;\n    const injectedSelectorCache = /* @__PURE__ */new Map();\n    const injectedStateCache = /* @__PURE__ */new WeakMap();\n    let _reducer;\n    function reducer(state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps(reducerPath2, injected = false) {\n      function selectSlice(state) {\n        let sliceState = state[reducerPath2];\n        if (typeof sliceState === \"undefined\") {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (process.env.NODE_ENV !== \"production\") {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : \"selectSlice returned undefined for an uninjected slice reducer\");\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map = {};\n          for (const [name2, selector] of Object.entries(options.selectors ?? {})) {\n            map[name2] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        });\n      }\n      return {\n        reducerPath: reducerPath2,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice = {\n      name,\n      reducer,\n      actions: context.actionCreators,\n      caseReducers: context.sliceCaseReducersByName,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        };\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector(selector, selectState, getInitialState, injected) {\n  function wrapper(rootState, ...args) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === \"undefined\") {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"selectState returned undefined for an uninjected slice reducer\");\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper;\n}\nvar createSlice = /* @__PURE__ */buildCreateSlice();\nfunction buildReducerCreators() {\n  function asyncThunk(payloadCreator, config) {\n    return {\n      _reducerDefinitionType: \"asyncThunk\" /* asyncThunk */,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: \"reducer\" /* reducer */\n      });\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: \"reducerWithPrepare\" /* reducerWithPrepare */,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk\n  };\n}\nfunction handleNormalReducerDefinition({\n  type,\n  reducerName,\n  createNotation\n}, maybeReducerWithPrepare, context) {\n  let caseReducer;\n  let prepareCallback;\n  if (\"reducer\" in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(17) : \"Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.\");\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"asyncThunk\" /* asyncThunk */;\n}\nfunction isCaseReducerWithPrepareDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"reducerWithPrepare\" /* reducerWithPrepare */;\n}\nfunction handleThunkCaseReducerDefinition({\n  type,\n  reducerName\n}, reducerDefinition, context, cAT) {\n  if (!cAT) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(18) : \"Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.\");\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {}\n\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory(stateAdapter) {\n  function getInitialState(additionalState = {}, entities) {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}\n\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState, options = {}) {\n    const {\n      createSelector: createSelector2 = createDraftSafeSelector\n    } = options;\n    const selectIds = state => state.ids;\n    const selectEntities = state => state.entities;\n    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map(id => entities[id]));\n    const selectId = (_, id) => id;\n    const selectById = (entities, id) => entities[id];\n    const selectTotal = createSelector2(selectIds, ids => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector2(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);\n    return {\n      selectIds: createSelector2(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector2(selectState, selectAll),\n      selectTotal: createSelector2(selectState, selectTotal),\n      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}\n\n// src/entities/state_adapter.ts\nimport { produce as createNextState3, isDraft as isDraft3 } from \"immer\";\nvar isDraftTyped = isDraft3;\nfunction createSingleArgumentStateOperator(mutator) {\n  const operator = createStateOperator((_, state) => mutator(state));\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    const runMutator = draft => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped(state)) {\n      runMutator(state);\n      return state;\n    }\n    return createNextState3(state, runMutator);\n  };\n}\n\n// src/entities/utils.ts\nimport { current as current2, isDraft as isDraft4 } from \"immer\";\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n  if (process.env.NODE_ENV !== \"production\" && key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction getCurrent(value) {\n  return isDraft4(value) ? current2(value) : value;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set(existingIdsArray);\n  const added = [];\n  const addedIds = /* @__PURE__ */new Set([]);\n  const updated = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}\n\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    ;\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    let didMutate = false;\n    keys.forEach(key => {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter(id => id in state.entities);\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    const original3 = state.entities[update.id];\n    if (original3 === void 0) {\n      return false;\n    }\n    const updated = Object.assign({}, original3, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    ;\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    const updatesPerEntity = {};\n    updates.forEach(update => {\n      if (update.id in state.entities) {\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter(update => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map(e => selectIdValue(e, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n\n// src/entities/sorted_state_adapter.ts\nfunction findInsertIndex(sortedItems, item, comparisonFunction) {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nfunction insert(sortedItems, item, comparisonFunction) {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nfunction createSortedStateAdapter(selectId, comparer) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state, existingIds) {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter(model => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete state.entities[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        replacedIds = true;\n        delete state.entities[update.id];\n        const oldIndex = state.ids.indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities;\n    let ids = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n\n// src/entities/create_adapter.ts\nfunction createEntityAdapter(options = {}) {\n  const {\n    selectId,\n    sortComparer\n  } = {\n    sortComparer: false,\n    selectId: instance => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}\n\n// src/listenerMiddleware/index.ts\nimport { isAction as isAction3 } from \"redux\";\n\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = `task-${cancelled}`;\nvar taskCompleted = `task-${completed}`;\nvar listenerCancelled = `${listener}-${cancelled}`;\nvar listenerCompleted = `${listener}-${completed}`;\nvar TaskAbortError = class {\n  constructor(code) {\n    this.code = code;\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n  name = \"TaskAbortError\";\n  message;\n};\n\n// src/listenerMiddleware/utils.ts\nvar assertFunction = (func, expected) => {\n  if (typeof func !== \"function\") {\n    throw new TypeError(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(32) : `${expected} is not a function`);\n  }\n};\nvar noop2 = () => {};\nvar catchRejection = (promise, onError = noop2) => {\n  promise.catch(onError);\n  return promise;\n};\nvar addAbortSignalListener = (abortSignal, callback) => {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener(\"abort\", callback);\n};\nvar abortControllerWithReason = (abortController, reason) => {\n  const signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n\n// src/listenerMiddleware/task.ts\nvar validateActive = signal => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal;\n    throw new TaskAbortError(reason);\n  }\n};\nfunction raceWithSignal(signal, promise) {\n  let cleanup = noop2;\n  return new Promise((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    cleanup = noop2;\n  });\n}\nvar runTask = async (task2, cleanUp) => {\n  try {\n    await Promise.resolve();\n    const value = await task2();\n    return {\n      status: \"ok\",\n      value\n    };\n  } catch (error) {\n    return {\n      status: error instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\nvar createPause = signal => {\n  return promise => {\n    return catchRejection(raceWithSignal(signal, promise).then(output => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = signal => {\n  const pause = createPause(signal);\n  return timeoutMs => {\n    return pause(new Promise(resolve => setTimeout(resolve, timeoutMs)));\n  };\n};\n\n// src/listenerMiddleware/index.ts\nvar {\n  assign\n} = Object;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = (parentAbortSignal, parentBlockingPromises) => {\n  const linkControllers = controller => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return (taskExecutor, opts) => {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask(async () => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result2 = await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      });\n      validateActive(childAbortController.signal);\n      return result2;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop2));\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = (startListening, signal) => {\n  const take = async (predicate, timeout) => {\n    validateActive(signal);\n    let unsubscribe = () => {};\n    const tuplePromise = new Promise((resolve, reject) => {\n      let stopListening = startListening({\n        predicate,\n        effect: (action, listenerApi) => {\n          listenerApi.unsubscribe();\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise(resolve => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      unsubscribe();\n    }\n  };\n  return (predicate, timeout) => catchRejection(take(predicate, timeout));\n};\nvar getListenerEntryPropsFrom = options => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {} else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(21) : \"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\nvar createListenerEntry = /* @__PURE__ */assign(options => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: /* @__PURE__ */new Set(),\n    unsubscribe: () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(22) : \"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n});\nvar findListenerEntry = (listenerMap, options) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find(entry => {\n    const matchPredicateOrType = typeof type === \"string\" ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nvar cancelActiveListeners = entry => {\n  entry.pending.forEach(controller => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = listenerMap => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/add`), {\n  withTypes: () => addListener\n});\nvar clearAllListeners = /* @__PURE__ */createAction(`${alm}/removeAll`);\nvar removeListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n});\nvar defaultErrorHandler = (...args) => {\n  console.error(`${alm}/error`, ...args);\n};\nvar createListenerMiddleware = (middlewareOptions = {}) => {\n  const listenerMap = /* @__PURE__ */new Map();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, \"onError\");\n  const insertEntry = entry => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return cancelOptions => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = options => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options);\n    return insertEntry(entry);\n  };\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = options => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry, action, api, getOriginalState) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening, internalTaskController.signal);\n    const autoJoinPromises = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(action,\n      // Use assign() rather than ... to avoid extra helper functions added to bundle\n      assign({}, api, {\n        getOriginalState,\n        condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),\n        take,\n        delay: createDelay(internalTaskController.signal),\n        pause: createPause(internalTaskController.signal),\n        extra,\n        signal: internalTaskController.signal,\n        fork: createFork(internalTaskController.signal, autoJoinPromises),\n        unsubscribe: entry.unsubscribe,\n        subscribe: () => {\n          listenerMap.set(entry.id, entry);\n        },\n        cancelActiveListeners: () => {\n          entry.pending.forEach((controller, _, set) => {\n            if (controller !== internalTaskController) {\n              abortControllerWithReason(controller, listenerCancelled);\n              set.delete(controller);\n            }\n          });\n        },\n        cancel: () => {\n          abortControllerWithReason(internalTaskController, listenerCancelled);\n          entry.pending.delete(internalTaskController);\n        },\n        throwIfCancelled: () => {\n          validateActive(internalTaskController.signal);\n        }\n      })));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: \"effect\"\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted);\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware = api => next => action => {\n    if (!isAction3(action)) {\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n    let originalState = api.getState();\n    const getOriginalState = () => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(23) : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState;\n    };\n    let result;\n    try {\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: \"predicate\"\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n};\n\n// src/dynamicMiddleware/index.ts\nimport { compose as compose3 } from \"redux\";\nvar createMiddlewareEntry = middleware => ({\n  middleware,\n  applied: /* @__PURE__ */new Map()\n});\nvar matchInstance = instanceId => action => action?.meta?.instanceId === instanceId;\nvar createDynamicMiddleware = () => {\n  const instanceId = nanoid();\n  const middlewareMap = /* @__PURE__ */new Map();\n  const withMiddleware = Object.assign(createAction(\"dynamicMiddleware/add\", (...middlewares) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  });\n  const addMiddleware = Object.assign(function addMiddleware2(...middlewares) {\n    middlewares.forEach(middleware2 => {\n      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  });\n  const getFinalMiddleware = api => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map(entry => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return compose3(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware = api => next => action => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};\n\n// src/combineSlices.ts\nimport { combineReducers as combineReducers2 } from \"redux\";\nvar isSliceLike = maybeSliceLike => \"reducerPath\" in maybeSliceLike && typeof maybeSliceLike.reducerPath === \"string\";\nvar getReducers = slices => slices.flatMap(sliceOrMap => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));\nvar ORIGINAL_STATE = Symbol.for(\"rtk-state-proxy-original\");\nvar isStateProxy = value => !!value && !!value[ORIGINAL_STATE];\nvar stateProxyMap = /* @__PURE__ */new WeakMap();\nvar createStateProxy = (state, reducerMap, initialStateCache) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === \"undefined\") {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== \"undefined\") return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        const reducerResult = reducer(void 0, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === \"undefined\") {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(24) : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n}));\nvar original = state => {\n  if (!isStateProxy(state)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(25) : \"original must be used on state Proxy\");\n  }\n  return state[ORIGINAL_STATE];\n};\nvar emptyObject = {};\nvar noopReducer = (state = emptyObject) => state;\nfunction combineSlices(...slices) {\n  const reducerMap = Object.fromEntries(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? combineReducers2(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state, action) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache = {};\n  const inject = (slice, config = {}) => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector(selectorFn, selectState) {\n    return function selector2(state, ...args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  });\n}\n\n// src/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\nexport { ReducerType, SHOULD_AUTOBATCH, TaskAbortError, Tuple, addListener, asyncThunkCreator, autoBatchEnhancer, buildCreateSlice, clearAllListeners, combineSlices, configureStore, createAction, createActionCreatorInvariantMiddleware, createAsyncThunk, createDraftSafeSelector, createDraftSafeSelectorCreator, createDynamicMiddleware, createEntityAdapter, createImmutableStateInvariantMiddleware, createListenerMiddleware, produce as createNextState, createReducer, createSelector, createSelectorCreator2 as createSelectorCreator, createSerializableStateInvariantMiddleware, createSlice, current3 as current, findNonSerializableValue, formatProdErrorMessage, freeze, isActionCreator, isAllOf, isAnyOf, isAsyncThunkAction, isDraft5 as isDraft, isFSA as isFluxStandardAction, isFulfilled, isImmutableDefault, isPending, isPlain, isRejected, isRejectedWithValue, lruMemoize, miniSerializeError, nanoid, original2 as original, prepareAutoBatched, removeListener, unwrapResult, weakMapMemoize2 as weakMapMemoize };", "map": {"version": 3, "names": ["produce", "current", "current3", "freeze", "original", "original2", "isDraft", "isDraft5", "createSelector", "createSelectorCreator", "createSelectorCreator2", "lruMemoize", "weakMapMemoize", "weakMapMemoize2", "createDraftSafeSelectorCreator", "args", "createSelector2", "createDraftSafeSelector2", "Object", "assign", "args2", "selector", "wrappedSelector", "value", "rest", "withTypes", "createDraftSafeSelector", "applyMiddleware", "createStore", "compose", "compose2", "combineReducers", "isPlainObject", "isPlainObject2", "composeWithDevTools", "window", "__REDUX_DEVTOOLS_EXTENSION_COMPOSE__", "arguments", "length", "apply", "devToolsEnhancer", "__REDUX_DEVTOOLS_EXTENSION__", "noop3", "thunk", "thunkMiddleware", "withExtraArgument", "isAction", "hasMatchFunction", "v", "match", "createAction", "type", "prepareAction", "actionCreator", "prepared", "Error", "process", "env", "NODE_ENV", "formatProdErrorMessage", "payload", "meta", "error", "toString", "action", "isActionCreator", "isFSA", "keys", "every", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "getMessage", "splitType", "split", "actionName", "createActionCreatorInvariantMiddleware", "options", "next", "isActionCreator2", "console", "warn", "createNextState", "isDraftable", "getTimeMeasureUtils", "max<PERSON><PERSON><PERSON>", "fnName", "elapsed", "measureTime", "fn", "started", "Date", "now", "finished", "warnIfExceeded", "<PERSON><PERSON>", "_<PERSON><PERSON>", "Array", "constructor", "items", "setPrototypeOf", "prototype", "Symbol", "species", "concat", "arr", "prepend", "isArray", "freezeDraftable", "val", "getOrInsertComputed", "map", "compute", "has", "get", "set", "isImmutableDefault", "isFrozen", "trackForMutations", "isImmutable", "ignorePaths", "obj", "trackedProperties", "trackProperties", "detectMutations", "path", "checkedObjects", "Set", "tracked", "add", "children", "child<PERSON><PERSON>", "ignoredPaths", "trackedProperty", "sameParentRef", "prevObj", "sameRef", "Number", "isNaN", "wasMutated", "keysToDetect", "hasIgnoredPaths", "nested<PERSON>ath", "hasMatches", "some", "ignored", "RegExp", "test", "result", "createImmutableStateInvariantMiddleware", "stringify2", "stringify", "serializer", "indent", "decycler", "JSON", "getSerialize2", "getSerialize", "stack", "_", "slice", "join", "thisPos", "splice", "push", "Infinity", "call", "warnAfter", "track", "bind", "getState", "state", "tracker", "measureUtils", "dispatchedAction", "isAction2", "<PERSON><PERSON><PERSON>", "findNonSerializableValue", "isSerializable", "getEntries", "cache", "foundNestedSerializable", "keyP<PERSON>", "entries", "nestedV<PERSON>ue", "isNestedFrozen", "values", "createSerializableStateInvariantMiddleware", "ignoredActions", "ignoredActionPaths", "ignoreState", "ignoreActions", "disableCache", "WeakSet", "storeAPI", "foundActionNonSerializableValue", "foundStateNonSerializableValue", "isBoolean", "x", "buildGetDefaultMiddleware", "getDefaultMiddleware", "immutableCheck", "serializableCheck", "actionCreatorCheck", "middlewareArray", "extraArgument", "immutableOptions", "unshift", "serializableOptions", "actionCreatorOptions", "SHOULD_AUTOBATCH", "prepareAutoBatched", "createQueueWithTimer", "timeout", "notify", "setTimeout", "autoBatchEnhancer", "store", "notifying", "shouldNotifyAtEndOfTick", "notificationQueued", "listeners", "queue<PERSON>allback", "queueMicrotask", "requestAnimationFrame", "queueNotification", "notifyListeners", "for<PERSON>ach", "l", "subscribe", "listener2", "wrappedListener", "unsubscribe", "delete", "dispatch", "buildGetDefaultEnhancers", "middlewareEnhancer", "getDefaultEnhancers", "autoBatch", "enhancerArray", "configureStore", "reducer", "middleware", "devTools", "duplicateMiddlewareCheck", "preloadedState", "enhancers", "rootReducer", "finalMiddleware", "item", "middlewareReferences", "middleware2", "finalCompose", "trace", "storeEnhancers", "includes", "composedEnhancer", "createNextState2", "isDraft2", "isDraftable2", "executeReducerBuilderCallback", "builderCallback", "actionsMap", "actionMatchers", "defaultCaseReducer", "builder", "addCase", "typeOrActionCreator", "addMatcher", "matcher", "addDefaultCase", "isStateFunction", "createReducer", "initialState", "mapOrBuilderCallback", "finalActionMatchers", "finalDefaultCaseReducer", "getInitialState", "frozenInitialState", "caseReducers", "filter", "reducer2", "cr", "reduce", "previousState", "caseReducer", "draft", "matches", "isAnyOf", "matchers", "isAllOf", "hasExpectedRequestMetadata", "validStatus", "hasValidRequestId", "requestId", "hasValidRequestStatus", "requestStatus", "isAsyncThunkArray", "a", "isPending", "asyncThunks", "asyncThunk", "pending", "isRejected", "rejected", "isRejectedWithValue", "hasFlag", "rejectedWithValue", "isFulfilled", "fulfilled", "isAsyncThunkAction", "flatMap", "url<PERSON>l<PERSON><PERSON>", "nanoid", "size", "id", "i", "Math", "random", "commonProperties", "RejectWithValue", "_type", "FulfillWithMeta", "miniSerializeError", "simpleError", "property", "message", "String", "externalAbortMessage", "createAsyncThunk", "createAsyncThunk2", "typePrefix", "payloadCreator", "arg", "serializeError", "aborted", "name", "condition", "signal", "extra", "idGenerator", "abortController", "AbortController", "abor<PERSON><PERSON><PERSON><PERSON>", "abortReason", "abort", "reason", "addEventListener", "once", "promise", "finalAction", "conditionResult", "isThenable", "abortedPromise", "Promise", "reject", "getPendingMeta", "race", "resolve", "rejectWithValue", "fulfillWithValue", "then", "err", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchConditionRejection", "unwrap", "unwrapResult", "settled", "asyncThunkSymbol", "for", "asyncThunkCreator", "ReducerType", "ReducerType2", "getType", "action<PERSON>ey", "buildCreateSlice", "creators", "cAT", "createSlice2", "reducerPath", "reducers", "buildReducerCreators", "reducerNames", "context", "sliceCaseReducersByName", "sliceCaseReducersByType", "actionCreators", "sliceMatchers", "contextMethods", "exposeAction", "name2", "exposeCaseReducer", "reducerName", "reducerDefinition", "reducerDetails", "createNotation", "isAsyncThunkSliceReducerDefinition", "handleThunkCaseReducerDefinition", "handleNormalReducerDefinition", "buildReducer", "extraReducers", "finalCaseReducers", "sM", "m", "selectSelf", "injectedSelectorCache", "Map", "injectedStateCache", "WeakMap", "_reducer", "makeSelectorProps", "reducerPath2", "injected", "selectSlice", "sliceState", "getSelectors", "selectState", "selectorCache", "selectors", "wrapSelector", "actions", "injectInto", "injectable", "pathOpt", "config", "newReducerPath", "inject", "wrapper", "rootState", "unwrapped", "createSlice", "_reducerDefinitionType", "preparedReducer", "prepare", "maybeReducerWithPrepare", "prepareCallback", "isCaseReducerWithPrepareDefinition", "noop", "getInitialEntityState", "ids", "entities", "createInitialStateFactory", "stateAdapter", "additionalState", "setAll", "createSelectorsFactory", "selectIds", "selectEntities", "selectAll", "selectId", "selectById", "selectTotal", "selectGlobalizedEntities", "createNextState3", "isDraft3", "isDraftTyped", "createSingleArgumentStateOperator", "mutator", "operator", "createStateOperator", "operation", "isPayloadActionArgument", "arg2", "runMutator", "current2", "isDraft4", "selectIdValue", "entity", "ensureEntitiesArray", "get<PERSON>urrent", "splitAddedUpdatedEntities", "newEntities", "existingIdsArray", "existingIds", "added", "addedIds", "updated", "changes", "createUnsortedStateAdapter", "addOneMutably", "addManyMutably", "setOneMutably", "setManyMutably", "setAllMutably", "removeOneMutably", "removeManyMutably", "didMutate", "removeAllMutably", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "original3", "new<PERSON>ey", "has<PERSON>ew<PERSON><PERSON>", "updateOneMutably", "updateManyMutably", "updates", "newKeys", "updatesPerEntity", "didMutateEntities", "didMutateIds", "e", "upsertOneMutably", "upsertManyMutably", "removeAll", "addOne", "addMany", "setOne", "setMany", "updateOne", "updateMany", "upsertOne", "upsertMany", "removeOne", "remove<PERSON>any", "findInsertIndex", "sortedItems", "comparisonFunction", "lowIndex", "highIndex", "middleIndex", "currentItem", "res", "insert", "insertAtIndex", "createSortedStateAdapter", "comparer", "existingKeys", "models", "model", "mergeFunction", "appliedUpdates", "replacedIds", "newId", "oldIndex", "areArraysEqual", "b", "addedItems", "currentEntities", "currentIds", "stateEntities", "sortedEntities", "was<PERSON>revious<PERSON><PERSON><PERSON><PERSON>", "sort", "newSortedIds", "createEntityAdapter", "sortComparer", "instance", "stateFactory", "selectorsFactory", "isAction3", "task", "listener", "completed", "cancelled", "taskCancelled", "taskCompleted", "listenerCancelled", "listenerCompleted", "TaskAbortError", "code", "assertFunction", "func", "expected", "TypeError", "noop2", "catchRejection", "onError", "catch", "addAbortSignalListener", "abortSignal", "callback", "abortControllerWithReason", "defineProperty", "enumerable", "configurable", "writable", "validateActive", "raceWithSignal", "cleanup", "notifyRejection", "finally", "runTask", "task2", "cleanUp", "status", "createPause", "output", "createDelay", "pause", "timeoutMs", "INTERNAL_NIL_TOKEN", "alm", "createFork", "parentAbortSignal", "parentBlockingPromises", "linkControllers", "controller", "taskExecutor", "opts", "childAbortController", "result2", "delay", "autoJoin", "cancel", "createTakePattern", "startListening", "take", "predicate", "tuplePromise", "stopListening", "effect", "listenerApi", "getOriginalState", "promises", "getListenerEntryPropsFrom", "createListenerEntry", "entry", "findListenerEntry", "listenerMap", "from", "find", "matchPredicateOrType", "cancelActiveListeners", "createClearListenerMiddleware", "clear", "safelyNotifyError", "<PERSON><PERSON><PERSON><PERSON>", "errorToNotify", "errorInfo", "errorHandlerError", "addListener", "clearAllListeners", "removeListener", "defaultErrorHandler", "createListenerMiddleware", "middlewareOptions", "insertEntry", "cancelOptions", "cancelActive", "notifyL<PERSON>ener", "api", "internalTaskController", "autoJoinPromises", "Boolean", "fork", "throwIfCancelled", "listenerError", "<PERSON><PERSON><PERSON>", "all", "clearListenerMiddleware", "originalState", "currentState", "listenerEntries", "runListener", "predicateError", "clearListeners", "compose3", "createMiddlewareEntry", "applied", "matchInstance", "instanceId", "createDynamicMiddleware", "middlewareMap", "withMiddleware", "middlewares", "addMiddleware", "addMiddleware2", "getFinalMiddleware", "appliedMiddleware", "isWithMiddleware", "combineReducers2", "isSliceLike", "maybeSliceLike", "getReducers", "slices", "sliceOrMap", "ORIGINAL_STATE", "isStateProxy", "stateProxyMap", "createStateProxy", "reducerMap", "initialStateCache", "Proxy", "target", "prop", "receiver", "Reflect", "cached", "reducerResult", "emptyObject", "noopReducer", "combineSlices", "fromEntries", "getReducer", "combinedReducer", "withLazyLoadedSlices", "reducerToInject", "currentReducer", "overrideExisting", "makeSelector", "selectorFn", "selector2"], "sources": ["D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\index.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\createDraftSafeSelector.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\configureStore.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\devtoolsExtension.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\getDefaultMiddleware.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\createAction.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\tsHelpers.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\actionCreatorInvariantMiddleware.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\utils.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\immutableStateInvariantMiddleware.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\serializableStateInvariantMiddleware.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\autoBatchEnhancer.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\getDefaultEnhancers.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\createReducer.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\mapBuilders.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\matchers.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\nanoid.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\createAsyncThunk.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\createSlice.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\entities\\entity_state.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\entities\\state_selectors.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\entities\\state_adapter.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\entities\\utils.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\entities\\unsorted_state_adapter.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\entities\\sorted_state_adapter.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\entities\\create_adapter.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\listenerMiddleware\\index.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\listenerMiddleware\\exceptions.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\listenerMiddleware\\utils.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\listenerMiddleware\\task.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\dynamicMiddleware\\index.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\combineSlices.ts", "D:\\FlashCardManager\\flashcard-manager\\node_modules\\@reduxjs\\toolkit\\src\\formatProdErrorMessage.ts"], "sourcesContent": ["// This must remain here so that the `mangleErrors.cjs` build script\n// does not have to import this into each source file it rewrites.\nimport { formatProdErrorMessage } from './formatProdErrorMessage';\nexport * from 'redux';\nexport { produce as createNextState, current, freeze, original, isDraft } from 'immer';\nexport type { Draft } from 'immer';\nexport { createSelector, createSelectorCreator, lruMemoize, weakMapMemoize } from 'reselect';\nexport type { Selector, OutputSelector } from 'reselect';\nexport { createDraftSafeSelector, createDraftSafeSelectorCreator } from './createDraftSafeSelector';\nexport type { ThunkAction, ThunkDispatch, ThunkMiddleware } from 'redux-thunk';\nexport {\n// js\nconfigureStore } from './configureStore';\nexport type {\n// types\nConfigureStoreOptions, EnhancedStore } from './configureStore';\nexport type { DevToolsEnhancerOptions } from './devtoolsExtension';\nexport {\n// js\ncreateAction, isActionCreator, isFSA as isFluxStandardAction } from './createAction';\nexport type {\n// types\nPayloadAction, PayloadActionCreator, ActionCreatorWithNonInferrablePayload, ActionCreatorWithOptionalPayload, ActionCreatorWithPayload, ActionCreatorWithoutPayload, ActionCreatorWithPreparedPayload, PrepareAction } from './createAction';\nexport {\n// js\ncreateReducer } from './createReducer';\nexport type {\n// types\nActions, CaseReducer, CaseReducers } from './createReducer';\nexport {\n// js\ncreateSlice, buildCreateSlice, asyncThunkCreator, ReducerType } from './createSlice';\nexport type {\n// types\nCreateSliceOptions, Slice, CaseReducerActions, SliceCaseReducers, ValidateSliceCaseReducers, CaseReducerWithPrepare, ReducerCreators, SliceSelectors } from './createSlice';\nexport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nexport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nexport {\n// js\ncreateImmutableStateInvariantMiddleware, isImmutableDefault } from './immutableStateInvariantMiddleware';\nexport type {\n// types\nImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\nexport {\n// js\ncreateSerializableStateInvariantMiddleware, findNonSerializableValue, isPlain } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nSerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nexport type {\n// types\nActionReducerMapBuilder } from './mapBuilders';\nexport { Tuple } from './utils';\nexport { createEntityAdapter } from './entities/create_adapter';\nexport type { EntityState, EntityAdapter, EntitySelectors, EntityStateAdapter, EntityId, Update, IdSelector, Comparer } from './entities/models';\nexport { createAsyncThunk, unwrapResult, miniSerializeError } from './createAsyncThunk';\nexport type { AsyncThunk, AsyncThunkOptions, AsyncThunkAction, AsyncThunkPayloadCreatorReturnValue, AsyncThunkPayloadCreator, GetState, GetThunkAPI, SerializedError, CreateAsyncThunkFunction } from './createAsyncThunk';\nexport {\n// js\nisAllOf, isAnyOf, isPending, isRejected, isFulfilled, isAsyncThunkAction, isRejectedWithValue } from './matchers';\nexport type {\n// types\nActionMatchingAllOf, ActionMatchingAnyOf } from './matchers';\nexport { nanoid } from './nanoid';\nexport type { ListenerEffect, ListenerMiddleware, ListenerEffectAPI, ListenerMiddlewareInstance, CreateListenerMiddlewareOptions, ListenerErrorHandler, TypedStartListening, TypedAddListener, TypedStopListening, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions, ForkedTaskExecutor, ForkedTask, ForkedTaskAPI, AsyncTaskExecutor, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult } from './listenerMiddleware/index';\nexport type { AnyListenerPredicate } from './listenerMiddleware/types';\nexport { createListenerMiddleware, addListener, removeListener, clearAllListeners, TaskAbortError } from './listenerMiddleware/index';\nexport type { AddMiddleware, DynamicDispatch, DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './dynamicMiddleware/types';\nexport { createDynamicMiddleware } from './dynamicMiddleware/index';\nexport { SHOULD_AUTOBATCH, prepareAutoBatched, autoBatchEnhancer } from './autoBatchEnhancer';\nexport type { AutoBatchOptions } from './autoBatchEnhancer';\nexport { combineSlices } from './combineSlices';\nexport type { CombinedSliceReducer, WithSlice } from './combineSlices';\nexport type { ExtractDispatchExtensions as TSHelpersExtractDispatchExtensions, SafePromise } from './tsHelpers';\nexport { formatProdErrorMessage } from './formatProdErrorMessage';", "import { current, isDraft } from 'immer';\nimport { createSelectorCreator, weakMapMemoize } from 'reselect';\nexport const createDraftSafeSelectorCreator: typeof createSelectorCreator = (...args: unknown[]) => {\n  const createSelector = (createSelectorCreator as any)(...args);\n  const createDraftSafeSelector = Object.assign((...args: unknown[]) => {\n    const selector = createSelector(...args);\n    const wrappedSelector = (value: unknown, ...rest: unknown[]) => selector(isDraft(value) ? current(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector as any;\n  }, {\n    withTypes: () => createDraftSafeSelector\n  });\n  return createDraftSafeSelector;\n};\n\n/**\n * \"Draft-Safe\" version of `reselect`'s `createSelector`:\n * If an `immer`-drafted object is passed into the resulting selector's first argument,\n * the selector will act on the current draft value, instead of returning a cached value\n * that might be possibly outdated if the draft has been modified since.\n * @public\n */\nexport const createDraftSafeSelector = /* @__PURE__ */\ncreateDraftSafeSelectorCreator(weakMapMemoize);", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Reducer, ReducersMapObject, Middleware, Action, StoreEnhancer, Store, UnknownAction } from 'redux';\nimport { applyMiddleware, createStore, compose, combineReducers, isPlainObject } from 'redux';\nimport type { DevToolsEnhancerOptions as DevToolsOptions } from './devtoolsExtension';\nimport { composeWithDevTools } from './devtoolsExtension';\nimport type { ThunkMiddlewareFor, GetDefaultMiddleware } from './getDefaultMiddleware';\nimport { buildGetDefaultMiddleware } from './getDefaultMiddleware';\nimport type { ExtractDispatchExtensions, ExtractStoreExtensions, ExtractStateExtensions, UnknownIfNonSpecific } from './tsHelpers';\nimport type { Tuple } from './utils';\nimport type { GetDefaultEnhancers } from './getDefaultEnhancers';\nimport { buildGetDefaultEnhancers } from './getDefaultEnhancers';\n\n/**\n * Options for `configureStore()`.\n *\n * @public\n */\nexport interface ConfigureStoreOptions<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<Middlewares<S>>, E extends Tuple<Enhancers> = Tuple<Enhancers>, P = S> {\n  /**\n   * A single reducer function that will be used as the root reducer, or an\n   * object of slice reducers that will be passed to `combineReducers()`.\n   */\n  reducer: Reducer<S, A, P> | ReducersMapObject<S, A, P>;\n\n  /**\n   * An array of Redux middleware to install, or a callback receiving `getDefaultMiddleware` and returning a Tuple of middleware.\n   * If not supplied, defaults to the set of middleware returned by `getDefaultMiddleware()`.\n   *\n   * @example `middleware: (gDM) => gDM().concat(logger, apiMiddleware, yourCustomMiddleware)`\n   * @see https://redux-toolkit.js.org/api/getDefaultMiddleware#intended-usage\n   */\n  middleware?: (getDefaultMiddleware: GetDefaultMiddleware<S>) => M;\n\n  /**\n   * Whether to enable Redux DevTools integration. Defaults to `true`.\n   *\n   * Additional configuration can be done by passing Redux DevTools options\n   */\n  devTools?: boolean | DevToolsOptions;\n\n  /**\n   * Whether to check for duplicate middleware instances. Defaults to `true`.\n   */\n  duplicateMiddlewareCheck?: boolean;\n\n  /**\n   * The initial state, same as Redux's createStore.\n   * You may optionally specify it to hydrate the state\n   * from the server in universal apps, or to restore a previously serialized\n   * user session. If you use `combineReducers()` to produce the root reducer\n   * function (either directly or indirectly by passing an object as `reducer`),\n   * this must be an object with the same shape as the reducer map keys.\n   */\n  // we infer here, and instead complain if the reducer doesn't match\n  preloadedState?: P;\n\n  /**\n   * The store enhancers to apply. See Redux's `createStore()`.\n   * All enhancers will be included before the DevTools Extension enhancer.\n   * If you need to customize the order of enhancers, supply a callback\n   * function that will receive a `getDefaultEnhancers` function that returns a Tuple,\n   * and should return a Tuple of enhancers (such as `getDefaultEnhancers().concat(offline)`).\n   * If you only need to add middleware, you can use the `middleware` parameter instead.\n   */\n  enhancers?: (getDefaultEnhancers: GetDefaultEnhancers<M>) => E;\n}\nexport type Middlewares<S> = ReadonlyArray<Middleware<{}, S>>;\ntype Enhancers = ReadonlyArray<StoreEnhancer>;\n\n/**\n * A Redux store returned by `configureStore()`. Supports dispatching\n * side-effectful _thunks_ in addition to plain actions.\n *\n * @public\n */\nexport type EnhancedStore<S = any, A extends Action = UnknownAction, E extends Enhancers = Enhancers> = ExtractStoreExtensions<E> & Store<S, A, UnknownIfNonSpecific<ExtractStateExtensions<E>>>;\n\n/**\n * A friendly abstraction over the standard Redux `createStore()` function.\n *\n * @param options The store configuration.\n * @returns A configured Redux store.\n *\n * @public\n */\nexport function configureStore<S = any, A extends Action = UnknownAction, M extends Tuple<Middlewares<S>> = Tuple<[ThunkMiddlewareFor<S>]>, E extends Tuple<Enhancers> = Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>, StoreEnhancer]>, P = S>(options: ConfigureStoreOptions<S, A, M, E, P>): EnhancedStore<S, A, E> {\n  const getDefaultMiddleware = buildGetDefaultMiddleware<S>();\n  const {\n    reducer = undefined,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = undefined,\n    enhancers = undefined\n  } = options || {};\n  let rootReducer: Reducer<S, A, P>;\n  if (typeof reducer === 'function') {\n    rootReducer = reducer;\n  } else if (isPlainObject(reducer)) {\n    rootReducer = combineReducers(reducer) as unknown as Reducer<S, A, P>;\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(1) : '`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\n  }\n  if (process.env.NODE_ENV !== 'production' && middleware && typeof middleware !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(2) : '`middleware` field must be a callback');\n  }\n  let finalMiddleware: Tuple<Middlewares<S>>;\n  if (typeof middleware === 'function') {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (process.env.NODE_ENV !== 'production' && !Array.isArray(finalMiddleware)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(3) : 'when using a middleware builder function, an array of middleware must be returned');\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(4) : 'each middleware provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && duplicateMiddlewareCheck) {\n    let middlewareReferences = new Set<Middleware<any, S>>();\n    finalMiddleware.forEach(middleware => {\n      if (middlewareReferences.has(middleware)) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(42) : 'Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.');\n      }\n      middlewareReferences.add(middleware);\n    });\n  }\n  let finalCompose = compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: process.env.NODE_ENV !== 'production',\n      ...(typeof devTools === 'object' && devTools)\n    });\n  }\n  const middlewareEnhancer = applyMiddleware(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers<M>(middlewareEnhancer);\n  if (process.env.NODE_ENV !== 'production' && enhancers && typeof enhancers !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(5) : '`enhancers` field must be a callback');\n  }\n  let storeEnhancers = typeof enhancers === 'function' ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (process.env.NODE_ENV !== 'production' && !Array.isArray(storeEnhancers)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(6) : '`enhancers` callback must return an array');\n  }\n  if (process.env.NODE_ENV !== 'production' && storeEnhancers.some((item: any) => typeof item !== 'function')) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(7) : 'each enhancer provided to configureStore must be a function');\n  }\n  if (process.env.NODE_ENV !== 'production' && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error('middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`');\n  }\n  const composedEnhancer: StoreEnhancer<any> = finalCompose(...storeEnhancers);\n  return createStore(rootReducer, preloadedState as P, composedEnhancer);\n}", "import type { Action, ActionCreator, StoreEnhancer } from 'redux';\nimport { compose } from 'redux';\n\n/**\r\n * @public\r\n */\nexport interface DevToolsEnhancerOptions {\n  /**\r\n   * the instance name to be showed on the monitor page. Default value is `document.title`.\r\n   * If not specified and there's no document title, it will consist of `tabId` and `instanceId`.\r\n   */\n  name?: string;\n  /**\r\n   * action creators functions to be available in the Dispatcher.\r\n   */\n  actionCreators?: ActionCreator<any>[] | {\n    [key: string]: ActionCreator<any>;\n  };\n  /**\r\n   * if more than one action is dispatched in the indicated interval, all new actions will be collected and sent at once.\r\n   * It is the joint between performance and speed. When set to `0`, all actions will be sent instantly.\r\n   * Set it to a higher value when experiencing perf issues (also `maxAge` to a lower value).\r\n   *\r\n   * @default 500 ms.\r\n   */\n  latency?: number;\n  /**\r\n   * (> 1) - maximum allowed actions to be stored in the history tree. The oldest actions are removed once maxAge is reached. It's critical for performance.\r\n   *\r\n   * @default 50\r\n   */\n  maxAge?: number;\n  /**\r\n   * Customizes how actions and state are serialized and deserialized. Can be a boolean or object. If given a boolean, the behavior is the same as if you\r\n   * were to pass an object and specify `options` as a boolean. Giving an object allows fine-grained customization using the `replacer` and `reviver`\r\n   * functions.\r\n   */\n  serialize?: boolean | {\n    /**\r\n     * - `undefined` - will use regular `JSON.stringify` to send data (it's the fast mode).\r\n     * - `false` - will handle also circular references.\r\n     * - `true` - will handle also date, regex, undefined, error objects, symbols, maps, sets and functions.\r\n     * - object, which contains `date`, `regex`, `undefined`, `error`, `symbol`, `map`, `set` and `function` keys.\r\n     *   For each of them you can indicate if to include (by setting as `true`).\r\n     *   For `function` key you can also specify a custom function which handles serialization.\r\n     *   See [`jsan`](https://github.com/kolodny/jsan) for more details.\r\n     */\n    options?: undefined | boolean | {\n      date?: true;\n      regex?: true;\n      undefined?: true;\n      error?: true;\n      symbol?: true;\n      map?: true;\n      set?: true;\n      function?: true | ((fn: (...args: any[]) => any) => string);\n    };\n    /**\r\n     * [JSON replacer function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter) used for both actions and states stringify.\r\n     * In addition, you can specify a data type by adding a [`__serializedType__`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/helpers/index.js#L4)\r\n     * key. So you can deserialize it back while importing or persisting data.\r\n     * Moreover, it will also [show a nice preview showing the provided custom type](https://cloud.githubusercontent.com/assets/7957859/21814330/a17d556a-d761-11e6-85ef-159dd12f36c5.png):\r\n     */\n    replacer?: (key: string, value: unknown) => any;\n    /**\r\n     * [JSON `reviver` function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter)\r\n     * used for parsing the imported actions and states. See [`remotedev-serialize`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/immutable/serialize.js#L8-L41)\r\n     * as an example on how to serialize special data types and get them back.\r\n     */\n    reviver?: (key: string, value: unknown) => any;\n    /**\r\n     * Automatically serialize/deserialize immutablejs via [remotedev-serialize](https://github.com/zalmoxisus/remotedev-serialize).\r\n     * Just pass the Immutable library. It will support all ImmutableJS structures. You can even export them into a file and get them back.\r\n     * The only exception is `Record` class, for which you should pass this in addition the references to your classes in `refs`.\r\n     */\n    immutable?: any;\n    /**\r\n     * ImmutableJS `Record` classes used to make possible restore its instances back when importing, persisting...\r\n     */\n    refs?: any;\n  };\n  /**\r\n   * function which takes `action` object and id number as arguments, and should return `action` object back.\r\n   */\n  actionSanitizer?: <A extends Action>(action: A, id: number) => A;\n  /**\r\n   * function which takes `state` object and index as arguments, and should return `state` object back.\r\n   */\n  stateSanitizer?: <S>(state: S, index: number) => S;\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsDenylist?: string | string[];\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\n  actionsAllowlist?: string | string[];\n  /**\r\n   * called for every action before sending, takes `state` and `action` object, and returns `true` in case it allows sending the current data to the monitor.\r\n   * Use it as a more advanced version of `actionsDenylist`/`actionsAllowlist` parameters.\r\n   */\n  predicate?: <S, A extends Action>(state: S, action: A) => boolean;\n  /**\r\n   * if specified as `false`, it will not record the changes till clicking on `Start recording` button.\r\n   * Available only for Redux enhancer, for others use `autoPause`.\r\n   *\r\n   * @default true\r\n   */\n  shouldRecordChanges?: boolean;\n  /**\r\n   * if specified, whenever clicking on `Pause recording` button and there are actions in the history log, will add this action type.\r\n   * If not specified, will commit when paused. Available only for Redux enhancer.\r\n   *\r\n   * @default \"@@PAUSED\"\"\r\n   */\n  pauseActionType?: string;\n  /**\r\n   * auto pauses when the extension’s window is not opened, and so has zero impact on your app when not in use.\r\n   * Not available for Redux enhancer (as it already does it but storing the data to be sent).\r\n   *\r\n   * @default false\r\n   */\n  autoPause?: boolean;\n  /**\r\n   * if specified as `true`, it will not allow any non-monitor actions to be dispatched till clicking on `Unlock changes` button.\r\n   * Available only for Redux enhancer.\r\n   *\r\n   * @default false\r\n   */\n  shouldStartLocked?: boolean;\n  /**\r\n   * if set to `false`, will not recompute the states on hot reloading (or on replacing the reducers). Available only for Redux enhancer.\r\n   *\r\n   * @default true\r\n   */\n  shouldHotReload?: boolean;\n  /**\r\n   * if specified as `true`, whenever there's an exception in reducers, the monitors will show the error message, and next actions will not be dispatched.\r\n   *\r\n   * @default false\r\n   */\n  shouldCatchErrors?: boolean;\n  /**\r\n   * If you want to restrict the extension, specify the features you allow.\r\n   * If not specified, all of the features are enabled. When set as an object, only those included as `true` will be allowed.\r\n   * Note that except `true`/`false`, `import` and `export` can be set as `custom` (which is by default for Redux enhancer), meaning that the importing/exporting occurs on the client side.\r\n   * Otherwise, you'll get/set the data right from the monitor part.\r\n   */\n  features?: {\n    /**\r\n     * start/pause recording of dispatched actions\r\n     */\n    pause?: boolean;\n    /**\r\n     * lock/unlock dispatching actions and side effects\r\n     */\n    lock?: boolean;\n    /**\r\n     * persist states on page reloading\r\n     */\n    persist?: boolean;\n    /**\r\n     * export history of actions in a file\r\n     */\n    export?: boolean | 'custom';\n    /**\r\n     * import history of actions from a file\r\n     */\n    import?: boolean | 'custom';\n    /**\r\n     * jump back and forth (time travelling)\r\n     */\n    jump?: boolean;\n    /**\r\n     * skip (cancel) actions\r\n     */\n    skip?: boolean;\n    /**\r\n     * drag and drop actions in the history list\r\n     */\n    reorder?: boolean;\n    /**\r\n     * dispatch custom actions or action creators\r\n     */\n    dispatch?: boolean;\n    /**\r\n     * generate tests for the selected actions\r\n     */\n    test?: boolean;\n  };\n  /**\r\n   * Set to true or a stacktrace-returning function to record call stack traces for dispatched actions.\r\n   * Defaults to false.\r\n   */\n  trace?: boolean | (<A extends Action>(action: A) => string);\n  /**\r\n   * The maximum number of stack trace entries to record per action. Defaults to 10.\r\n   */\n  traceLimit?: number;\n}\ntype Compose = typeof compose;\ninterface ComposeWithDevTools {\n  (options: DevToolsEnhancerOptions): Compose;\n  <StoreExt extends {}>(...funcs: StoreEnhancer<StoreExt>[]): StoreEnhancer<StoreExt>;\n}\n\n/**\r\n * @public\r\n */\nexport const composeWithDevTools: ComposeWithDevTools = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return undefined;\n  if (typeof arguments[0] === 'object') return compose;\n  return compose.apply(null, arguments as any as Function[]);\n};\n\n/**\r\n * @public\r\n */\nexport const devToolsEnhancer: {\n  (options: DevToolsEnhancerOptions): StoreEnhancer<any>;\n} = typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__ ? (window as any).__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop) {\n    return noop;\n  };\n};", "import type { Middleware, UnknownAction } from 'redux';\nimport type { ThunkMiddleware } from 'redux-thunk';\nimport { thunk as thunkMiddleware, withExtraArgument } from 'redux-thunk';\nimport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware';\nimport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware';\nimport type { ImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware';\n/* PROD_START_REMOVE_UMD */\nimport { createImmutableStateInvariantMiddleware } from './immutableStateInvariantMiddleware';\n/* PROD_STOP_REMOVE_UMD */\n\nimport type { SerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware';\nimport { createSerializableStateInvariantMiddleware } from './serializableStateInvariantMiddleware';\nimport type { ExcludeFromTuple } from './tsHelpers';\nimport { Tuple } from './utils';\nfunction isBoolean(x: any): x is boolean {\n  return typeof x === 'boolean';\n}\ninterface ThunkOptions<E = any> {\n  extraArgument: E;\n}\ninterface GetDefaultMiddlewareOptions {\n  thunk?: boolean | ThunkOptions;\n  immutableCheck?: boolean | ImmutableStateInvariantMiddlewareOptions;\n  serializableCheck?: boolean | SerializableStateInvariantMiddlewareOptions;\n  actionCreatorCheck?: boolean | ActionCreatorInvariantMiddlewareOptions;\n}\nexport type ThunkMiddlewareFor<S, O extends GetDefaultMiddlewareOptions = {}> = O extends {\n  thunk: false;\n} ? never : O extends {\n  thunk: {\n    extraArgument: infer E;\n  };\n} ? ThunkMiddleware<S, UnknownAction, E> : ThunkMiddleware<S, UnknownAction>;\nexport type GetDefaultMiddleware<S = any> = <O extends GetDefaultMiddlewareOptions = {\n  thunk: true;\n  immutableCheck: true;\n  serializableCheck: true;\n  actionCreatorCheck: true;\n}>(options?: O) => Tuple<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>>;\nexport const buildGetDefaultMiddleware = <S = any,>(): GetDefaultMiddleware<S> => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple<Middleware[]>();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (immutableCheck) {\n      /* PROD_START_REMOVE_UMD */\n      let immutableOptions: ImmutableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n      /* PROD_STOP_REMOVE_UMD */\n    }\n    if (serializableCheck) {\n      let serializableOptions: SerializableStateInvariantMiddlewareOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions: ActionCreatorInvariantMiddlewareOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport { isAction } from 'redux';\nimport type { IsUnknownOrNonInferrable, IfMaybeUndefined, IfVoid, IsAny } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\n\n/**\n * An action with a string type and an associated payload. This is the\n * type of action returned by `createAction()` action creators.\n *\n * @template P The type of the action's payload.\n * @template T the type used for the action type.\n * @template M The type of the action's meta (optional)\n * @template E The type of the action's error (optional)\n *\n * @public\n */\nexport type PayloadAction<P = void, T extends string = string, M = never, E = never> = {\n  payload: P;\n  type: T;\n} & ([M] extends [never] ? {} : {\n  meta: M;\n}) & ([E] extends [never] ? {} : {\n  error: E;\n});\n\n/**\n * A \"prepare\" method to be used as the second parameter of `createAction`.\n * Takes any number of arguments and returns a Flux Standard Action without\n * type (will be added later) that *must* contain a payload (might be undefined).\n *\n * @public\n */\nexport type PrepareAction<P> = ((...args: any[]) => {\n  payload: P;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  error: any;\n}) | ((...args: any[]) => {\n  payload: P;\n  meta: any;\n  error: any;\n});\n\n/**\n * Internal version of `ActionCreatorWithPreparedPayload`. Not to be used externally.\n *\n * @internal\n */\nexport type _ActionCreatorWithPreparedPayload<PA extends PrepareAction<any> | void, T extends string = string> = PA extends PrepareAction<infer P> ? ActionCreatorWithPreparedPayload<Parameters<PA>, P, T, ReturnType<PA> extends {\n  error: infer E;\n} ? E : never, ReturnType<PA> extends {\n  meta: infer M;\n} ? M : never> : void;\n\n/**\n * Basic type for all action creators.\n *\n * @inheritdoc {redux#ActionCreator}\n */\nexport type BaseActionCreator<P, T extends string, M = never, E = never> = {\n  type: T;\n  match: (action: unknown) => action is PayloadAction<P, T, M, E>;\n};\n\n/**\n * An action creator that takes multiple arguments that are passed\n * to a `PrepareAction` method to create the final Action.\n * @typeParam Args arguments for the action creator function\n * @typeParam P `payload` type\n * @typeParam T `type` name\n * @typeParam E optional `error` type\n * @typeParam M optional `meta` type\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPreparedPayload<Args extends unknown[], P, T extends string = string, E = never, M = never> extends BaseActionCreator<P, T, M, E> {\n  /**\n   * Calling this {@link redux#ActionCreator} with `Args` will return\n   * an Action with a payload of type `P` and (depending on the `PrepareAction`\n   * method used) a `meta`- and `error` property of types `M` and `E` respectively.\n   */\n  (...args: Args): PayloadAction<P, T, M, E>;\n}\n\n/**\n * An action creator of type `T` that takes an optional payload of type `P`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithOptionalPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`.\n   * Calling it without an argument will return a PayloadAction with a payload of `undefined`.\n   */\n  (payload?: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` that takes no payload.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithoutPayload<T extends string = string> extends BaseActionCreator<undefined, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} will\n   * return a {@link PayloadAction} of type `T` with a payload of `undefined`\n   */\n  (noArgument: void): PayloadAction<undefined, T>;\n}\n\n/**\n * An action creator of type `T` that requires a payload of type P.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithPayload<P, T extends string = string> extends BaseActionCreator<P, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload of `P`\n   */\n  (payload: P): PayloadAction<P, T>;\n}\n\n/**\n * An action creator of type `T` whose `payload` type could not be inferred. Accepts everything as `payload`.\n *\n * @inheritdoc {redux#ActionCreator}\n *\n * @public\n */\nexport interface ActionCreatorWithNonInferrablePayload<T extends string = string> extends BaseActionCreator<unknown, T> {\n  /**\n   * Calling this {@link redux#ActionCreator} with an argument will\n   * return a {@link PayloadAction} of type `T` with a payload\n   * of exactly the type of the argument.\n   */\n  <PT extends unknown>(payload: PT): PayloadAction<PT, T>;\n}\n\n/**\n * An action creator that produces actions with a `payload` attribute.\n *\n * @typeParam P the `payload` type\n * @typeParam T the `type` of the resulting action\n * @typeParam PA if the resulting action is preprocessed by a `prepare` method, the signature of said method.\n *\n * @public\n */\nexport type PayloadActionCreator<P = void, T extends string = string, PA extends PrepareAction<P> | void = void> = IfPrepareActionMethodProvided<PA, _ActionCreatorWithPreparedPayload<PA, T>,\n// else\nIsAny<P, ActionCreatorWithPayload<any, T>, IsUnknownOrNonInferrable<P, ActionCreatorWithNonInferrablePayload<T>,\n// else\nIfVoid<P, ActionCreatorWithoutPayload<T>,\n// else\nIfMaybeUndefined<P, ActionCreatorWithOptionalPayload<P, T>,\n// else\nActionCreatorWithPayload<P, T>>>>>>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<P = void, T extends string = string>(type: T): PayloadActionCreator<P, T>;\n\n/**\n * A utility function to create an action creator for the given action type\n * string. The action creator accepts a single argument, which will be included\n * in the action object as a field called payload. The action creator function\n * will also have its toString() overridden so that it returns the action type.\n *\n * @param type The action type to use for created actions.\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\n *\n * @public\n */\nexport function createAction<PA extends PrepareAction<any>, T extends string = string>(type: T, prepareAction: PA): PayloadActionCreator<ReturnType<PA>['payload'], T, PA>;\nexport function createAction(type: string, prepareAction?: Function): any {\n  function actionCreator(...args: any[]) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(0) : 'prepareAction did not return an object');\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...('meta' in prepared && {\n          meta: prepared.meta\n        }),\n        ...('error' in prepared && {\n          error: prepared.error\n        })\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action: unknown): action is PayloadAction => isAction(action) && action.type === type;\n  return actionCreator;\n}\n\n/**\n * Returns true if value is an RTK-like action creator, with a static type property and match method.\n */\nexport function isActionCreator(action: unknown): action is BaseActionCreator<unknown, string> & Function {\n  return typeof action === 'function' && 'type' in action &&\n  // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action as any);\n}\n\n/**\n * Returns true if value is an action with a string type and valid Flux Standard Action keys.\n */\nexport function isFSA(action: unknown): action is {\n  type: string;\n  payload?: unknown;\n  error?: unknown;\n  meta?: unknown;\n} {\n  return isAction(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key: string) {\n  return ['type', 'payload', 'error', 'meta'].indexOf(key) > -1;\n}\n\n// helper types for more readable typings\n\ntype IfPrepareActionMethodProvided<PA extends PrepareAction<any> | void, True, False> = PA extends ((...args: any[]) => any) ? True : False;", "import type { Middleware, StoreEnhancer } from 'redux';\nimport type { Tuple } from './utils';\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>) {\n  Object.assign(target, ...args);\n}\n\n/**\n * return True if T is `any`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsAny<T, True, False = never> =\n// test if we are going the left AND right path in the condition\ntrue | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;\n\n/**\n * return True if T is `unknown`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\nexport type IsUnknown<T, True, False = never> = unknown extends T ? IsAny<T, False, True> : False;\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>;\n\n/**\n * @internal\n */\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False;\n\n/**\n * @internal\n */\nexport type IsEmptyObj<T, True, False = never> = T extends any ? keyof T extends never ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>> : False : never;\n\n/**\n * returns True if TS version is above 3.5, False if below.\n * uses feature detection to detect TS version >= 3.5\n * * versions below 3.5 will return `{}` for unresolvable interference\n * * versions above will return `unknown`\n *\n * @internal\n */\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<ReturnType<<T>() => T>, 0, 1>];\n\n/**\n * @internal\n */\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<IsUnknown<T, True, False>, IsEmptyObj<T, True, IsUnknown<T, True, False>>>;\n\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n// Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [infer Head, ...infer Tail] ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]> : Acc;\ntype ExtractDispatchFromMiddlewareTuple<MiddlewareTuple extends readonly any[], Acc extends {}> = MiddlewareTuple extends [infer Head, ...infer Tail] ? ExtractDispatchFromMiddlewareTuple<Tail, Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})> : Acc;\nexport type ExtractDispatchExtensions<M> = M extends Tuple<infer MiddlewareTuple> ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}> : M extends ReadonlyArray<Middleware> ? ExtractDispatchFromMiddlewareTuple<[...M], {}> : never;\ntype ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStoreExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})> : Acc;\nexport type ExtractStoreExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<infer Ext> ? Ext extends {} ? IsAny<Ext, {}, Ext> : {} : {}> : never;\ntype ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStateExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<any, infer StateExt> ? IsAny<StateExt, {}, StateExt> : {})> : Acc;\nexport type ExtractStateExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<any, infer StateExt> ? StateExt extends {} ? IsAny<StateExt, {}, StateExt> : {} : {}> : never;\n\n/**\n * Helper type. Passes T out again, but boxes it in a way that it cannot\n * \"widen\" the type by accident if it is a generic that should be inferred\n * from elsewhere.\n *\n * @internal\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type WithOptionalProp<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport interface TypeGuard<T> {\n  (value: any): value is T;\n}\nexport interface HasMatchFunction<T> {\n  match: TypeGuard<T>;\n}\nexport const hasMatchFunction = <T,>(v: Matcher<T>): v is HasMatchFunction<T> => {\n  return v && typeof (v as HasMatchFunction<T>).match === 'function';\n};\n\n/** @public */\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>;\n\n/** @public */\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<infer T> ? T : never;\nexport type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type Tail<T extends any[]> = T extends [any, ...infer Tail] ? Tail : never;\nexport type UnknownIfNonSpecific<T> = {} extends T ? unknown : T;\n\n/**\n * A Promise that will never reject.\n * @see https://github.com/reduxjs/redux-toolkit/issues/4101\n */\nexport type SafePromise<T> = Promise<T> & {\n  __linterBrands: 'SafePromise';\n};\n\n/**\n * Properly wraps a Promise as a {@link SafePromise} with .catch(fallback).\n */\nexport function asSafePromise<Resolved, Rejected>(promise: Promise<Resolved>, fallback: (error: unknown) => Rejected) {\n  return promise.catch(fallback) as SafePromise<Resolved | Rejected>;\n}", "import type { Middleware } from 'redux';\nimport { isActionCreator as isRTKAction } from './createAction';\nexport interface ActionCreatorInvariantMiddlewareOptions {\n  /**\n   * The function to identify whether a value is an action creator.\n   * The default checks for a function with a static type property and match method.\n   */\n  isActionCreator?: (action: unknown) => action is Function & {\n    type?: unknown;\n  };\n}\nexport function getMessage(type?: unknown) {\n  const splitType = type ? `${type}`.split('/') : [];\n  const actionName = splitType[splitType.length - 1] || 'actionCreator';\n  return `Detected an action creator with type \"${type || 'unknown'}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nexport function createActionCreatorInvariantMiddleware(options: ActionCreatorInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  }\n  const {\n    isActionCreator = isRTKAction\n  } = options;\n  return () => next => action => {\n    if (isActionCreator(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}", "import { produce as createNextState, isDraftable } from 'immer';\nexport function getTimeMeasureUtils(maxDelay: number, fnName: string) {\n  let elapsed = 0;\n  return {\n    measureTime<T>(fn: () => T): T {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nexport function delay(ms: number) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\nexport class Tuple<Items extends ReadonlyArray<unknown> = []> extends Array<Items[number]> {\n  constructor(length: number);\n  constructor(...items: Items);\n  constructor(...items: any[]) {\n    super(...items);\n    Object.setPrototypeOf(this, Tuple.prototype);\n  }\n  static override get [Symbol.species]() {\n    return Tuple as any;\n  }\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...Items, ...AdditionalItems]>;\n  override concat(...arr: any[]) {\n    return super.concat.apply(this, arr);\n  }\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: Tuple<AdditionalItems>): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend<AdditionalItems extends ReadonlyArray<unknown>>(...items: AdditionalItems): Tuple<[...AdditionalItems, ...Items]>;\n  prepend(...arr: any[]) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new Tuple(...arr[0].concat(this));\n    }\n    return new Tuple(...arr.concat(this));\n  }\n}\nexport function freezeDraftable<T>(val: T) {\n  return isDraftable(val) ? createNextState(val, () => {}) : val;\n}\nexport function getOrInsert<K extends object, V>(map: WeakMap<K, V>, key: K, value: V): V;\nexport function getOrInsert<K, V>(map: Map<K, V>, key: K, value: V): V;\nexport function getOrInsert<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, value: V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, value).get(key) as V;\n}\nexport function getOrInsertComputed<K extends object, V>(map: WeakMap<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K, V>(map: Map<K, V>, key: K, compute: (key: K) => V): V;\nexport function getOrInsertComputed<K extends object, V>(map: Map<K, V> | WeakMap<K, V>, key: K, compute: (key: K) => V): V {\n  if (map.has(key)) return map.get(key) as V;\n  return map.set(key, compute(key)).get(key) as V;\n}\nexport function promiseWithResolvers<T>(): {\n  promise: Promise<T>;\n  resolve: (value: T | PromiseLike<T>) => void;\n  reject: (reason?: any) => void;\n} {\n  let resolve: any;\n  let reject: any;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {\n    promise,\n    resolve,\n    reject\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Middleware } from 'redux';\nimport type { IgnorePaths } from './serializableStateInvariantMiddleware';\nimport { getTimeMeasureUtils } from './utils';\ntype EntryProcessor = (key: string, value: any) => any;\n\n/**\n * The default `isImmutable` function.\n *\n * @public\n */\nexport function isImmutableDefault(value: unknown): boolean {\n  return typeof value !== 'object' || value == null || Object.isFrozen(value);\n}\nexport function trackForMutations(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths | undefined, obj: any) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\ninterface TrackedProperty {\n  value: any;\n  children: Record<string, any>;\n}\nfunction trackProperties(isImmutable: IsImmutableFunc, ignorePaths: IgnorePaths = [], obj: Record<string, any>, path: string = '', checkedObjects: Set<Record<string, any>> = new Set()) {\n  const tracked: Partial<TrackedProperty> = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + '.' + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked as TrackedProperty;\n}\nfunction detectMutations(isImmutable: IsImmutableFunc, ignoredPaths: IgnorePaths = [], trackedProperty: TrackedProperty, obj: any, sameParentRef: boolean = false, path: string = ''): {\n  wasMutated: boolean;\n  path?: string;\n} {\n  const prevObj = trackedProperty ? trackedProperty.value : undefined;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n\n  // Gather all keys from prev (tracked) and after objs\n  const keysToDetect: Record<string, boolean> = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\ntype IsImmutableFunc = (value: any) => boolean;\n\n/**\n * Options for `createImmutableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface ImmutableStateInvariantMiddlewareOptions {\n  /**\n    Callback function to check if a value is considered to be immutable.\n    This function is applied recursively to every value contained in the state.\n    The default implementation will return true for primitive types\n    (like numbers, strings, booleans, null and undefined).\n   */\n  isImmutable?: IsImmutableFunc;\n  /**\n    An array of dot-separated path strings that match named nodes from\n    the root state to ignore when checking for immutability.\n    Defaults to undefined\n   */\n  ignoredPaths?: IgnorePaths;\n  /** Print a warning if checks take longer than N ms. Default: 32ms */\n  warnAfter?: number;\n}\n\n/**\n * Creates a middleware that checks whether any state was mutated in between\n * dispatches or during a dispatch. If any mutations are detected, an error is\n * thrown.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createImmutableStateInvariantMiddleware(options: ImmutableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    function stringify(obj: any, serializer?: EntryProcessor, indent?: string | number, decycler?: EntryProcessor): string {\n      return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\n    }\n    function getSerialize(serializer?: EntryProcessor, decycler?: EntryProcessor): EntryProcessor {\n      let stack: any[] = [],\n        keys: any[] = [];\n      if (!decycler) decycler = function (_: string, value: any) {\n        if (stack[0] === value) return '[Circular ~]';\n        return '[Circular ~.' + keys.slice(0, stack.indexOf(value)).join('.') + ']';\n      };\n      return function (this: any, key: string, value: any) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler!.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    }\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return next => action => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, 'ImmutableStateInvariantMiddleware');\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(19) : `A state mutation was detected between dispatches, in the path '${result.path || ''}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          // Track before potentially not meeting the invariant\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(20) : `A state mutation was detected inside a dispatch, in the path: ${result.path || ''}. Take a look at the reducer(s) handling the action ${stringify(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}", "import type { Middleware } from 'redux';\nimport { isAction, isPlainObject } from 'redux';\nimport { getTimeMeasureUtils } from './utils';\n\n/**\n * Returns true if the passed value is \"plain\", i.e. a value that is either\n * directly JSON-serializable (boolean, number, string, array, plain object)\n * or `undefined`.\n *\n * @param val The value to check.\n *\n * @public\n */\nexport function isPlain(val: any) {\n  const type = typeof val;\n  return val == null || type === 'string' || type === 'boolean' || type === 'number' || Array.isArray(val) || isPlainObject(val);\n}\ninterface NonSerializableValue {\n  keyPath: string;\n  value: unknown;\n}\nexport type IgnorePaths = readonly (string | RegExp)[];\n\n/**\n * @public\n */\nexport function findNonSerializableValue(value: unknown, path: string = '', isSerializable: (value: unknown) => boolean = isPlain, getEntries?: (value: unknown) => [string, any][], ignoredPaths: IgnorePaths = [], cache?: WeakSet<object>): NonSerializableValue | false {\n  let foundNestedSerializable: NonSerializableValue | false;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || '<root>',\n      value: value\n    };\n  }\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + '.' + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some(ignored => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === 'object') {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nexport function isNestedFrozen(value: object) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== 'object' || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\n\n/**\n * Options for `createSerializableStateInvariantMiddleware()`.\n *\n * @public\n */\nexport interface SerializableStateInvariantMiddlewareOptions {\n  /**\n   * The function to check if a value is considered serializable. This\n   * function is applied recursively to every value contained in the\n   * state. Defaults to `isPlain()`.\n   */\n  isSerializable?: (value: any) => boolean;\n  /**\n   * The function that will be used to retrieve entries from each\n   * value.  If unspecified, `Object.entries` will be used. Defaults\n   * to `undefined`.\n   */\n  getEntries?: (value: any) => [string, any][];\n\n  /**\n   * An array of action types to ignore when checking for serializability.\n   * Defaults to []\n   */\n  ignoredActions?: string[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to\n   * ['meta.arg', 'meta.baseQueryMeta']\n   */\n  ignoredActionPaths?: (string | RegExp)[];\n\n  /**\n   * An array of dot-separated path strings or regular expressions to ignore\n   * when checking for serializability, Defaults to []\n   */\n  ignoredPaths?: (string | RegExp)[];\n  /**\n   * Execution time warning threshold. If the middleware takes longer\n   * than `warnAfter` ms, a warning will be displayed in the console.\n   * Defaults to 32ms.\n   */\n  warnAfter?: number;\n\n  /**\n   * Opt out of checking state. When set to `true`, other state-related params will be ignored.\n   */\n  ignoreState?: boolean;\n\n  /**\n   * Opt out of checking actions. When set to `true`, other action-related params will be ignored.\n   */\n  ignoreActions?: boolean;\n\n  /**\n   * Opt out of caching the results. The cache uses a WeakSet and speeds up repeated checking processes.\n   * The cache is automatically disabled if no browser support for WeakSet is present.\n   */\n  disableCache?: boolean;\n}\n\n/**\n * Creates a middleware that, after every state change, checks if the new\n * state is serializable. If a non-serializable value is found within the\n * state, an error is printed to the console.\n *\n * @param options Middleware options.\n *\n * @public\n */\nexport function createSerializableStateInvariantMiddleware(options: SerializableStateInvariantMiddlewareOptions = {}): Middleware {\n  if (process.env.NODE_ENV === 'production') {\n    return () => next => action => next(action);\n  } else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = ['meta.arg', 'meta.baseQueryMeta'],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache: WeakSet<object> | undefined = !disableCache && WeakSet ? new WeakSet() : undefined;\n    return storeAPI => next => action => {\n      if (!isAction(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, 'SerializableStateInvariantMiddleware');\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type as any) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, '', isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, '\\nTake a look at the logic that dispatched this action: ', action, '\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)', '\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)');\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, '', isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}", "import type { StoreEnhancer } from 'redux';\nexport const SHOULD_AUTOBATCH = 'RTK_autoBatch';\nexport const prepareAutoBatched = <T,>() => (payload: T): {\n  payload: T;\n  meta: unknown;\n} => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nconst createQueueWithTimer = (timeout: number) => {\n  return (notify: () => void) => {\n    setTimeout(notify, timeout);\n  };\n};\nexport type AutoBatchOptions = {\n  type: 'tick';\n} | {\n  type: 'timer';\n  timeout: number;\n} | {\n  type: 'raf';\n} | {\n  type: 'callback';\n  queueNotification: (notify: () => void) => void;\n};\n\n/**\n * A Redux store enhancer that watches for \"low-priority\" actions, and delays\n * notifying subscribers until either the queued callback executes or the\n * next \"standard-priority\" action is dispatched.\n *\n * This allows dispatching multiple \"low-priority\" actions in a row with only\n * a single subscriber notification to the UI after the sequence of actions\n * is finished, thus improving UI re-render performance.\n *\n * Watches for actions with the `action.meta[SHOULD_AUTOBATCH]` attribute.\n * This can be added to `action.meta` manually, or by using the\n * `prepareAutoBatched` helper.\n *\n * By default, it will queue a notification for the end of the event loop tick.\n * However, you can pass several other options to configure the behavior:\n * - `{type: 'tick'}`: queues using `queueMicrotask`\n * - `{type: 'timer', timeout: number}`: queues using `setTimeout`\n * - `{type: 'raf'}`: queues using `requestAnimationFrame` (default)\n * - `{type: 'callback', queueNotification: (notify: () => void) => void}`: lets you provide your own callback\n *\n *\n */\nexport const autoBatchEnhancer = (options: AutoBatchOptions = {\n  type: 'raf'\n}): StoreEnhancer => next => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = new Set<() => void>();\n  const queueCallback = options.type === 'tick' ? queueMicrotask : options.type === 'raf' ?\n  // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n  typeof window !== 'undefined' && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10) : options.type === 'callback' ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    // We're running at the end of the event loop tick.\n    // Run the real listener callbacks to actually update the UI.\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach(l => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener: () => void) {\n      // Each wrapped listener will only call the real listener if\n      // the `notifying` flag is currently active when it's called.\n      // This lets the base store work as normal, while the actual UI\n      // update becomes controlled by this enhancer.\n      const wrappedListener: typeof listener = () => notifying && listener();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action: any) {\n      try {\n        // If the action does _not_ have the `shouldAutoBatch` flag,\n        // we resume/continue normal notify-after-each-dispatch behavior\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        // If a `notifyListeners` microtask was queued, you can't cancel it.\n        // Instead, we set a flag so that it's a no-op when it does run\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          // We've seen at least 1 action with `SHOULD_AUTOBATCH`. Try to queue\n          // a microtask to notify listeners at the end of the event loop tick.\n          // Make sure we only enqueue this _once_ per tick.\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        // Go ahead and process the action as usual, including reducers.\n        // If normal notification behavior is enabled, the store will notify\n        // all of its own listeners, and the wrapper callbacks above will\n        // see `notifying` is true and pass on to the real listener callbacks.\n        // If we're \"batching\" behavior, then the wrapped callbacks will\n        // bail out, causing the base store notification behavior to be no-ops.\n        return store.dispatch(action);\n      } finally {\n        // Assume we're back to normal behavior after each action\n        notifying = true;\n      }\n    }\n  });\n};", "import type { StoreEnhancer } from 'redux';\nimport type { AutoBatchOptions } from './autoBatchEnhancer';\nimport { autoBatchEnhancer } from './autoBatchEnhancer';\nimport { Tuple } from './utils';\nimport type { Middlewares } from './configureStore';\nimport type { ExtractDispatchExtensions } from './tsHelpers';\ntype GetDefaultEnhancersOptions = {\n  autoBatch?: boolean | AutoBatchOptions;\n};\nexport type GetDefaultEnhancers<M extends Middlewares<any>> = (options?: GetDefaultEnhancersOptions) => Tuple<[StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>]>;\nexport const buildGetDefaultEnhancers = <M extends Middlewares<any>,>(middlewareEnhancer: StoreEnhancer<{\n  dispatch: ExtractDispatchExtensions<M>;\n}>): GetDefaultEnhancers<M> => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple<StoreEnhancer[]>(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === 'object' ? autoBatch : undefined));\n  }\n  return enhancerArray as any;\n};", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { Draft } from 'immer';\nimport { produce as createNextState, isDraft, isDraftable } from 'immer';\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { ActionReducerMapBuilder } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { NoInfer, TypeGuard } from './tsHelpers';\nimport { freezeDraftable } from './utils';\n\n/**\n * Defines a mapping from action types to corresponding action object shapes.\n *\n * @deprecated This should not be used manually - it is only used for internal\n *             inference purposes and should not have any further value.\n *             It might be removed in the future.\n * @public\n */\nexport type Actions<T extends keyof any = string> = Record<T, Action>;\nexport type ActionMatcherDescription<S, A extends Action> = {\n  matcher: TypeGuard<A>;\n  reducer: CaseReducer<S, NoInfer<A>>;\n};\nexport type ReadonlyActionMatcherDescriptionCollection<S> = ReadonlyArray<ActionMatcherDescription<S, any>>;\nexport type ActionMatcherDescriptionCollection<S> = Array<ActionMatcherDescription<S, any>>;\n\n/**\n * A *case reducer* is a reducer function for a specific action type. Case\n * reducers can be composed to full reducers using `createReducer()`.\n *\n * Unlike a normal Redux reducer, a case reducer is never called with an\n * `undefined` state to determine the initial state. Instead, the initial\n * state is explicitly specified as an argument to `createReducer()`.\n *\n * In addition, a case reducer can choose to mutate the passed-in `state`\n * value directly instead of returning a new state. This does not actually\n * cause the store state to be mutated directly; instead, thanks to\n * [immer](https://github.com/mweststrate/immer), the mutations are\n * translated to copy operations that result in a new state.\n *\n * @public\n */\nexport type CaseReducer<S = any, A extends Action = UnknownAction> = (state: Draft<S>, action: A) => NoInfer<S> | void | Draft<NoInfer<S>>;\n\n/**\n * A mapping from action types to case reducers for `createReducer()`.\n *\n * @deprecated This should not be used manually - it is only used\n *             for internal inference purposes and using it manually\n *             would lead to type erasure.\n *             It might be removed in the future.\n * @public\n */\nexport type CaseReducers<S, AS extends Actions> = { [T in keyof AS]: AS[T] extends Action ? CaseReducer<S, AS[T]> : void };\nexport type NotFunction<T> = T extends Function ? never : T;\nfunction isStateFunction<S>(x: unknown): x is () => S {\n  return typeof x === 'function';\n}\nexport type ReducerWithInitialState<S extends NotFunction<any>> = Reducer<S> & {\n  getInitialState: () => S;\n};\n\n/**\n * A utility function that allows defining a reducer as a mapping from action\n * type to *case reducer* functions that handle these action types. The\n * reducer's initial state is passed as the first argument.\n *\n * @remarks\n * The body of every case reducer is implicitly wrapped with a call to\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\n * This means that rather than returning a new state object, you can also\n * mutate the passed-in state object directly; these mutations will then be\n * automatically and efficiently translated into copies, giving you both\n * convenience and immutability.\n *\n * @overloadSummary\n * This function accepts a callback that receives a `builder` object as its argument.\n * That builder provides `addCase`, `addMatcher` and `addDefaultCase` functions that may be\n * called to define what actions this reducer will handle.\n *\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n * @param builderCallback - `(builder: Builder) => void` A callback that receives a *builder* object to define\n *   case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n * @example\n```ts\nimport {\n  createAction,\n  createReducer,\n  UnknownAction,\n  PayloadAction,\n} from \"@reduxjs/toolkit\";\n\nconst increment = createAction<number>(\"increment\");\nconst decrement = createAction<number>(\"decrement\");\n\nfunction isActionWithNumberPayload(\n  action: UnknownAction\n): action is PayloadAction<number> {\n  return typeof action.payload === \"number\";\n}\n\nconst reducer = createReducer(\n  {\n    counter: 0,\n    sumOfNumberPayloads: 0,\n    unhandledActions: 0,\n  },\n  (builder) => {\n    builder\n      .addCase(increment, (state, action) => {\n        // action is inferred correctly here\n        state.counter += action.payload;\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {\n        state.counter -= action.payload;\n      })\n      // You can apply a \"matcher function\" to incoming actions\n      .addMatcher(isActionWithNumberPayload, (state, action) => {})\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {});\n  }\n);\n```\n * @public\n */\nexport function createReducer<S extends NotFunction<any>>(initialState: S | (() => S), mapOrBuilderCallback: (builder: ActionReducerMapBuilder<S>) => void): ReducerWithInitialState<S> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof mapOrBuilderCallback === 'object') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(8) : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n\n  // Ensure the initial state gets frozen either way (if draftable)\n  let getInitialState: () => S;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action: any): S {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer\n    }) => reducer)];\n    if (caseReducers.filter(cr => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer): S => {\n      if (caseReducer) {\n        if (isDraft(previousState)) {\n          // If it's already a draft, we must already be inside a `createNextState` call,\n          // likely because this is being wrapped in `createReducer`, `createSlice`, or nested\n          // inside an existing draft. It's safe to just pass the draft to the mutator.\n          const draft = previousState as Draft<S>; // We can assume this is already a draft\n          const result = caseReducer(draft, action);\n          if (result === undefined) {\n            return previousState;\n          }\n          return result as S;\n        } else if (!isDraftable(previousState)) {\n          // If state is not draftable (ex: a primitive, such as 0), we want to directly\n          // return the caseReducer func and not wrap it with produce.\n          const result = caseReducer(previousState as any, action);\n          if (result === undefined) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error('A case reducer on a non-draftable value must not return undefined');\n          }\n          return result as S;\n        } else {\n          // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\n          // than an Immutable<S>, and TypeScript cannot find out how to reconcile\n          // these two types.\n          return createNextState(previousState, (draft: Draft<S>) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer as ReducerWithInitialState<S>;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6 } from \"@reduxjs/toolkit\";\nimport type { Action } from 'redux';\nimport type { CaseReducer, CaseReducers, ActionMatcherDescriptionCollection } from './createReducer';\nimport type { TypeGuard } from './tsHelpers';\nexport type TypedActionCreator<Type extends string> = {\n  (...args: any[]): Action<Type>;\n  type: Type;\n};\n\n/**\n * A builder for an action <-> reducer map.\n *\n * @public\n */\nexport interface ActionReducerMapBuilder<State> {\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ActionReducerMapBuilder<State>;\n  /**\n   * Adds a case reducer to handle a single exact action type.\n   * @remarks\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ActionReducerMapBuilder<State>;\n\n  /**\n   * Allows you to match your incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   * @example\n  ```ts\n  import {\n  createAction,\n  createReducer,\n  AsyncThunk,\n  UnknownAction,\n  } from \"@reduxjs/toolkit\";\n  type GenericAsyncThunk = AsyncThunk<unknown, unknown, any>;\n  type PendingAction = ReturnType<GenericAsyncThunk[\"pending\"]>;\n  type RejectedAction = ReturnType<GenericAsyncThunk[\"rejected\"]>;\n  type FulfilledAction = ReturnType<GenericAsyncThunk[\"fulfilled\"]>;\n  const initialState: Record<string, string> = {};\n  const resetAction = createAction(\"reset-tracked-loading-state\");\n  function isPendingAction(action: UnknownAction): action is PendingAction {\n  return typeof action.type === \"string\" && action.type.endsWith(\"/pending\");\n  }\n  const reducer = createReducer(initialState, (builder) => {\n  builder\n    .addCase(resetAction, () => initialState)\n    // matcher can be defined outside as a type predicate function\n    .addMatcher(isPendingAction, (state, action) => {\n      state[action.meta.requestId] = \"pending\";\n    })\n    .addMatcher(\n      // matcher can be defined inline as a type predicate function\n      (action): action is RejectedAction => action.type.endsWith(\"/rejected\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"rejected\";\n      }\n    )\n    // matcher can just return boolean and the matcher can receive a generic argument\n    .addMatcher<FulfilledAction>(\n      (action) => action.type.endsWith(\"/fulfilled\"),\n      (state, action) => {\n        state[action.meta.requestId] = \"fulfilled\";\n      }\n    );\n  });\n  ```\n   */\n  addMatcher<A>(matcher: TypeGuard<A> | ((action: any) => boolean), reducer: CaseReducer<State, A extends Action ? A : A & Action>): Omit<ActionReducerMapBuilder<State>, 'addCase'>;\n\n  /**\n   * Adds a \"default case\" reducer that is executed if no case reducer and no matcher\n   * reducer was executed for this action.\n   * @param reducer - The fallback \"default case\" reducer function.\n   *\n   * @example\n  ```ts\n  import { createReducer } from '@reduxjs/toolkit'\n  const initialState = { otherActions: 0 }\n  const reducer = createReducer(initialState, builder => {\n  builder\n    // .addCase(...)\n    // .addMatcher(...)\n    .addDefaultCase((state, action) => {\n      state.otherActions++\n    })\n  })\n  ```\n   */\n  addDefaultCase(reducer: CaseReducer<State, Action>): {};\n}\nexport function executeReducerBuilderCallback<S>(builderCallback: (builder: ActionReducerMapBuilder<S>) => void): [CaseReducers<S, any>, ActionMatcherDescriptionCollection<S>, CaseReducer<S, Action> | undefined] {\n  const actionsMap: CaseReducers<S, any> = {};\n  const actionMatchers: ActionMatcherDescriptionCollection<S> = [];\n  let defaultCaseReducer: CaseReducer<S, Action> | undefined;\n  const builder = {\n    addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<S>) {\n      if (process.env.NODE_ENV !== 'production') {\n        /*\n         to keep the definition by the user in line with actual behavior,\n         we enforce `addCase` to always be called before calling `addMatcher`\n         as matching cases take precedence over matchers\n         */\n        if (actionMatchers.length > 0) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(26) : '`builder.addCase` should only be called before calling `builder.addMatcher`');\n        }\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(27) : '`builder.addCase` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(28) : '`builder.addCase` cannot be called with an empty action type');\n      }\n      if (type in actionsMap) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(29) : '`builder.addCase` cannot be called with two reducers for the same action type ' + `'${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<S, A extends Action ? A : A & Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(30) : '`builder.addMatcher` should only be called before calling `builder.addDefaultCase`');\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer: CaseReducer<S, Action>) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(31) : '`builder.addDefaultCase` can only be called once');\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}", "import type { Action<PERSON>romMatcher, Matcher, UnionToIntersection } from './tsHelpers';\nimport { hasMatchFunction } from './tsHelpers';\nimport type { AsyncThunk, AsyncThunkFulfilledActionCreator, AsyncThunkPendingActionCreator, AsyncThunkRejectedActionCreator } from './createAsyncThunk';\n\n/** @public */\nexport type ActionMatchingAnyOf<Matchers extends Matcher<any>[]> = ActionFromMatcher<Matchers[number]>;\n\n/** @public */\nexport type ActionMatchingAllOf<Matchers extends Matcher<any>[]> = UnionToIntersection<ActionMatchingAnyOf<Matchers>>;\nconst matches = (matcher: Matcher<any>, action: any) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches any one of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAnyOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAnyOf<Matchers> => {\n    return matchers.some(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action matches all of the supplied type guards or action\n * creators.\n *\n * @param matchers The type guards or action creators to match against.\n *\n * @public\n */\nexport function isAllOf<Matchers extends Matcher<any>[]>(...matchers: Matchers) {\n  return (action: any): action is ActionMatchingAllOf<Matchers> => {\n    return matchers.every(matcher => matches(matcher, action));\n  };\n}\n\n/**\n * @param action A redux action\n * @param validStatus An array of valid meta.requestStatus values\n *\n * @internal\n */\nexport function hasExpectedRequestMetadata(action: any, validStatus: readonly string[]) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === 'string';\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a: [any] | AnyAsyncThunk[]): a is AnyAsyncThunk[] {\n  return typeof a[0] === 'function' && 'pending' in a[0] && 'fulfilled' in a[0] && 'rejected' in a[0];\n}\nexport type UnknownAsyncThunkPendingAction = ReturnType<AsyncThunkPendingActionCreator<unknown>>;\nexport type PendingActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is pending.\n *\n * @public\n */\nexport function isPending(): (action: any) => action is UnknownAsyncThunkPendingAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is pending.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is PendingActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a pending thunk action\n * @public\n */\nexport function isPending(action: any): action is UnknownAsyncThunkPendingAction;\nexport function isPending<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.pending));\n}\nexport type UnknownAsyncThunkRejectedAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected.\n *\n * @public\n */\nexport function isRejected(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action\n * @public\n */\nexport function isRejected(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejected<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.rejected));\n}\nexport type UnknownAsyncThunkRejectedWithValueAction = ReturnType<AsyncThunkRejectedActionCreator<unknown, unknown>>;\nexport type RejectedWithValueActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['rejected']> & (T extends AsyncThunk<any, any, {\n  rejectValue: infer RejectedValue;\n}> ? {\n  payload: RejectedValue;\n} : unknown);\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is rejected with value.\n *\n * @public\n */\nexport function isRejectedWithValue(): (action: any) => action is UnknownAsyncThunkRejectedAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is rejected with value.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is RejectedWithValueActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a rejected thunk action with value\n * @public\n */\nexport function isRejectedWithValue(action: any): action is UnknownAsyncThunkRejectedAction;\nexport function isRejectedWithValue<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  const hasFlag = (action: any): action is any => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nexport type UnknownAsyncThunkFulfilledAction = ReturnType<AsyncThunkFulfilledActionCreator<unknown, unknown>>;\nexport type FulfilledActionFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['fulfilled']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator, and that\n * the action is fulfilled.\n *\n * @public\n */\nexport function isFulfilled(): (action: any) => action is UnknownAsyncThunkFulfilledAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators,\n * and that the action is fulfilled.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is FulfilledActionFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a fulfilled thunk action\n * @public\n */\nexport function isFulfilled(action: any): action is UnknownAsyncThunkFulfilledAction;\nexport function isFulfilled<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['fulfilled']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map(asyncThunk => asyncThunk.fulfilled));\n}\nexport type UnknownAsyncThunkAction = UnknownAsyncThunkPendingAction | UnknownAsyncThunkRejectedAction | UnknownAsyncThunkFulfilledAction;\nexport type AnyAsyncThunk = {\n  pending: {\n    match: (action: any) => action is any;\n  };\n  fulfilled: {\n    match: (action: any) => action is any;\n  };\n  rejected: {\n    match: (action: any) => action is any;\n  };\n};\nexport type ActionsFromAsyncThunk<T extends AnyAsyncThunk> = ActionFromMatcher<T['pending']> | ActionFromMatcher<T['fulfilled']> | ActionFromMatcher<T['rejected']>;\n\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action was created by an async thunk action creator.\n *\n * @public\n */\nexport function isAsyncThunkAction(): (action: any) => action is UnknownAsyncThunkAction;\n/**\n * A higher-order function that returns a function that may be used to check\n * whether an action belongs to one of the provided async thunk action creators.\n *\n * @param asyncThunks (optional) The async thunk action creators to match against.\n *\n * @public\n */\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks): (action: any) => action is ActionsFromAsyncThunk<AsyncThunks[number]>;\n/**\n * Tests if `action` is a thunk action\n * @public\n */\nexport function isAsyncThunkAction(action: any): action is UnknownAsyncThunkAction;\nexport function isAsyncThunkAction<AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]>(...asyncThunks: AsyncThunks | [any]) {\n  if (asyncThunks.length === 0) {\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending', 'fulfilled', 'rejected']);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap(asyncThunk => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}", "// Borrowed from https://github.com/ai/nanoid/blob/3.0.2/non-secure/index.js\n// This alphabet uses `A-Za-z0-9_-` symbols. A genetic algorithm helped\n// optimize the gzip compression for this alphabet.\nlet urlAlphabet = 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';\n\n/**\r\n *\r\n * @public\r\n */\nexport let nanoid = (size = 21) => {\n  let id = '';\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\n  let i = size;\n  while (i--) {\n    // `| 0` is more compact and faster than `Math.floor()`.\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};", "import type { Dispatch, UnknownAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport type { ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport { isAnyOf } from './matchers';\nimport { nanoid } from './nanoid';\nimport type { FallbackIfUnknown, Id, IsAny, IsUnknown, SafePromise } from './tsHelpers';\nexport type BaseThunkAPI<S, E, D extends Dispatch = Dispatch, RejectedValue = unknown, RejectedMeta = unknown, FulfilledMeta = unknown> = {\n  dispatch: D;\n  getState: () => S;\n  extra: E;\n  requestId: string;\n  signal: AbortSignal;\n  abort: (reason?: string) => void;\n  rejectWithValue: IsUnknown<RejectedMeta, (value: RejectedValue) => RejectWithValue<RejectedValue, RejectedMeta>, (value: RejectedValue, meta: RejectedMeta) => RejectWithValue<RejectedValue, RejectedMeta>>;\n  fulfillWithValue: IsUnknown<FulfilledMeta, <FulfilledValue>(value: FulfilledValue) => FulfilledValue, <FulfilledValue>(value: FulfilledValue, meta: FulfilledMeta) => FulfillWithMeta<FulfilledValue, FulfilledMeta>>;\n};\n\n/**\n * @public\n */\nexport interface SerializedError {\n  name?: string;\n  message?: string;\n  stack?: string;\n  code?: string;\n}\nconst commonProperties: Array<keyof SerializedError> = ['name', 'message', 'stack', 'code'];\nclass RejectWithValue<Payload, RejectedMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'RejectWithValue';\n  constructor(public readonly payload: Payload, public readonly meta: RejectedMeta) {}\n}\nclass FulfillWithMeta<Payload, FulfilledMeta> {\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  private readonly _type!: 'FulfillWithMeta';\n  constructor(public readonly payload: Payload, public readonly meta: FulfilledMeta) {}\n}\n\n/**\n * Serializes an error into a plain object.\n * Reworked from https://github.com/sindresorhus/serialize-error\n *\n * @public\n */\nexport const miniSerializeError = (value: any): SerializedError => {\n  if (typeof value === 'object' && value !== null) {\n    const simpleError: SerializedError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === 'string') {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nexport type AsyncThunkConfig = {\n  state?: unknown;\n  dispatch?: ThunkDispatch<unknown, unknown, UnknownAction>;\n  extra?: unknown;\n  rejectValue?: unknown;\n  serializedErrorType?: unknown;\n  pendingMeta?: unknown;\n  fulfilledMeta?: unknown;\n  rejectedMeta?: unknown;\n};\nexport type GetState<ThunkApiConfig> = ThunkApiConfig extends {\n  state: infer State;\n} ? State : unknown;\ntype GetExtra<ThunkApiConfig> = ThunkApiConfig extends {\n  extra: infer Extra;\n} ? Extra : unknown;\ntype GetDispatch<ThunkApiConfig> = ThunkApiConfig extends {\n  dispatch: infer Dispatch;\n} ? FallbackIfUnknown<Dispatch, ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>> : ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, UnknownAction>;\nexport type GetThunkAPI<ThunkApiConfig> = BaseThunkAPI<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, GetDispatch<ThunkApiConfig>, GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>, GetFulfilledMeta<ThunkApiConfig>>;\ntype GetRejectValue<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectValue: infer RejectValue;\n} ? RejectValue : unknown;\ntype GetPendingMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  pendingMeta: infer PendingMeta;\n} ? PendingMeta : unknown;\ntype GetFulfilledMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  fulfilledMeta: infer FulfilledMeta;\n} ? FulfilledMeta : unknown;\ntype GetRejectedMeta<ThunkApiConfig> = ThunkApiConfig extends {\n  rejectedMeta: infer RejectedMeta;\n} ? RejectedMeta : unknown;\ntype GetSerializedErrorType<ThunkApiConfig> = ThunkApiConfig extends {\n  serializedErrorType: infer GetSerializedErrorType;\n} ? GetSerializedErrorType : SerializedError;\ntype MaybePromise<T> = T | Promise<T> | (T extends any ? Promise<T> : never);\n\n/**\n * A type describing the return value of the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig extends AsyncThunkConfig> = MaybePromise<IsUnknown<GetFulfilledMeta<ThunkApiConfig>, Returned, FulfillWithMeta<Returned, GetFulfilledMeta<ThunkApiConfig>>> | RejectWithValue<GetRejectValue<ThunkApiConfig>, GetRejectedMeta<ThunkApiConfig>>>;\n/**\n * A type describing the `payloadCreator` argument to `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunkPayloadCreator<Returned, ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = (arg: ThunkArg, thunkAPI: GetThunkAPI<ThunkApiConfig>) => AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig>;\n\n/**\n * A ThunkAction created by `createAsyncThunk`.\n * Dispatching it returns a Promise for either a\n * fulfilled or rejected action.\n * Also, the returned value contains an `abort()` method\n * that allows the asyncAction to be cancelled from the outside.\n *\n * @public\n */\nexport type AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = (dispatch: NonNullable<GetDispatch<ThunkApiConfig>>, getState: () => GetState<ThunkApiConfig>, extra: GetExtra<ThunkApiConfig>) => SafePromise<ReturnType<AsyncThunkFulfilledActionCreator<Returned, ThunkArg>> | ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>>> & {\n  abort: (reason?: string) => void;\n  requestId: string;\n  arg: ThunkArg;\n  unwrap: () => Promise<Returned>;\n};\n\n/**\n * Config provided when calling the async thunk action creator.\n */\nexport interface AsyncThunkDispatchConfig {\n  /**\n   * An external `AbortSignal` that will be tracked by the internal `AbortSignal`.\n   */\n  signal?: AbortSignal;\n}\ntype AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = IsAny<ThunkArg,\n// any handling\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// unknown handling\nunknown extends ThunkArg ? (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument not specified or specified as void or undefined\n: [ThunkArg] extends [void] | [undefined] ? (arg?: undefined, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains void\n: [void] extends [ThunkArg] // make optional\n? (arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains undefined\n: [undefined] extends [ThunkArg] ? WithStrictNullChecks<\n// with strict nullChecks: make optional\n(arg?: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\n// without strict null checks this will match everything, so don't make it optional\n(arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>> // default case: normal argument\n: (arg: ThunkArg, config?: AsyncThunkDispatchConfig) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>>;\n\n/**\n * Options object for `createAsyncThunk`.\n *\n * @public\n */\nexport type AsyncThunkOptions<ThunkArg = void, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  /**\n   * A method to control whether the asyncThunk should be executed. Has access to the\n   * `arg`, `api.getState()` and `api.extra` arguments.\n   *\n   * @returns `false` if it should be skipped\n   */\n  condition?(arg: ThunkArg, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): MaybePromise<boolean | undefined>;\n  /**\n   * If `condition` returns `false`, the asyncThunk will be skipped.\n   * This option allows you to control whether a `rejected` action with `meta.condition == false`\n   * will be dispatched or not.\n   *\n   * @default `false`\n   */\n  dispatchConditionRejection?: boolean;\n  serializeError?: (x: unknown) => GetSerializedErrorType<ThunkApiConfig>;\n\n  /**\n   * A function to use when generating the `requestId` for the request sequence.\n   *\n   * @default `nanoid`\n   */\n  idGenerator?: (arg: ThunkArg) => string;\n} & IsUnknown<GetPendingMeta<ThunkApiConfig>, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   *\n   * Using this optional overload will not modify the types correctly, this overload is only in place to support JavaScript users.\n   * Please use the `ThunkApiConfig` parameter `pendingMeta` to get access to a correctly typed overload\n   */\n  getPendingMeta?(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}, {\n  /**\n   * A method to generate additional properties to be added to `meta` of the pending action.\n   */\n  getPendingMeta(base: {\n    arg: ThunkArg;\n    requestId: string;\n  }, api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>): GetPendingMeta<ThunkApiConfig>;\n}>;\nexport type AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[string, ThunkArg, GetPendingMeta<ThunkApiConfig>?], undefined, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'pending';\n} & GetPendingMeta<ThunkApiConfig>>;\nexport type AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Error | null, string, ThunkArg, GetRejectValue<ThunkApiConfig>?, GetRejectedMeta<ThunkApiConfig>?], GetRejectValue<ThunkApiConfig> | undefined, string, GetSerializedErrorType<ThunkApiConfig>, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'rejected';\n  aborted: boolean;\n  condition: boolean;\n} & (({\n  rejectedWithValue: false;\n} & { [K in keyof GetRejectedMeta<ThunkApiConfig>]?: undefined }) | ({\n  rejectedWithValue: true;\n} & GetRejectedMeta<ThunkApiConfig>))>;\nexport type AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig = {}> = ActionCreatorWithPreparedPayload<[Returned, string, ThunkArg, GetFulfilledMeta<ThunkApiConfig>?], Returned, string, never, {\n  arg: ThunkArg;\n  requestId: string;\n  requestStatus: 'fulfilled';\n} & GetFulfilledMeta<ThunkApiConfig>>;\n\n/**\n * A type describing the return value of `createAsyncThunk`.\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\n *\n * @public\n */\nexport type AsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig> = AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig> & {\n  pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig>;\n  rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>;\n  fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>;\n  // matchSettled?\n  settled: (action: any) => action is ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> | AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig>>;\n  typePrefix: string;\n};\nexport type OverrideThunkApiConfigs<OldConfig, NewConfig> = Id<NewConfig & Omit<OldConfig, keyof NewConfig>>;\nexport type CreateAsyncThunkFunction<CurriedThunkApiConfig extends AsyncThunkConfig> = {\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  // separate signature without `AsyncThunkConfig` for better inference\n  <Returned, ThunkArg = void>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, CurriedThunkApiConfig>): AsyncThunk<Returned, ThunkArg, CurriedThunkApiConfig>;\n\n  /**\n   *\n   * @param typePrefix\n   * @param payloadCreator\n   * @param options\n   *\n   * @public\n   */\n  <Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>, options?: AsyncThunkOptions<ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>): AsyncThunk<Returned, ThunkArg, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\ntype CreateAsyncThunk<CurriedThunkApiConfig extends AsyncThunkConfig> = CreateAsyncThunkFunction<CurriedThunkApiConfig> & {\n  withTypes<ThunkApiConfig extends AsyncThunkConfig>(): CreateAsyncThunk<OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n};\nconst externalAbortMessage = 'External signal was aborted';\nexport const createAsyncThunk = /* @__PURE__ */(() => {\n  function createAsyncThunk<Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(typePrefix: string, payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>): AsyncThunk<Returned, ThunkArg, ThunkApiConfig> {\n    type RejectedValue = GetRejectValue<ThunkApiConfig>;\n    type PendingMeta = GetPendingMeta<ThunkApiConfig>;\n    type FulfilledMeta = GetFulfilledMeta<ThunkApiConfig>;\n    type RejectedMeta = GetRejectedMeta<ThunkApiConfig>;\n    const fulfilled: AsyncThunkFulfilledActionCreator<Returned, ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/fulfilled', (payload: Returned, requestId: string, arg: ThunkArg, meta?: FulfilledMeta) => ({\n      payload,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'fulfilled' as const\n      }\n    }));\n    const pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/pending', (requestId: string, arg: ThunkArg, meta?: PendingMeta) => ({\n      payload: undefined,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        requestStatus: 'pending' as const\n      }\n    }));\n    const rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> = createAction(typePrefix + '/rejected', (error: Error | null, requestId: string, arg: ThunkArg, payload?: RejectedValue, meta?: RejectedMeta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || 'Rejected') as GetSerializedErrorType<ThunkApiConfig>,\n      meta: {\n        ...(meta as any || {}),\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: 'rejected' as const,\n        aborted: error?.name === 'AbortError',\n        condition: error?.name === 'ConditionError'\n      }\n    }));\n    function actionCreator(arg: ThunkArg, {\n      signal\n    }: AsyncThunkDispatchConfig = {}): AsyncThunkAction<Returned, ThunkArg, Required<ThunkApiConfig>> {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler: (() => void) | undefined;\n        let abortReason: string | undefined;\n        function abort(reason?: string) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener('abort', () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function () {\n          let finalAction: ReturnType<typeof fulfilled | typeof rejected>;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              // eslint-disable-next-line no-throw-literal\n              throw {\n                name: 'ConditionError',\n                message: 'Aborted due to condition callback returning false.'\n              };\n            }\n            const abortedPromise = new Promise<never>((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: 'AbortError',\n                  message: abortReason || 'Aborted'\n                });\n              };\n              abortController.signal.addEventListener('abort', abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })) as any);\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: ((value: RejectedValue, meta?: RejectedMeta) => {\n                return new RejectWithValue(value, meta);\n              }) as any,\n              fulfillWithValue: ((value: unknown, meta?: FulfilledMeta) => {\n                return new FulfillWithMeta(value, meta);\n              }) as any\n            })).then(result => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result as any, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err as any, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener('abort', abortHandler);\n            }\n          }\n          // We dispatch the result action _after_ the catch, to avoid having any errors\n          // here get swallowed by the try/catch block,\n          // per https://twitter.com/dan_abramov/status/770914221638942720\n          // and https://github.com/reduxjs/redux-toolkit/blob/e85eb17b39a2118d859f7b7746e0f3fee523e089/docs/tutorials/advanced-tutorial.md#async-error-handling-logic-in-thunks\n\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && (finalAction as any).meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction as any);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise as SafePromise<any>, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then<any>(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator as AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig>, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk.withTypes = () => createAsyncThunk;\n  return createAsyncThunk as CreateAsyncThunk<AsyncThunkConfig>;\n})();\ninterface UnwrappableAction {\n  payload: any;\n  meta?: any;\n  error?: any;\n}\ntype UnwrappedActionPayload<T extends UnwrappableAction> = Exclude<T, {\n  error: any;\n}>['payload'];\n\n/**\n * @public\n */\nexport function unwrapResult<R extends UnwrappableAction>(action: R): UnwrappedActionPayload<R> {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\ntype WithStrictNullChecks<True, False> = undefined extends boolean ? False : True;\nfunction isThenable(value: any): value is PromiseLike<any> {\n  return value !== null && typeof value === 'object' && typeof value.then === 'function';\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3, formatProdErrorMessage as _formatProdErrorMessage4, formatProdErrorMessage as _formatProdErrorMessage5, formatProdErrorMessage as _formatProdErrorMessage6, formatProdErrorMessage as _formatProdErrorMessage7, formatProdErrorMessage as _formatProdErrorMessage8 } from \"@reduxjs/toolkit\";\nimport type { Action, Reducer, UnknownAction } from 'redux';\nimport type { Selector } from 'reselect';\nimport type { InjectConfig } from './combineSlices';\nimport type { ActionCreatorWithoutPayload, PayloadAction, PayloadActionCreator, PrepareAction, _ActionCreatorWithPreparedPayload } from './createAction';\nimport { createAction } from './createAction';\nimport type { AsyncThunk, AsyncThunkConfig, AsyncThunkOptions, AsyncThunkPayloadCreator, OverrideThunkApiConfigs } from './createAsyncThunk';\nimport { createAsyncThunk as _createAsyncThunk } from './createAsyncThunk';\nimport type { ActionMatcherDescriptionCollection, CaseReducer, ReducerWithInitialState } from './createReducer';\nimport { createReducer } from './createReducer';\nimport type { ActionReducerMapBuilder, TypedActionCreator } from './mapBuilders';\nimport { executeReducerBuilderCallback } from './mapBuilders';\nimport type { Id, TypeGuard } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\nconst asyncThunkSymbol = /* @__PURE__ */Symbol.for('rtk-slice-createasyncthunk');\n// type is annotated because it's too long to infer\nexport const asyncThunkCreator: {\n  [asyncThunkSymbol]: typeof _createAsyncThunk;\n} = {\n  [asyncThunkSymbol]: _createAsyncThunk\n};\ntype InjectIntoConfig<NewReducerPath extends string> = InjectConfig & {\n  reducerPath?: NewReducerPath;\n};\n\n/**\n * The return value of `createSlice`\n *\n * @public\n */\nexport interface Slice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice name.\n   */\n  name: Name;\n\n  /**\n   *  The slice reducer path.\n   */\n  reducerPath: ReducerPath;\n\n  /**\n   * The slice's reducer.\n   */\n  reducer: Reducer<State>;\n\n  /**\n   * Action creators for the types of actions that are handled by the slice\n   * reducer.\n   */\n  actions: CaseReducerActions<CaseReducers, Name>;\n\n  /**\n   * The individual case reducer functions that were passed in the `reducers` parameter.\n   * This enables reuse and testing if they were defined inline when calling `createSlice`.\n   */\n  caseReducers: SliceDefinedCaseReducers<CaseReducers>;\n\n  /**\n   * Provides access to the initial state value given to the slice.\n   * If a lazy state initializer was provided, it will be called and a fresh value returned.\n   */\n  getInitialState: () => State;\n\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.reducerPath]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.reducerPath])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]: State }>>;\n\n  /**\n   * Inject slice into provided reducer (return value from `combineSlices`), and return injected slice.\n   */\n  injectInto<NewReducerPath extends string = ReducerPath>(this: this, injectable: {\n    inject: (slice: {\n      reducerPath: string;\n      reducer: Reducer;\n    }, config?: InjectConfig) => void;\n  }, config?: InjectIntoConfig<NewReducerPath>): InjectedSlice<State, CaseReducers, Name, NewReducerPath, Selectors>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Will throw an error if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]: State }): State;\n}\n\n/**\n * A slice after being called with `injectInto(reducer)`.\n *\n * Selectors can now be called with an `undefined` value, in which case they use the slice's initial state.\n */\ntype InjectedSlice<State = any, CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> = Omit<Slice<State, CaseReducers, Name, ReducerPath, Selectors>, 'getSelectors' | 'selectors'> & {\n  /**\n   * Get localised slice selectors (expects to be called with *just* the slice's state as the first parameter)\n   */\n  getSelectors(): Id<SliceDefinedSelectors<State, Selectors, State | undefined>>;\n\n  /**\n   * Get globalised slice selectors (`selectState` callback is expected to receive first parameter and return slice state)\n   */\n  getSelectors<RootState>(selectState: (rootState: RootState) => State | undefined): Id<SliceDefinedSelectors<State, Selectors, RootState>>;\n\n  /**\n   * Selectors that assume the slice's state is `rootState[slice.name]` (which is usually the case)\n   *\n   * Equivalent to `slice.getSelectors((state: RootState) => state[slice.name])`.\n   */\n  get selectors(): Id<SliceDefinedSelectors<State, Selectors, { [K in ReducerPath]?: State | undefined }>>;\n\n  /**\n   * Select the slice state, using the slice's current reducerPath.\n   *\n   * Returns initial state if slice is not found.\n   */\n  selectSlice(state: { [K in ReducerPath]?: State | undefined }): State;\n};\n\n/**\n * Options for `createSlice()`.\n *\n * @public\n */\nexport interface CreateSliceOptions<State = any, CR extends SliceCaseReducers<State> = SliceCaseReducers<State>, Name extends string = string, ReducerPath extends string = Name, Selectors extends SliceSelectors<State> = SliceSelectors<State>> {\n  /**\n   * The slice's name. Used to namespace the generated action types.\n   */\n  name: Name;\n\n  /**\n   * The slice's reducer path. Used when injecting into a combined slice reducer.\n   */\n  reducerPath?: ReducerPath;\n\n  /**\n   * The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\n   */\n  initialState: State | (() => State);\n\n  /**\n   * A mapping from action types to action-type-specific *case reducer*\n   * functions. For every action type, a matching action creator will be\n   * generated using `createAction()`.\n   */\n  reducers: ValidateSliceCaseReducers<State, CR> | ((creators: ReducerCreators<State>) => CR);\n\n  /**\n   * A callback that receives a *builder* object to define\n   * case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\n   *\n   *\n   * @example\n  ```ts\n  import { createAction, createSlice, Action } from '@reduxjs/toolkit'\n  const incrementBy = createAction<number>('incrementBy')\n  const decrement = createAction('decrement')\n  interface RejectedAction extends Action {\n  error: Error\n  }\n  function isRejectedAction(action: Action): action is RejectedAction {\n  return action.type.endsWith('rejected')\n  }\n  createSlice({\n  name: 'counter',\n  initialState: 0,\n  reducers: {},\n  extraReducers: builder => {\n    builder\n      .addCase(incrementBy, (state, action) => {\n        // action is inferred correctly here if using TS\n      })\n      // You can chain calls, or have separate `builder.addCase()` lines each time\n      .addCase(decrement, (state, action) => {})\n      // You can match a range of action types\n      .addMatcher(\n        isRejectedAction,\n        // `action` will be inferred as a RejectedAction due to isRejectedAction being defined as a type guard\n        (state, action) => {}\n      )\n      // and provide a default case if no other handlers matched\n      .addDefaultCase((state, action) => {})\n    }\n  })\n  ```\n   */\n  extraReducers?: (builder: ActionReducerMapBuilder<State>) => void;\n\n  /**\n   * A map of selectors that receive the slice's state and any additional arguments, and return a result.\n   */\n  selectors?: Selectors;\n}\nexport enum ReducerType {\n  reducer = 'reducer',\n  reducerWithPrepare = 'reducerWithPrepare',\n  asyncThunk = 'asyncThunk',\n}\ntype ReducerDefinition<T extends ReducerType = ReducerType> = {\n  _reducerDefinitionType: T;\n};\nexport type CaseReducerDefinition<S = any, A extends Action = UnknownAction> = CaseReducer<S, A> & ReducerDefinition<ReducerType.reducer>;\n\n/**\n * A CaseReducer with a `prepare` method.\n *\n * @public\n */\nexport type CaseReducerWithPrepare<State, Action extends PayloadAction> = {\n  reducer: CaseReducer<State, Action>;\n  prepare: PrepareAction<Action['payload']>;\n};\nexport interface CaseReducerWithPrepareDefinition<State, Action extends PayloadAction> extends CaseReducerWithPrepare<State, Action>, ReducerDefinition<ReducerType.reducerWithPrepare> {}\ntype AsyncThunkSliceReducerConfig<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = {\n  pending?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['pending']>>;\n  rejected?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected']>>;\n  fulfilled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['fulfilled']>>;\n  settled?: CaseReducer<State, ReturnType<AsyncThunk<Returned, ThunkArg, ThunkApiConfig>['rejected' | 'fulfilled']>>;\n  options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>;\n};\ntype AsyncThunkSliceReducerDefinition<State, ThunkArg extends any, Returned = unknown, ThunkApiConfig extends AsyncThunkConfig = {}> = AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig> & ReducerDefinition<ReducerType.asyncThunk> & {\n  payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>;\n};\n\n/**\n * Providing these as part of the config would cause circular types, so we disallow passing them\n */\ntype PreventCircular<ThunkApiConfig> = { [K in keyof ThunkApiConfig]: K extends 'state' | 'dispatch' ? never : ThunkApiConfig[K] };\ninterface AsyncThunkCreator<State, CurriedThunkApiConfig extends PreventCircular<AsyncThunkConfig> = PreventCircular<AsyncThunkConfig>> {\n  <Returned, ThunkArg = void>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, CurriedThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, CurriedThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, CurriedThunkApiConfig>;\n  <Returned, ThunkArg, ThunkApiConfig extends PreventCircular<AsyncThunkConfig> = {}>(payloadCreator: AsyncThunkPayloadCreator<Returned, ThunkArg, ThunkApiConfig>, config?: AsyncThunkSliceReducerConfig<State, ThunkArg, Returned, ThunkApiConfig>): AsyncThunkSliceReducerDefinition<State, ThunkArg, Returned, ThunkApiConfig>;\n  withTypes<ThunkApiConfig extends PreventCircular<AsyncThunkConfig>>(): AsyncThunkCreator<State, OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>>;\n}\nexport interface ReducerCreators<State> {\n  reducer(caseReducer: CaseReducer<State, PayloadAction>): CaseReducerDefinition<State, PayloadAction>;\n  reducer<Payload>(caseReducer: CaseReducer<State, PayloadAction<Payload>>): CaseReducerDefinition<State, PayloadAction<Payload>>;\n  asyncThunk: AsyncThunkCreator<State>;\n  preparedReducer<Prepare extends PrepareAction<any>>(prepare: Prepare, reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>): {\n    _reducerDefinitionType: ReducerType.reducerWithPrepare;\n    prepare: Prepare;\n    reducer: CaseReducer<State, ReturnType<_ActionCreatorWithPreparedPayload<Prepare>>>;\n  };\n}\n\n/**\n * The type describing a slice's `reducers` option.\n *\n * @public\n */\nexport type SliceCaseReducers<State> = Record<string, ReducerDefinition> | Record<string, CaseReducer<State, PayloadAction<any>> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>>;\n\n/**\n * The type describing a slice's `selectors` option.\n */\nexport type SliceSelectors<State> = {\n  [K: string]: (sliceState: State, ...args: any[]) => any;\n};\ntype SliceActionType<SliceName extends string, ActionName extends keyof any> = ActionName extends string | number ? `${SliceName}/${ActionName}` : string;\n\n/**\n * Derives the slice's `actions` property from the `reducers` options\n *\n * @public\n */\nexport type CaseReducerActions<CaseReducers extends SliceCaseReducers<any>, SliceName extends string> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends {\n  prepare: any;\n} ? ActionCreatorForCaseReducerWithPrepare<Definition, SliceActionType<SliceName, Type>> : Definition extends AsyncThunkSliceReducerDefinition<any, infer ThunkArg, infer Returned, infer ThunkApiConfig> ? AsyncThunk<Returned, ThunkArg, ThunkApiConfig> : Definition extends {\n  reducer: any;\n} ? ActionCreatorForCaseReducer<Definition['reducer'], SliceActionType<SliceName, Type>> : ActionCreatorForCaseReducer<Definition, SliceActionType<SliceName, Type>> : never };\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducerWithPrepare`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducerWithPrepare<CR extends {\n  prepare: any;\n}, Type extends string> = _ActionCreatorWithPreparedPayload<CR['prepare'], Type>;\n\n/**\n * Get a `PayloadActionCreator` type for a passed `CaseReducer`\n *\n * @internal\n */\ntype ActionCreatorForCaseReducer<CR, Type extends string> = CR extends ((state: any, action: infer Action) => any) ? Action extends {\n  payload: infer P;\n} ? PayloadActionCreator<P, Type> : ActionCreatorWithoutPayload<Type> : ActionCreatorWithoutPayload<Type>;\n\n/**\n * Extracts the CaseReducers out of a `reducers` object, even if they are\n * tested into a `CaseReducerWithPrepare`.\n *\n * @internal\n */\ntype SliceDefinedCaseReducers<CaseReducers extends SliceCaseReducers<any>> = { [Type in keyof CaseReducers]: CaseReducers[Type] extends infer Definition ? Definition extends AsyncThunkSliceReducerDefinition<any, any, any> ? Id<Pick<Required<Definition>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>> : Definition extends {\n  reducer: infer Reducer;\n} ? Reducer : Definition : never };\ntype RemappedSelector<S extends Selector, NewState> = S extends Selector<any, infer R, infer P> ? Selector<NewState, R, P> & {\n  unwrapped: S;\n} : never;\n\n/**\n * Extracts the final selector type from the `selectors` object.\n *\n * Removes the `string` index signature from the default value.\n */\ntype SliceDefinedSelectors<State, Selectors extends SliceSelectors<State>, RootState> = { [K in keyof Selectors as string extends K ? never : K]: RemappedSelector<Selectors[K], RootState> };\n\n/**\n * Used on a SliceCaseReducers object.\n * Ensures that if a CaseReducer is a `CaseReducerWithPrepare`, that\n * the `reducer` and the `prepare` function use the same type of `payload`.\n *\n * Might do additional such checks in the future.\n *\n * This type is only ever useful if you want to write your own wrapper around\n * `createSlice`. Please don't use it otherwise!\n *\n * @public\n */\nexport type ValidateSliceCaseReducers<S, ACR extends SliceCaseReducers<S>> = ACR & { [T in keyof ACR]: ACR[T] extends {\n  reducer(s: S, action?: infer A): any;\n} ? {\n  prepare(...a: never[]): Omit<A, 'type'>;\n} : {} };\nfunction getType(slice: string, actionKey: string): string {\n  return `${slice}/${actionKey}`;\n}\ninterface BuildCreateSliceConfig {\n  creators?: {\n    asyncThunk?: typeof asyncThunkCreator;\n  };\n}\nexport function buildCreateSlice({\n  creators\n}: BuildCreateSliceConfig = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice<State, CaseReducers extends SliceCaseReducers<State>, Name extends string, Selectors extends SliceSelectors<State>, ReducerPath extends string = Name>(options: CreateSliceOptions<State, CaseReducers, Name, ReducerPath, Selectors>): Slice<State, CaseReducers, Name, ReducerPath, Selectors> {\n    const {\n      name,\n      reducerPath = name as unknown as ReducerPath\n    } = options;\n    if (!name) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(11) : '`name` is a required option for createSlice');\n    }\n    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n      if (options.initialState === undefined) {\n        console.error('You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`');\n      }\n    }\n    const reducers = (typeof options.reducers === 'function' ? options.reducers(buildReducerCreators<State>()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context: ReducerHandlingContext<State> = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods: ReducerHandlingContextMethods<State> = {\n      addCase(typeOrActionCreator: string | TypedActionCreator<any>, reducer: CaseReducer<State>) {\n        const type = typeof typeOrActionCreator === 'string' ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(12) : '`context.addCase` cannot be called with an empty action type');\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(13) : '`context.addCase` cannot be called with two reducers for the same action type: ' + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer\n        });\n        return contextMethods;\n      },\n      exposeAction(name, actionCreator) {\n        context.actionCreators[name] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name, reducer) {\n        context.sliceCaseReducersByName[name] = reducer;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach(reducerName => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails: ReducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === 'function'\n      };\n      if (isAsyncThunkSliceReducerDefinition<State>(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition<State>(reducerDetails, reducerDefinition as any, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (process.env.NODE_ENV !== 'production') {\n        if (typeof options.extraReducers === 'object') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage4(14) : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = undefined] = typeof options.extraReducers === 'function' ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, builder => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key] as CaseReducer<any>);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state: State) => state;\n    const injectedSelectorCache = new Map<boolean, WeakMap<(rootState: any) => State | undefined, Record<string, (rootState: any) => any>>>();\n    const injectedStateCache = new WeakMap<(rootState: any) => State, State>();\n    let _reducer: ReducerWithInitialState<State>;\n    function reducer(state: State | undefined, action: UnknownAction) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps<CurrentReducerPath extends string = ReducerPath>(reducerPath: CurrentReducerPath, injected = false): Pick<Slice<State, CaseReducers, Name, CurrentReducerPath, Selectors>, 'getSelectors' | 'selectors' | 'selectSlice' | 'reducerPath'> {\n      function selectSlice(state: { [K in CurrentReducerPath]: State }) {\n        let sliceState = state[reducerPath];\n        if (typeof sliceState === 'undefined') {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (process.env.NODE_ENV !== 'production') {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage5(15) : 'selectSlice returned undefined for an uninjected slice reducer');\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState: (rootState: any) => State = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map: Record<string, Selector<any, any>> = {};\n          for (const [name, selector] of Object.entries(options.selectors ?? {})) {\n            map[name] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        }) as any;\n      }\n      return {\n        reducerPath,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice: Slice<State, CaseReducers, Name, ReducerPath, Selectors> = {\n      name,\n      reducer,\n      actions: context.actionCreators as any,\n      caseReducers: context.sliceCaseReducersByName as any,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        } as any;\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector<State, NewState, S extends Selector<State>>(selector: S, selectState: Selector<NewState, State>, getInitialState: () => State, injected?: boolean) {\n  function wrapper(rootState: NewState, ...args: any[]) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === 'undefined') {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (process.env.NODE_ENV !== 'production') {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage6(16) : 'selectState returned undefined for an uninjected slice reducer');\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper as RemappedSelector<S, NewState>;\n}\n\n/**\n * A function that accepts an initial state, an object full of reducer\n * functions, and a \"slice name\", and automatically generates\n * action creators and action types that correspond to the\n * reducers and state.\n *\n * @public\n */\nexport const createSlice = /* @__PURE__ */buildCreateSlice();\ninterface ReducerHandlingContext<State> {\n  sliceCaseReducersByName: Record<string, CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>>;\n  sliceCaseReducersByType: Record<string, CaseReducer<State, any>>;\n  sliceMatchers: ActionMatcherDescriptionCollection<State>;\n  actionCreators: Record<string, Function>;\n}\ninterface ReducerHandlingContextMethods<State> {\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<ActionCreator extends TypedActionCreator<string>>(actionCreator: ActionCreator, reducer: CaseReducer<State, ReturnType<ActionCreator>>): ReducerHandlingContextMethods<State>;\n  /**\n   * Adds a case reducer to handle a single action type.\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\n   * @param reducer - The actual case reducer function.\n   */\n  addCase<Type extends string, A extends Action<Type>>(type: Type, reducer: CaseReducer<State, A>): ReducerHandlingContextMethods<State>;\n\n  /**\n   * Allows you to match incoming actions against your own filter function instead of only the `action.type` property.\n   * @remarks\n   * If multiple matcher reducers match, all of them will be executed in the order\n   * they were defined in - even if a case reducer already matched.\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\n   *   function\n   * @param reducer - The actual case reducer function.\n   *\n   */\n  addMatcher<A>(matcher: TypeGuard<A>, reducer: CaseReducer<State, A extends Action ? A : A & Action>): ReducerHandlingContextMethods<State>;\n  /**\n   * Add an action to be exposed under the final `slice.actions` key.\n   * @param name The key to be exposed as.\n   * @param actionCreator The action to expose.\n   * @example\n   * context.exposeAction(\"addPost\", createAction<Post>(\"addPost\"));\n   *\n   * export const { addPost } = slice.actions\n   *\n   * dispatch(addPost(post))\n   */\n  exposeAction(name: string, actionCreator: Function): ReducerHandlingContextMethods<State>;\n  /**\n   * Add a case reducer to be exposed under the final `slice.caseReducers` key.\n   * @param name The key to be exposed as.\n   * @param reducer The reducer to expose.\n   * @example\n   * context.exposeCaseReducer(\"addPost\", (state, action: PayloadAction<Post>) => {\n   *   state.push(action.payload)\n   * })\n   *\n   * slice.caseReducers.addPost([], addPost(post))\n   */\n  exposeCaseReducer(name: string, reducer: CaseReducer<State, any> | Pick<AsyncThunkSliceReducerDefinition<State, any, any, any>, 'fulfilled' | 'rejected' | 'pending' | 'settled'>): ReducerHandlingContextMethods<State>;\n}\ninterface ReducerDetails {\n  /** The key the reducer was defined under */\n  reducerName: string;\n  /** The predefined action type, i.e. `${slice.name}/${reducerName}` */\n  type: string;\n  /** Whether create. notation was used when defining reducers */\n  createNotation: boolean;\n}\nfunction buildReducerCreators<State>(): ReducerCreators<State> {\n  function asyncThunk(payloadCreator: AsyncThunkPayloadCreator<any, any>, config: AsyncThunkSliceReducerConfig<State, any>): AsyncThunkSliceReducerDefinition<State, any> {\n    return {\n      _reducerDefinitionType: ReducerType.asyncThunk,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer: CaseReducer<State, any>) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args: Parameters<typeof caseReducer>) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: ReducerType.reducer\n      } as const);\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: ReducerType.reducerWithPrepare,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk: asyncThunk as any\n  };\n}\nfunction handleNormalReducerDefinition<State>({\n  type,\n  reducerName,\n  createNotation\n}: ReducerDetails, maybeReducerWithPrepare: CaseReducer<State, {\n  payload: any;\n  type: string;\n}> | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>, context: ReducerHandlingContextMethods<State>) {\n  let caseReducer: CaseReducer<State, any>;\n  let prepareCallback: PrepareAction<any> | undefined;\n  if ('reducer' in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage7(17) : 'Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.');\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition<State>(reducerDefinition: any): reducerDefinition is AsyncThunkSliceReducerDefinition<State, any, any, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.asyncThunk;\n}\nfunction isCaseReducerWithPrepareDefinition<State>(reducerDefinition: any): reducerDefinition is CaseReducerWithPrepareDefinition<State, any> {\n  return reducerDefinition._reducerDefinitionType === ReducerType.reducerWithPrepare;\n}\nfunction handleThunkCaseReducerDefinition<State>({\n  type,\n  reducerName\n}: ReducerDetails, reducerDefinition: AsyncThunkSliceReducerDefinition<State, any, any, any>, context: ReducerHandlingContextMethods<State>, cAT: typeof _createAsyncThunk | undefined) {\n  if (!cAT) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage8(18) : 'Cannot use `create.asyncThunk` in the built-in `createSlice`. ' + 'Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.');\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options as any);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {}", "import type { EntityId, EntityState, EntityStateAdapter, EntityStateFactory } from './models';\nexport function getInitialEntityState<T, Id extends EntityId>(): EntityState<T, Id> {\n  return {\n    ids: [],\n    entities: {} as Record<Id, T>\n  };\n}\nexport function createInitialStateFactory<T, Id extends EntityId>(stateAdapter: EntityStateAdapter<T, Id>): EntityStateFactory<T, Id> {\n  function getInitialState(state?: undefined, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id>;\n  function getInitialState<S extends object>(additionalState: S, entities?: readonly T[] | Record<Id, T>): EntityState<T, Id> & S;\n  function getInitialState(additionalState: any = {}, entities?: readonly T[] | Record<Id, T>): any {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}", "import type { CreateSelectorFunction, Selector } from 'reselect';\nimport { createDraftSafeSelector } from '../createDraftSafeSelector';\nimport type { EntityId, EntitySelectors, EntityState } from './models';\ntype AnyFunction = (...args: any) => any;\ntype AnyCreateSelectorFunction = CreateSelectorFunction<<F extends AnyFunction>(f: F) => F, <F extends AnyFunction>(f: F) => F>;\nexport type GetSelectorsOptions = {\n  createSelector?: AnyCreateSelectorFunction;\n};\nexport function createSelectorsFactory<T, Id extends EntityId>() {\n  function getSelectors(selectState?: undefined, options?: GetSelectorsOptions): EntitySelectors<T, EntityState<T, Id>, Id>;\n  function getSelectors<V>(selectState: (state: V) => EntityState<T, Id>, options?: GetSelectorsOptions): EntitySelectors<T, V, Id>;\n  function getSelectors<V>(selectState?: (state: V) => EntityState<T, Id>, options: GetSelectorsOptions = {}): EntitySelectors<T, any, Id> {\n    const {\n      createSelector = createDraftSafeSelector as AnyCreateSelectorFunction\n    } = options;\n    const selectIds = (state: EntityState<T, Id>) => state.ids;\n    const selectEntities = (state: EntityState<T, Id>) => state.entities;\n    const selectAll = createSelector(selectIds, selectEntities, (ids, entities): T[] => ids.map(id => entities[id]!));\n    const selectId = (_: unknown, id: Id) => id;\n    const selectById = (entities: Record<Id, T>, id: Id) => entities[id];\n    const selectTotal = createSelector(selectIds, ids => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector(selectState as Selector<V, EntityState<T, Id>>, selectEntities);\n    return {\n      selectIds: createSelector(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector(selectState, selectAll),\n      selectTotal: createSelector(selectState, selectTotal),\n      selectById: createSelector(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}", "import { produce as createNextState, isDraft } from 'immer';\nimport type { Draft } from 'immer';\nimport type { EntityId, DraftableEntityState, PreventAny } from './models';\nimport type { PayloadAction } from '../createAction';\nimport { isFSA } from '../createAction';\nexport const isDraftTyped = isDraft as <T>(value: T | Draft<T>) => value is Draft<T>;\nexport function createSingleArgumentStateOperator<T, Id extends EntityId>(mutator: (state: DraftableEntityState<T, Id>) => void) {\n  const operator = createStateOperator((_: undefined, state: DraftableEntityState<T, Id>) => mutator(state));\n  return function operation<S extends DraftableEntityState<T, Id>>(state: PreventAny<S, T, Id>): S {\n    return operator(state as S, undefined);\n  };\n}\nexport function createStateOperator<T, Id extends EntityId, R>(mutator: (arg: R, state: DraftableEntityState<T, Id>) => void) {\n  return function operation<S extends DraftableEntityState<T, Id>>(state: S, arg: R | PayloadAction<R>): S {\n    function isPayloadActionArgument(arg: R | PayloadAction<R>): arg is PayloadAction<R> {\n      return isFSA(arg);\n    }\n    const runMutator = (draft: DraftableEntityState<T, Id>) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped<DraftableEntityState<T, Id>>(state)) {\n      // we must already be inside a `createNextState` call, likely because\n      // this is being wrapped in `createReducer` or `createSlice`.\n      // It's safe to just pass the draft to the mutator.\n      runMutator(state);\n\n      // since it's a draft, we'll just return it\n      return state;\n    }\n    return createNextState(state, runMutator);\n  };\n}", "import type { Draft } from 'immer';\nimport { current, isDraft } from 'immer';\nimport type { DraftableEntityState, EntityId, IdSelector, Update } from './models';\nexport function selectIdValue<T, Id extends EntityId>(entity: T, selectId: IdSelector<T, Id>) {\n  const key = selectId(entity);\n  if (process.env.NODE_ENV !== 'production' && key === undefined) {\n    console.warn('The entity passed to the `selectId` implementation returned undefined.', 'You should probably provide your own `selectId` implementation.', 'The entity that was passed:', entity, 'The `selectId` implementation:', selectId.toString());\n  }\n  return key;\n}\nexport function ensureEntitiesArray<T, Id extends EntityId>(entities: readonly T[] | Record<Id, T>): readonly T[] {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nexport function getCurrent<T>(value: T | Draft<T>): T {\n  return (isDraft(value) ? current(value) : value) as T;\n}\nexport function splitAddedUpdatedEntities<T, Id extends EntityId>(newEntities: readonly T[] | Record<Id, T>, selectId: IdSelector<T, Id>, state: DraftableEntityState<T, Id>): [T[], Update<T, Id>[], Id[]] {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set<Id>(existingIdsArray);\n  const added: T[] = [];\n  const addedIds = new Set<Id>([]);\n  const updated: Update<T, Id>[] = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}", "import type { Draft } from 'immer';\nimport type { EntityStateAdapter, IdSelector, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator, createSingleArgumentStateOperator } from './state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities } from './utils';\nexport function createUnsortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  function addOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key as Id & Draft<Id>);\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key as Id & Draft<Id>);\n    }\n    ;\n    (state.entities as Record<Id, T>)[key] = entity;\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {} as Record<Id, T>;\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key: Id, state: R): void {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys: readonly Id[], state: R): void {\n    let didMutate = false;\n    keys.forEach(key => {\n      if (key in state.entities) {\n        delete (state.entities as Record<Id, T>)[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = (state.ids as Id[]).filter(id => id in state.entities) as Id[] | Draft<Id[]>;\n    }\n  }\n  function removeAllMutably(state: R): void {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys: {\n    [id: string]: Id;\n  }, update: Update<T, Id>, state: R): boolean {\n    const original: T | undefined = (state.entities as Record<Id, T>)[update.id];\n    if (original === undefined) {\n      return false;\n    }\n    const updated: T = Object.assign({}, original, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete (state.entities as Record<Id, T>)[update.id];\n    }\n    ;\n    (state.entities as Record<Id, T>)[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    const newKeys: {\n      [id: string]: Id;\n    } = {};\n    const updatesPerEntity: {\n      [id: string]: Update<T, Id>;\n    } = {};\n    updates.forEach(update => {\n      // Only apply updates to entities that currently exist\n      if (update.id in state.entities) {\n        // If there are multiple updates to one entity, merge them together\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter(update => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map(e => selectIdValue(e as T, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}", "import type { IdSelector, Comparer, EntityStateAdapter, Update, EntityId, DraftableEntityState } from './models';\nimport { createStateOperator } from './state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport { selectIdValue, ensureEntitiesArray, splitAddedUpdatedEntities, getCurrent } from './utils';\n\n// Borrowed from Replay\nexport function findInsertIndex<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): number {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nexport function insert<T>(sortedItems: T[], item: T, comparisonFunction: Comparer<T>): T[] {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nexport function createSortedStateAdapter<T, Id extends EntityId>(selectId: IdSelector<T, Id>, comparer: Comparer<T>): EntityStateAdapter<T, Id> {\n  type R = DraftableEntityState<T, Id>;\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity: T, state: R): void {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R, existingIds?: Id[]): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set<Id>(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter(model => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity: T, state: R): void {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete (state.entities as Record<Id, T>)[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {} as Record<Id, T>;\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update: Update<T, Id>, state: R): void {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates: ReadonlyArray<Update<T, Id>>, state: R): void {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity: T | undefined = (state.entities as Record<Id, T>)[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        // We do support the case where updates can change an item's ID.\n        // This makes things trickier - go ahead and swap the IDs in state now.\n        replacedIds = true;\n        delete (state.entities as Record<Id, T>)[update.id];\n        const oldIndex = (state.ids as Id[]).indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        (state.entities as Record<Id, T>)[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity: T, state: R): void {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities: readonly T[] | Record<Id, T>, state: R): void {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities<T, Id>(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a: readonly unknown[], b: readonly unknown[]) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  type MergeFunction = (state: R, addedItems: readonly T[], appliedUpdates?: boolean, replacedIds?: boolean) => void;\n  const mergeFunction: MergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities as Record<Id, T>;\n    let ids: Iterable<Id> = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities: T[] = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n\n    // Insert/overwrite all new/updated\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        // Binary search insertion generally requires fewer comparisons\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      // All we have is the incoming values, sort them\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      // We should have a _mostly_-sorted array already\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}", "import type { EntityAdapter, EntityId, EntityAdapterOptions } from './models';\nimport { createInitialStateFactory } from './entity_state';\nimport { createSelectorsFactory } from './state_selectors';\nimport { createSortedStateAdapter } from './sorted_state_adapter';\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter';\nimport type { WithRequiredProp } from '../tsHelpers';\nexport function createEntityAdapter<T, Id extends EntityId>(options: WithRequiredProp<EntityAdapterOptions<T, Id>, 'selectId'>): EntityAdapter<T, Id>;\nexport function createEntityAdapter<T extends {\n  id: EntityId;\n}>(options?: Omit<EntityAdapterOptions<T, T['id']>, 'selectId'>): EntityAdapter<T, T['id']>;\n\n/**\n *\n * @param options\n *\n * @public\n */\nexport function createEntityAdapter<T>(options: EntityAdapterOptions<T, EntityId> = {}): EntityAdapter<T, EntityId> {\n  const {\n    selectId,\n    sortComparer\n  }: Required<EntityAdapterOptions<T, EntityId>> = {\n    sortComparer: false,\n    selectId: (instance: any) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory<T, EntityId>();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2, formatProdErrorMessage as _formatProdErrorMessage3 } from \"@reduxjs/toolkit\";\nimport type { Action, Dispatch, MiddlewareAPI, UnknownAction } from 'redux';\nimport { isAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport { createAction } from '../createAction';\nimport { nanoid } from '../nanoid';\nimport { TaskAbortError, listenerCancelled, listenerCompleted, taskCancelled, taskCompleted } from './exceptions';\nimport { createDelay, createPause, raceWithSignal, runTask, validateActive } from './task';\nimport type { AbortSignalWithReason, AddListenerOverloads, AnyListenerPredicate, CreateListenerMiddlewareOptions, FallbackAddListenerOptions, ForkOptions, ForkedTask, ForkedTaskExecutor, ListenerEntry, ListenerErrorHandler, ListenerErrorInfo, ListenerMiddleware, ListenerMiddlewareInstance, TakePattern, TaskResult, TypedAddListener, TypedCreateListenerEntry, TypedRemoveListener, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\nimport { abortControllerWithReason, addAbortSignalListener, assertFunction, catchRejection, noop } from './utils';\nexport { TaskAbortError } from './exceptions';\nexport type { AsyncTaskExecutor, CreateListenerMiddlewareOptions, ForkedTask, ForkedTaskAPI, ForkedTaskExecutor, ListenerEffect, ListenerEffectAPI, ListenerErrorHandler, ListenerMiddleware, ListenerMiddlewareInstance, SyncTaskExecutor, TaskCancelled, TaskRejected, TaskResolved, TaskResult, TypedAddListener, TypedRemoveListener, TypedStartListening, TypedStopListening, UnsubscribeListener, UnsubscribeListenerOptions } from './types';\n\n//Overly-aggressive byte-shaving\nconst {\n  assign\n} = Object;\n/**\n * @internal\n */\nconst INTERNAL_NIL_TOKEN = {} as const;\nconst alm = 'listenerMiddleware' as const;\nconst createFork = (parentAbortSignal: AbortSignalWithReason<unknown>, parentBlockingPromises: Promise<any>[]) => {\n  const linkControllers = (controller: AbortController) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return <T,>(taskExecutor: ForkedTaskExecutor<T>, opts?: ForkOptions): ForkedTask<T> => {\n    assertFunction(taskExecutor, 'taskExecutor');\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask<T>(async (): Promise<T> => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result = (await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      })) as T;\n      validateActive(childAbortController.signal);\n      return result;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop));\n    }\n    return {\n      result: createPause<TaskResult<T>>(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nconst createTakePattern = <S,>(startListening: AddListenerOverloads<UnsubscribeListener, S, Dispatch>, signal: AbortSignal): TakePattern<S> => {\n  /**\n   * A function that takes a ListenerPredicate and an optional timeout,\n   * and resolves when either the predicate returns `true` based on an action\n   * state combination or when the timeout expires.\n   * If the parent listener is canceled while waiting, this will throw a\n   * TaskAbortError.\n   */\n  const take = async <P extends AnyListenerPredicate<S>,>(predicate: P, timeout: number | undefined) => {\n    validateActive(signal);\n\n    // Placeholder unsubscribe function until the listener is added\n    let unsubscribe: UnsubscribeListener = () => {};\n    const tuplePromise = new Promise<[Action, S, S]>((resolve, reject) => {\n      // Inside the Promise, we synchronously add the listener.\n      let stopListening = startListening({\n        predicate: predicate as any,\n        effect: (action, listenerApi): void => {\n          // One-shot listener that cleans up as soon as the predicate passes\n          listenerApi.unsubscribe();\n          // Resolve the promise with the same arguments the predicate saw\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises: (Promise<null> | Promise<[Action, S, S]>)[] = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise<null>(resolve => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      // Always clean up the listener\n      unsubscribe();\n    }\n  };\n  return ((predicate: AnyListenerPredicate<S>, timeout: number | undefined) => catchRejection(take(predicate, timeout))) as TakePattern<S>;\n};\nconst getListenerEntryPropsFrom = (options: FallbackAddListenerOptions) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator!.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n    // pass\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(21) : 'Creating or removing a listener requires one of the known fields for matching an action');\n  }\n  assertFunction(effect, 'options.listener');\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\n\n/** Accepts the possible options for creating a listener, and returns a formatted listener entry */\nexport const createListenerEntry: TypedCreateListenerEntry<unknown> = /* @__PURE__ */assign((options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry: ListenerEntry<unknown> = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: new Set<AbortController>(),\n    unsubscribe: () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(22) : 'Unsubscribe not initialized');\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n}) as unknown as TypedCreateListenerEntry<unknown>;\nconst findListenerEntry = (listenerMap: Map<string, ListenerEntry>, options: FallbackAddListenerOptions) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find(entry => {\n    const matchPredicateOrType = typeof type === 'string' ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nconst cancelActiveListeners = (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>) => {\n  entry.pending.forEach(controller => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nconst createClearListenerMiddleware = (listenerMap: Map<string, ListenerEntry>) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\n\n/**\n * Safely reports errors to the `errorHandler` provided.\n * Errors that occur inside `errorHandler` are notified in a new task.\n * Inspired by [rxjs reportUnhandledError](https://github.com/ReactiveX/rxjs/blob/6fafcf53dc9e557439b25debaeadfd224b245a66/src/internal/util/reportUnhandledError.ts)\n * @param errorHandler\n * @param errorToNotify\n */\nconst safelyNotifyError = (errorHandler: ListenerErrorHandler, errorToNotify: unknown, errorInfo: ListenerErrorInfo): void => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    // We cannot let an error raised here block the listener queue.\n    // The error raised here will be picked up by `window.onerror`, `process.on('error')` etc...\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\n\n/**\n * @public\n */\nexport const addListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/add`), {\n  withTypes: () => addListener\n}) as unknown as TypedAddListener<unknown>;\n\n/**\n * @public\n */\nexport const clearAllListeners = /* @__PURE__ */createAction(`${alm}/removeAll`);\n\n/**\n * @public\n */\nexport const removeListener = /* @__PURE__ */assign(/* @__PURE__ */createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n}) as unknown as TypedRemoveListener<unknown>;\nconst defaultErrorHandler: ListenerErrorHandler = (...args: unknown[]) => {\n  console.error(`${alm}/error`, ...args);\n};\n\n/**\n * @public\n */\nexport const createListenerMiddleware = <StateType = unknown, DispatchType extends Dispatch<Action> = ThunkDispatch<StateType, unknown, UnknownAction>, ExtraArgument = unknown>(middlewareOptions: CreateListenerMiddlewareOptions<ExtraArgument> = {}) => {\n  const listenerMap = new Map<string, ListenerEntry>();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, 'onError');\n  const insertEntry = (entry: ListenerEntry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions?: UnsubscribeListenerOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = ((options: FallbackAddListenerOptions) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options as any);\n    return insertEntry(entry);\n  }) as AddListenerOverloads<any>;\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options: FallbackAddListenerOptions & UnsubscribeListenerOptions): boolean => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry: ListenerEntry<unknown, Dispatch<UnknownAction>>, action: unknown, api: MiddlewareAPI, getOriginalState: () => StateType) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening as AddListenerOverloads<any>, internalTaskController.signal);\n    const autoJoinPromises: Promise<any>[] = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(action,\n      // Use assign() rather than ... to avoid extra helper functions added to bundle\n      assign({}, api, {\n        getOriginalState,\n        condition: (predicate: AnyListenerPredicate<any>, timeout?: number) => take(predicate, timeout).then(Boolean),\n        take,\n        delay: createDelay(internalTaskController.signal),\n        pause: createPause<any>(internalTaskController.signal),\n        extra,\n        signal: internalTaskController.signal,\n        fork: createFork(internalTaskController.signal, autoJoinPromises),\n        unsubscribe: entry.unsubscribe,\n        subscribe: () => {\n          listenerMap.set(entry.id, entry);\n        },\n        cancelActiveListeners: () => {\n          entry.pending.forEach((controller, _, set) => {\n            if (controller !== internalTaskController) {\n              abortControllerWithReason(controller, listenerCancelled);\n              set.delete(controller);\n            }\n          });\n        },\n        cancel: () => {\n          abortControllerWithReason(internalTaskController, listenerCancelled);\n          entry.pending.delete(internalTaskController);\n        },\n        throwIfCancelled: () => {\n          validateActive(internalTaskController.signal);\n        }\n      })));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: 'effect'\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted); // Notify that the task has completed\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware: ListenerMiddleware<StateType, DispatchType, ExtraArgument> = api => next => action => {\n    if (!isAction(action)) {\n      // we only want to notify listeners for action objects\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload as any);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n\n    // Need to get this state _before_ the reducer processes the action\n    let originalState: StateType | typeof INTERNAL_NIL_TOKEN = api.getState();\n\n    // `getOriginalState` can only be called synchronously.\n    // @see https://github.com/reduxjs/redux-toolkit/discussions/1648#discussioncomment-1932820\n    const getOriginalState = (): StateType => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage3(23) : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState as StateType;\n    };\n    let result: unknown;\n    try {\n      // Actually forward the action to the reducer before we handle listeners\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        // Work around ESBuild+TS transpilation issue\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: 'predicate'\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      // Remove `originalState` store from this scope.\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  } as ListenerMiddlewareInstance<StateType, DispatchType, ExtraArgument>;\n};", "import type { SerializedError } from '@reduxjs/toolkit';\nconst task = 'task';\nconst listener = 'listener';\nconst completed = 'completed';\nconst cancelled = 'cancelled';\n\n/* TaskAbortError error codes  */\nexport const taskCancelled = `task-${cancelled}` as const;\nexport const taskCompleted = `task-${completed}` as const;\nexport const listenerCancelled = `${listener}-${cancelled}` as const;\nexport const listenerCompleted = `${listener}-${completed}` as const;\nexport class TaskAbortError implements SerializedError {\n  name = 'TaskAbortError';\n  message: string;\n  constructor(public code: string | undefined) {\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { AbortSignalWithReason } from './types';\nexport const assertFunction: (func: unknown, expected: string) => asserts func is (...args: unknown[]) => unknown = (func: unknown, expected: string) => {\n  if (typeof func !== 'function') {\n    throw new TypeError(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(32) : `${expected} is not a function`);\n  }\n};\nexport const noop = () => {};\nexport const catchRejection = <T,>(promise: Promise<T>, onError = noop): Promise<T> => {\n  promise.catch(onError);\n  return promise;\n};\nexport const addAbortSignalListener = (abortSignal: AbortSignal, callback: (evt: Event) => void) => {\n  abortSignal.addEventListener('abort', callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener('abort', callback);\n};\n\n/**\n * Calls `abortController.abort(reason)` and patches `signal.reason`.\n * if it is not supported.\n *\n * At the time of writing `signal.reason` is available in FF chrome, edge node 17 and deno.\n * @param abortController\n * @param reason\n * @returns\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/reason\n */\nexport const abortControllerWithReason = <T,>(abortController: AbortController, reason: T): void => {\n  type Consumer<T> = (val: T) => void;\n  const signal = abortController.signal as AbortSignalWithReason<T>;\n  if (signal.aborted) {\n    return;\n  }\n\n  // Patch `reason` if necessary.\n  // - We use defineProperty here because reason is a getter of `AbortSignal.__proto__`.\n  // - We need to patch 'reason' before calling `.abort()` because listeners to the 'abort'\n  // event are are notified immediately.\n  if (!('reason' in signal)) {\n    Object.defineProperty(signal, 'reason', {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  (abortController.abort as Consumer<typeof reason>)(reason);\n};", "import { TaskAbortError } from './exceptions';\nimport type { AbortSignalWithReason, TaskResult } from './types';\nimport { addAbortSignalListener, catchRejection, noop } from './utils';\n\n/**\n * Synchronously raises {@link TaskAbortError} if the task tied to the input `signal` has been cancelled.\n * @param signal\n * @param reason\n * @see {TaskAbortError}\n */\nexport const validateActive = (signal: AbortSignal): void => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal as AbortSignalWithReason<string>;\n    throw new TaskAbortError(reason);\n  }\n};\n\n/**\n * Generates a race between the promise(s) and the AbortSignal\n * This avoids `Promise.race()`-related memory leaks:\n * https://github.com/nodejs/node/issues/17469#issuecomment-349794909\n */\nexport function raceWithSignal<T>(signal: AbortSignalWithReason<string>, promise: Promise<T>): Promise<T> {\n  let cleanup = noop;\n  return new Promise<T>((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    // after this point, replace `cleanup` with a noop, so there is no reference to `signal` any more\n    cleanup = noop;\n  });\n}\n\n/**\n * Runs a task and returns promise that resolves to {@link TaskResult}.\n * Second argument is an optional `cleanUp` function that always runs after task.\n *\n * **Note:** `runTask` runs the executor in the next microtask.\n * @returns\n */\nexport const runTask = async <T,>(task: () => Promise<T>, cleanUp?: () => void): Promise<TaskResult<T>> => {\n  try {\n    await Promise.resolve();\n    const value = await task();\n    return {\n      status: 'ok',\n      value\n    };\n  } catch (error: any) {\n    return {\n      status: error instanceof TaskAbortError ? 'cancelled' : 'rejected',\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\n\n/**\n * Given an input `AbortSignal` and a promise returns another promise that resolves\n * as soon the input promise is provided or rejects as soon as\n * `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createPause = <T,>(signal: AbortSignal) => {\n  return (promise: Promise<T>): Promise<T> => {\n    return catchRejection(raceWithSignal(signal, promise).then(output => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\n\n/**\n * Given an input `AbortSignal` and `timeoutMs` returns a promise that resolves\n * after `timeoutMs` or rejects as soon as `AbortSignal.abort` is `true`.\n * @param signal\n * @returns\n */\nexport const createDelay = (signal: AbortSignal) => {\n  const pause = createPause<void>(signal);\n  return (timeoutMs: number): Promise<void> => {\n    return pause(new Promise<void>(resolve => setTimeout(resolve, timeoutMs)));\n  };\n};", "import type { Dispatch, Middleware, UnknownAction } from 'redux';\nimport { compose } from 'redux';\nimport { createAction } from '../createAction';\nimport { isAllOf } from '../matchers';\nimport { nanoid } from '../nanoid';\nimport { getOrInsertComputed } from '../utils';\nimport type { AddMiddleware, DynamicMiddleware, DynamicMiddlewareInstance, MiddlewareEntry, WithMiddleware } from './types';\nexport type { DynamicMiddlewareInstance, GetDispatchType as GetDispatch, MiddlewareApiConfig } from './types';\nconst createMiddlewareEntry = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(middleware: Middleware<any, State, DispatchType>): MiddlewareEntry<State, DispatchType> => ({\n  middleware,\n  applied: new Map()\n});\nconst matchInstance = (instanceId: string) => (action: any): action is {\n  meta: {\n    instanceId: string;\n  };\n} => action?.meta?.instanceId === instanceId;\nexport const createDynamicMiddleware = <State = any, DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>>(): DynamicMiddlewareInstance<State, DispatchType> => {\n  const instanceId = nanoid();\n  const middlewareMap = new Map<Middleware<any, State, DispatchType>, MiddlewareEntry<State, DispatchType>>();\n  const withMiddleware = Object.assign(createAction('dynamicMiddleware/add', (...middlewares: Middleware<any, State, DispatchType>[]) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  }) as WithMiddleware<State, DispatchType>;\n  const addMiddleware = Object.assign(function addMiddleware(...middlewares: Middleware<any, State, DispatchType>[]) {\n    middlewares.forEach(middleware => {\n      getOrInsertComputed(middlewareMap, middleware, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  }) as AddMiddleware<State, DispatchType>;\n  const getFinalMiddleware: Middleware<{}, State, DispatchType> = api => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map(entry => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return compose(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware: DynamicMiddleware<State, DispatchType> = api => next => action => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};", "import { formatProdErrorMessage as _formatProdErrorMessage, formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport type { Reducer, StateFromReducersMapObject, UnknownAction } from 'redux';\nimport { combineReducers } from 'redux';\nimport { nanoid } from './nanoid';\nimport type { Id, NonUndefined, Tail, UnionToIntersection, WithOptionalProp } from './tsHelpers';\nimport { getOrInsertComputed } from './utils';\ntype SliceLike<ReducerPath extends string, State> = {\n  reducerPath: ReducerPath;\n  reducer: Reducer<State>;\n};\ntype AnySliceLike = SliceLike<string, any>;\ntype SliceLikeReducerPath<A extends AnySliceLike> = A extends SliceLike<infer ReducerPath, any> ? ReducerPath : never;\ntype SliceLikeState<A extends AnySliceLike> = A extends SliceLike<any, infer State> ? State : never;\nexport type WithSlice<A extends AnySliceLike> = { [Path in SliceLikeReducerPath<A>]: SliceLikeState<A> };\ntype ReducerMap = Record<string, Reducer>;\ntype ExistingSliceLike<DeclaredState> = { [ReducerPath in keyof DeclaredState]: SliceLike<ReducerPath & string, NonUndefined<DeclaredState[ReducerPath]>> }[keyof DeclaredState];\nexport type InjectConfig = {\n  /**\n   * Allow replacing reducer with a different reference. Normally, an error will be thrown if a different reducer instance to the one already injected is used.\n   */\n  overrideExisting?: boolean;\n};\n\n/**\n * A reducer that allows for slices/reducers to be injected after initialisation.\n */\nexport interface CombinedSliceReducer<InitialState, DeclaredState = InitialState> extends Reducer<DeclaredState, UnknownAction, Partial<DeclaredState>> {\n  /**\n   * Provide a type for slices that will be injected lazily.\n   *\n   * One way to do this would be with interface merging:\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {}\n   *\n   * export const rootReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * // elsewhere\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBoolean = rootReducer.inject(booleanSlice);\n   *\n   * // elsewhere again\n   *\n   * declare module './reducer' {\n   *   export interface LazyLoadedSlices {\n   *     customName: CustomState\n   *   }\n   * }\n   *\n   * const withCustom = rootReducer.inject({ reducerPath: \"customName\", reducer: customSlice.reducer })\n   * ```\n   */\n  withLazyLoadedSlices<Lazy = {}>(): CombinedSliceReducer<InitialState, Id<DeclaredState & Partial<Lazy>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<Sl extends Id<ExistingSliceLike<DeclaredState>>>(slice: Sl, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<Sl>>>;\n\n  /**\n   * Inject a slice.\n   *\n   * Accepts an individual slice, RTKQ API instance, or a \"slice-like\" { reducerPath, reducer } object.\n   *\n   * ```ts\n   * rootReducer.inject(booleanSlice)\n   * rootReducer.inject(baseApi)\n   * rootReducer.inject({ reducerPath: 'boolean' as const, reducer: newReducer }, { overrideExisting: true })\n   * ```\n   *\n   */\n  inject<ReducerPath extends string, State>(slice: SliceLike<ReducerPath, State & (ReducerPath extends keyof DeclaredState ? never : State)>, config?: InjectConfig): CombinedSliceReducer<InitialState, Id<DeclaredState & WithSlice<SliceLike<ReducerPath, State>>>>;\n\n  /**\n   * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n   *\n   * ```ts\n   * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n   * //                                                                ^? boolean | undefined\n   *\n   * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n   *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n   *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n   *   return state.boolean;\n   *   //           ^? boolean\n   * })\n   * ```\n   *\n   * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n   *\n   * ```ts\n   *\n   * export interface LazyLoadedSlices {};\n   *\n   * export const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n   *\n   * export const rootReducer = combineSlices({ inner: innerReducer });\n   *\n   * export type RootState = ReturnType<typeof rootReducer>;\n   *\n   * // elsewhere\n   *\n   * declare module \"./reducer.ts\" {\n   *  export interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n   * }\n   *\n   * const withBool = innerReducer.inject(booleanSlice);\n   *\n   * const selectBoolean = withBool.selector(\n   *   (state) => state.boolean,\n   *   (rootState: RootState) => state.inner\n   * );\n   * //    now expects to be passed RootState instead of innerReducer state\n   *\n   * ```\n   *\n   * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n   *\n   * ```ts\n   * const injectedReducer = rootReducer.inject(booleanSlice);\n   * const selectBoolean = injectedReducer.selector((state) => {\n   *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n   *   return state.boolean\n   * })\n   * ```\n   */\n  selector: {\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown>(selectorFn: Selector): (state: WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n\n    /**\n     * Create a selector that guarantees that the slices injected will have a defined value when selector is run.\n     *\n     * ```ts\n     * const selectBooleanWithoutInjection = (state: RootState) => state.boolean;\n     * //                                                                ^? boolean | undefined\n     *\n     * const selectBoolean = rootReducer.inject(booleanSlice).selector((state) => {\n     *   // if action hasn't been dispatched since slice was injected, this would usually be undefined\n     *   // however selector() uses a Proxy around the first parameter to ensure that it evaluates to the initial state instead, if undefined\n     *   return state.boolean;\n     *   //           ^? boolean\n     * })\n     * ```\n     *\n     * If the reducer is nested inside the root state, a selectState callback can be passed to retrieve the reducer's state.\n     *\n     * ```ts\n     *\n     * interface LazyLoadedSlices {};\n     *\n     * const innerReducer = combineSlices(stringSlice).withLazyLoadedSlices<LazyLoadedSlices>();\n     *\n     * const rootReducer = combineSlices({ inner: innerReducer });\n     *\n     * type RootState = ReturnType<typeof rootReducer>;\n     *\n     * // elsewhere\n     *\n     * declare module \"./reducer.ts\" {\n     *  interface LazyLoadedSlices extends WithSlice<typeof booleanSlice> {}\n     * }\n     *\n     * const withBool = innerReducer.inject(booleanSlice);\n     *\n     * const selectBoolean = withBool.selector(\n     *   (state) => state.boolean,\n     *   (rootState: RootState) => state.inner\n     * );\n     * //    now expects to be passed RootState instead of innerReducer state\n     *\n     * ```\n     *\n     * Value passed to selectorFn will be a Proxy - use selector.original(proxy) to get original state value (useful for debugging)\n     *\n     * ```ts\n     * const injectedReducer = rootReducer.inject(booleanSlice);\n     * const selectBoolean = injectedReducer.selector((state) => {\n     *   console.log(injectedReducer.selector.original(state).boolean) // possibly undefined\n     *   return state.boolean\n     * })\n     * ```\n     */\n    <Selector extends (state: DeclaredState, ...args: any[]) => unknown, RootState>(selectorFn: Selector, selectState: (rootState: RootState, ...args: Tail<Parameters<Selector>>) => WithOptionalProp<Parameters<Selector>[0], Exclude<keyof DeclaredState, keyof InitialState>>): (state: RootState, ...args: Tail<Parameters<Selector>>) => ReturnType<Selector>;\n    /**\n     * Returns the unproxied state. Useful for debugging.\n     * @param state state Proxy, that ensures injected reducers have value\n     * @returns original, unproxied state\n     * @throws if value passed is not a state Proxy\n     */\n    original: (state: DeclaredState) => InitialState & Partial<DeclaredState>;\n  };\n}\ntype InitialState<Slices extends Array<AnySliceLike | ReducerMap>> = UnionToIntersection<Slices[number] extends infer Slice ? Slice extends AnySliceLike ? WithSlice<Slice> : StateFromReducersMapObject<Slice> : never>;\nconst isSliceLike = (maybeSliceLike: AnySliceLike | ReducerMap): maybeSliceLike is AnySliceLike => 'reducerPath' in maybeSliceLike && typeof maybeSliceLike.reducerPath === 'string';\nconst getReducers = (slices: Array<AnySliceLike | ReducerMap>) => slices.flatMap(sliceOrMap => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer] as const] : Object.entries(sliceOrMap));\nconst ORIGINAL_STATE = Symbol.for('rtk-state-proxy-original');\nconst isStateProxy = (value: any) => !!value && !!value[ORIGINAL_STATE];\nconst stateProxyMap = new WeakMap<object, object>();\nconst createStateProxy = <State extends object,>(state: State, reducerMap: Partial<Record<PropertyKey, Reducer>>, initialStateCache: Record<PropertyKey, unknown>) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === 'undefined') {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== 'undefined') return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        // ensure action type is random, to prevent reducer treating it differently\n        const reducerResult = reducer(undefined, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === 'undefined') {\n          throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(24) : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). ` + `If the state passed to the reducer is undefined, you must ` + `explicitly return the initial state. The initial state may ` + `not be undefined. If you don't want to set a value for this reducer, ` + `you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n})) as State;\nconst original = (state: any) => {\n  if (!isStateProxy(state)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(25) : 'original must be used on state Proxy');\n  }\n  return state[ORIGINAL_STATE];\n};\nconst emptyObject = {};\nconst noopReducer: Reducer<Record<string, any>> = (state = emptyObject) => state;\nexport function combineSlices<Slices extends Array<AnySliceLike | ReducerMap>>(...slices: Slices): CombinedSliceReducer<Id<InitialState<Slices>>> {\n  const reducerMap = Object.fromEntries<Reducer>(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? combineReducers(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state: Record<string, unknown>, action: UnknownAction) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache: Record<PropertyKey, unknown> = {};\n  const inject = (slice: AnySliceLike, config: InjectConfig = {}): typeof combinedReducer => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector<State extends object, RootState, Args extends any[]>(selectorFn: (state: State, ...args: Args) => any, selectState?: (rootState: RootState, ...args: Args) => State) {\n    return function selector(state: State, ...args: Args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state as any, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  }) as any;\n}", "/**\r\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\r\n *\r\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\r\n * during build.\r\n * @param {number} code\r\n */\nexport function formatProdErrorMessage(code: number) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or ` + 'use the non-minified dev environment for full errors. ';\n}"], "mappings": ";AAGA,cAAc;AACd,SAAoBA,OAAA,EAAiBC,OAAA,IAAAC,QAAA,EAASC,MAAA,EAAQC,QAAA,IAAAC,SAAA,EAAUC,OAAA,IAAAC,QAAA,QAAe;AAE/E,SAASC,cAAA,EAAgBC,qBAAA,IAAAC,sBAAA,EAAuBC,UAAA,EAAYC,cAAA,IAAAC,eAAA,QAAsB;;;ACNlF,SAASZ,OAAA,EAASK,OAAA,QAAe;AACjC,SAASG,qBAAA,EAAuBG,cAAA,QAAsB;AAC/C,IAAME,8BAAA,GAA+DA,CAAA,GAAIC,IAAA,KAAoB;EAClG,MAAMC,eAAA,GAAkBP,qBAAA,CAA8B,GAAGM,IAAI;EAC7D,MAAME,wBAAA,GAA0BC,MAAA,CAAOC,MAAA,CAAO,IAAIC,KAAA,KAAoB;IACpE,MAAMC,QAAA,GAAWL,eAAA,CAAe,GAAGI,KAAI;IACvC,MAAME,eAAA,GAAkBA,CAACC,KAAA,KAAmBC,IAAA,KAAoBH,QAAA,CAASf,OAAA,CAAQiB,KAAK,IAAItB,OAAA,CAAQsB,KAAK,IAAIA,KAAA,EAAO,GAAGC,IAAI;IACzHN,MAAA,CAAOC,MAAA,CAAOG,eAAA,EAAiBD,QAAQ;IACvC,OAAOC,eAAA;EACT,GAAG;IACDG,SAAA,EAAWA,CAAA,KAAMR;EACnB,CAAC;EACD,OAAOA,wBAAA;AACT;AASO,IAAMS,uBAAA,GACb,eAAAZ,8BAAA,CAA+BF,cAAc;;;ACrB7C,SAASe,eAAA,EAAiBC,WAAA,EAAaC,OAAA,IAAAC,QAAA,EAASC,eAAA,EAAiBC,aAAA,IAAAC,cAAA,QAAqB;;;ACDtF,SAASJ,OAAA,QAAe;AAkNjB,IAAMK,mBAAA,GAA2C,OAAOC,MAAA,KAAW,eAAgBA,MAAA,CAAeC,oCAAA,GAAwCD,MAAA,CAAeC,oCAAA,GAAuC,YAAY;EACjN,IAAIC,SAAA,CAAUC,MAAA,KAAW,GAAG,OAAO;EACnC,IAAI,OAAOD,SAAA,CAAU,CAAC,MAAM,UAAU,OAAOR,OAAA;EAC7C,OAAOA,OAAA,CAAQU,KAAA,CAAM,MAAMF,SAA8B;AAC3D;AAKO,IAAMG,gBAAA,GAET,OAAOL,MAAA,KAAW,eAAgBA,MAAA,CAAeM,4BAAA,GAAgCN,MAAA,CAAeM,4BAAA,GAA+B,YAAY;EAC7I,OAAO,UAAUC,KAAA,EAAM;IACrB,OAAOA,KAAA;EACT;AACF;;;AChOA,SAASC,KAAA,IAASC,eAAA,EAAiBC,iBAAA,QAAyB;;;ACD5D,SAASC,QAAA,QAAgB;;;ACsFlB,IAAMC,gBAAA,GAAwBC,CAAA,IAA4C;EAC/E,OAAOA,CAAA,IAAK,OAAQA,CAAA,CAA0BC,KAAA,KAAU;AAC1D;;;AD4GO,SAASC,aAAaC,IAAA,EAAcC,aAAA,EAA+B;EACxE,SAASC,cAAA,GAAiBtC,IAAA,EAAa;IACrC,IAAIqC,aAAA,EAAe;MACjB,IAAIE,QAAA,GAAWF,aAAA,CAAc,GAAGrC,IAAI;MACpC,IAAI,CAACuC,QAAA,EAAU;QACb,MAAM,IAAIC,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,CAAC,IAAI,wCAAwC;MAC/H;MACA,OAAO;QACLR,IAAA;QACAS,OAAA,EAASN,QAAA,CAASM,OAAA;QAClB,IAAI,UAAUN,QAAA,IAAY;UACxBO,IAAA,EAAMP,QAAA,CAASO;QACjB;QACA,IAAI,WAAWP,QAAA,IAAY;UACzBQ,KAAA,EAAOR,QAAA,CAASQ;QAClB;MACF;IACF;IACA,OAAO;MACLX,IAAA;MACAS,OAAA,EAAS7C,IAAA,CAAK,CAAC;IACjB;EACF;EACAsC,aAAA,CAAcU,QAAA,GAAW,MAAM,GAAGZ,IAAI;EACtCE,aAAA,CAAcF,IAAA,GAAOA,IAAA;EACrBE,aAAA,CAAcJ,KAAA,GAASe,MAAA,IAA6ClB,QAAA,CAASkB,MAAM,KAAKA,MAAA,CAAOb,IAAA,KAASA,IAAA;EACxG,OAAOE,aAAA;AACT;AAKO,SAASY,gBAAgBD,MAAA,EAA0E;EACxG,OAAO,OAAOA,MAAA,KAAW,cAAc,UAAUA,MAAA;EAAA;EAEjDjB,gBAAA,CAAiBiB,MAAa;AAChC;AAKO,SAASE,MAAMF,MAAA,EAKpB;EACA,OAAOlB,QAAA,CAASkB,MAAM,KAAK9C,MAAA,CAAOiD,IAAA,CAAKH,MAAM,EAAEI,KAAA,CAAMC,UAAU;AACjE;AACA,SAASA,WAAWC,GAAA,EAAa;EAC/B,OAAO,CAAC,QAAQ,WAAW,SAAS,MAAM,EAAEC,OAAA,CAAQD,GAAG,IAAI;AAC7D;;;AE7OO,SAASE,WAAWrB,IAAA,EAAgB;EACzC,MAAMsB,SAAA,GAAYtB,IAAA,GAAO,GAAGA,IAAI,GAAGuB,KAAA,CAAM,GAAG,IAAI,EAAC;EACjD,MAAMC,UAAA,GAAaF,SAAA,CAAUA,SAAA,CAAUnC,MAAA,GAAS,CAAC,KAAK;EACtD,OAAO,yCAAyCa,IAAA,IAAQ,SAAS;AAAA,kFACewB,UAAU,+BAA+BA,UAAU;AACrI;AACO,SAASC,uCAAuCC,OAAA,GAAmD,CAAC,GAAe;EACxH,IAAIrB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO,MAAMoB,IAAA,IAAQd,MAAA,IAAUc,IAAA,CAAKd,MAAM;EAC5C;EACA,MAAM;IACJC,eAAA,EAAAc,gBAAA,GAAkBd;EACpB,IAAIY,OAAA;EACJ,OAAO,MAAMC,IAAA,IAAQd,MAAA,IAAU;IAC7B,IAAIe,gBAAA,CAAgBf,MAAM,GAAG;MAC3BgB,OAAA,CAAQC,IAAA,CAAKT,UAAA,CAAWR,MAAA,CAAOb,IAAI,CAAC;IACtC;IACA,OAAO2B,IAAA,CAAKd,MAAM;EACpB;AACF;;;AC9BA,SAAShE,OAAA,IAAWkF,eAAA,EAAiBC,WAAA,QAAmB;AACjD,SAASC,oBAAoBC,QAAA,EAAkBC,MAAA,EAAgB;EACpE,IAAIC,OAAA,GAAU;EACd,OAAO;IACLC,YAAeC,EAAA,EAAgB;MAC7B,MAAMC,OAAA,GAAUC,IAAA,CAAKC,GAAA,CAAI;MACzB,IAAI;QACF,OAAOH,EAAA,CAAG;MACZ,UAAE;QACA,MAAMI,QAAA,GAAWF,IAAA,CAAKC,GAAA,CAAI;QAC1BL,OAAA,IAAWM,QAAA,GAAWH,OAAA;MACxB;IACF;IACAI,eAAA,EAAiB;MACf,IAAIP,OAAA,GAAUF,QAAA,EAAU;QACtBL,OAAA,CAAQC,IAAA,CAAK,GAAGK,MAAM,SAASC,OAAO,mDAAmDF,QAAQ;AAAA;AAAA,4EAE7B;MACtE;IACF;EACF;AACF;AAIO,IAAMU,KAAA,GAAN,MAAMC,MAAA,SAAyDC,KAAA,CAAqB;EAGzFC,YAAA,GAAeC,KAAA,EAAc;IAC3B,MAAM,GAAGA,KAAK;IACdjF,MAAA,CAAOkF,cAAA,CAAe,MAAMJ,MAAA,CAAMK,SAAS;EAC7C;EACA,YAAqBC,MAAA,CAAOC,OAAO,IAAI;IACrC,OAAOP,MAAA;EACT;EAISQ,OAAA,GAAUC,GAAA,EAAY;IAC7B,OAAO,MAAMD,MAAA,CAAOjE,KAAA,CAAM,MAAMkE,GAAG;EACrC;EAIAC,QAAA,GAAWD,GAAA,EAAY;IACrB,IAAIA,GAAA,CAAInE,MAAA,KAAW,KAAK2D,KAAA,CAAMU,OAAA,CAAQF,GAAA,CAAI,CAAC,CAAC,GAAG;MAC7C,OAAO,IAAIT,MAAA,CAAM,GAAGS,GAAA,CAAI,CAAC,EAAED,MAAA,CAAO,IAAI,CAAC;IACzC;IACA,OAAO,IAAIR,MAAA,CAAM,GAAGS,GAAA,CAAID,MAAA,CAAO,IAAI,CAAC;EACtC;AACF;AACO,SAASI,gBAAmBC,GAAA,EAAQ;EACzC,OAAO1B,WAAA,CAAY0B,GAAG,IAAI3B,eAAA,CAAgB2B,GAAA,EAAK,MAAM,CAAC,CAAC,IAAIA,GAAA;AAC7D;AASO,SAASC,oBAAyCC,GAAA,EAAgCzC,GAAA,EAAQ0C,OAAA,EAA2B;EAC1H,IAAID,GAAA,CAAIE,GAAA,CAAI3C,GAAG,GAAG,OAAOyC,GAAA,CAAIG,GAAA,CAAI5C,GAAG;EACpC,OAAOyC,GAAA,CAAII,GAAA,CAAI7C,GAAA,EAAK0C,OAAA,CAAQ1C,GAAG,CAAC,EAAE4C,GAAA,CAAI5C,GAAG;AAC3C;;;ACtDO,SAAS8C,mBAAmB7F,KAAA,EAAyB;EAC1D,OAAO,OAAOA,KAAA,KAAU,YAAYA,KAAA,IAAS,QAAQL,MAAA,CAAOmG,QAAA,CAAS9F,KAAK;AAC5E;AACO,SAAS+F,kBAAkBC,WAAA,EAA8BC,WAAA,EAAsCC,GAAA,EAAU;EAC9G,MAAMC,iBAAA,GAAoBC,eAAA,CAAgBJ,WAAA,EAAaC,WAAA,EAAaC,GAAG;EACvE,OAAO;IACLG,gBAAA,EAAkB;MAChB,OAAOA,eAAA,CAAgBL,WAAA,EAAaC,WAAA,EAAaE,iBAAA,EAAmBD,GAAG;IACzE;EACF;AACF;AAKA,SAASE,gBAAgBJ,WAAA,EAA8BC,WAAA,GAA2B,EAAC,EAAGC,GAAA,EAA0BI,IAAA,GAAe,IAAIC,cAAA,GAA2C,mBAAIC,GAAA,CAAI,GAAG;EACvL,MAAMC,OAAA,GAAoC;IACxCzG,KAAA,EAAOkG;EACT;EACA,IAAI,CAACF,WAAA,CAAYE,GAAG,KAAK,CAACK,cAAA,CAAeb,GAAA,CAAIQ,GAAG,GAAG;IACjDK,cAAA,CAAeG,GAAA,CAAIR,GAAG;IACtBO,OAAA,CAAQE,QAAA,GAAW,CAAC;IACpB,WAAW5D,GAAA,IAAOmD,GAAA,EAAK;MACrB,MAAMU,SAAA,GAAYN,IAAA,GAAOA,IAAA,GAAO,MAAMvD,GAAA,GAAMA,GAAA;MAC5C,IAAIkD,WAAA,CAAYlF,MAAA,IAAUkF,WAAA,CAAYjD,OAAA,CAAQ4D,SAAS,MAAM,IAAI;QAC/D;MACF;MACAH,OAAA,CAAQE,QAAA,CAAS5D,GAAG,IAAIqD,eAAA,CAAgBJ,WAAA,EAAaC,WAAA,EAAaC,GAAA,CAAInD,GAAG,GAAG6D,SAAS;IACvF;EACF;EACA,OAAOH,OAAA;AACT;AACA,SAASJ,gBAAgBL,WAAA,EAA8Ba,YAAA,GAA4B,EAAC,EAAGC,eAAA,EAAkCZ,GAAA,EAAUa,aAAA,GAAyB,OAAOT,IAAA,GAAe,IAGhL;EACA,MAAMU,OAAA,GAAUF,eAAA,GAAkBA,eAAA,CAAgB9G,KAAA,GAAQ;EAC1D,MAAMiH,OAAA,GAAUD,OAAA,KAAYd,GAAA;EAC5B,IAAIa,aAAA,IAAiB,CAACE,OAAA,IAAW,CAACC,MAAA,CAAOC,KAAA,CAAMjB,GAAG,GAAG;IACnD,OAAO;MACLkB,UAAA,EAAY;MACZd;IACF;EACF;EACA,IAAIN,WAAA,CAAYgB,OAAO,KAAKhB,WAAA,CAAYE,GAAG,GAAG;IAC5C,OAAO;MACLkB,UAAA,EAAY;IACd;EACF;EAGA,MAAMC,YAAA,GAAwC,CAAC;EAC/C,SAAStE,GAAA,IAAO+D,eAAA,CAAgBH,QAAA,EAAU;IACxCU,YAAA,CAAatE,GAAG,IAAI;EACtB;EACA,SAASA,GAAA,IAAOmD,GAAA,EAAK;IACnBmB,YAAA,CAAatE,GAAG,IAAI;EACtB;EACA,MAAMuE,eAAA,GAAkBT,YAAA,CAAa9F,MAAA,GAAS;EAC9C,SAASgC,GAAA,IAAOsE,YAAA,EAAc;IAC5B,MAAME,UAAA,GAAajB,IAAA,GAAOA,IAAA,GAAO,MAAMvD,GAAA,GAAMA,GAAA;IAC7C,IAAIuE,eAAA,EAAiB;MACnB,MAAME,UAAA,GAAaX,YAAA,CAAaY,IAAA,CAAKC,OAAA,IAAW;QAC9C,IAAIA,OAAA,YAAmBC,MAAA,EAAQ;UAC7B,OAAOD,OAAA,CAAQE,IAAA,CAAKL,UAAU;QAChC;QACA,OAAOA,UAAA,KAAeG,OAAA;MACxB,CAAC;MACD,IAAIF,UAAA,EAAY;QACd;MACF;IACF;IACA,MAAMK,MAAA,GAASxB,eAAA,CAAgBL,WAAA,EAAaa,YAAA,EAAcC,eAAA,CAAgBH,QAAA,CAAS5D,GAAG,GAAGmD,GAAA,CAAInD,GAAG,GAAGkE,OAAA,EAASM,UAAU;IACtH,IAAIM,MAAA,CAAOT,UAAA,EAAY;MACrB,OAAOS,MAAA;IACT;EACF;EACA,OAAO;IACLT,UAAA,EAAY;EACd;AACF;AAmCO,SAASU,wCAAwCxE,OAAA,GAAoD,CAAC,GAAe;EAC1H,IAAIrB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO,MAAMoB,IAAA,IAAQd,MAAA,IAAUc,IAAA,CAAKd,MAAM;EAC5C,OAAO;IACL,IAASsF,UAAA,GAAT,SAAAC,CAAmB9B,GAAA,EAAU+B,UAAA,EAA6BC,MAAA,EAA0BC,QAAA,EAAmC;QACrH,OAAOC,IAAA,CAAKJ,SAAA,CAAU9B,GAAA,EAAKmC,aAAA,CAAaJ,UAAA,EAAYE,QAAQ,GAAGD,MAAM;MACvE;MACSG,aAAA,GAAT,SAAAC,CAAsBL,UAAA,EAA6BE,QAAA,EAA2C;QAC5F,IAAII,KAAA,GAAe,EAAC;UAClB3F,IAAA,GAAc,EAAC;QACjB,IAAI,CAACuF,QAAA,EAAUA,QAAA,GAAW,SAAAA,CAAUK,CAAA,EAAWxI,KAAA,EAAY;UACzD,IAAIuI,KAAA,CAAM,CAAC,MAAMvI,KAAA,EAAO,OAAO;UAC/B,OAAO,iBAAiB4C,IAAA,CAAK6F,KAAA,CAAM,GAAGF,KAAA,CAAMvF,OAAA,CAAQhD,KAAK,CAAC,EAAE0I,IAAA,CAAK,GAAG,IAAI;QAC1E;QACA,OAAO,UAAqB3F,GAAA,EAAa/C,KAAA,EAAY;UACnD,IAAIuI,KAAA,CAAMxH,MAAA,GAAS,GAAG;YACpB,IAAI4H,OAAA,GAAUJ,KAAA,CAAMvF,OAAA,CAAQ,IAAI;YAChC,CAAC2F,OAAA,GAAUJ,KAAA,CAAMK,MAAA,CAAOD,OAAA,GAAU,CAAC,IAAIJ,KAAA,CAAMM,IAAA,CAAK,IAAI;YACtD,CAACF,OAAA,GAAU/F,IAAA,CAAKgG,MAAA,CAAOD,OAAA,EAASG,QAAA,EAAU/F,GAAG,IAAIH,IAAA,CAAKiG,IAAA,CAAK9F,GAAG;YAC9D,IAAI,CAACwF,KAAA,CAAMvF,OAAA,CAAQhD,KAAK,GAAGA,KAAA,GAAQmI,QAAA,CAAUY,IAAA,CAAK,MAAMhG,GAAA,EAAK/C,KAAK;UACpE,OAAOuI,KAAA,CAAMM,IAAA,CAAK7I,KAAK;UACvB,OAAOiI,UAAA,IAAc,OAAOjI,KAAA,GAAQiI,UAAA,CAAWc,IAAA,CAAK,MAAMhG,GAAA,EAAK/C,KAAK;QACtE;MACF;IAnBS,IAAAgI,SAAA,GAAAD,UAAA;MAGAO,YAAA,GAAAD,aAAA;IAiBT,IAAI;MACFrC,WAAA,GAAcH,kBAAA;MACdgB,YAAA;MACAmC,SAAA,GAAY;IACd,IAAI1F,OAAA;IACJ,MAAM2F,KAAA,GAAQlD,iBAAA,CAAkBmD,IAAA,CAAK,MAAMlD,WAAA,EAAaa,YAAY;IACpE,OAAO,CAAC;MACNsC;IACF,MAAM;MACJ,IAAIC,KAAA,GAAQD,QAAA,CAAS;MACrB,IAAIE,OAAA,GAAUJ,KAAA,CAAMG,KAAK;MACzB,IAAIvB,MAAA;MACJ,OAAOtE,IAAA,IAAQd,MAAA,IAAU;QACvB,MAAM6G,YAAA,GAAezF,mBAAA,CAAoBmF,SAAA,EAAW,mCAAmC;QACvFM,YAAA,CAAarF,WAAA,CAAY,MAAM;UAC7BmF,KAAA,GAAQD,QAAA,CAAS;UACjBtB,MAAA,GAASwB,OAAA,CAAQhD,eAAA,CAAgB;UAEjCgD,OAAA,GAAUJ,KAAA,CAAMG,KAAK;UACrB,IAAIvB,MAAA,CAAOT,UAAA,EAAY;YACrB,MAAM,IAAIpF,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,kEAAkEyF,MAAA,CAAOvB,IAAA,IAAQ,EAAE,2GAA2G;UACtR;QACF,CAAC;QACD,MAAMiD,gBAAA,GAAmBhG,IAAA,CAAKd,MAAM;QACpC6G,YAAA,CAAarF,WAAA,CAAY,MAAM;UAC7BmF,KAAA,GAAQD,QAAA,CAAS;UACjBtB,MAAA,GAASwB,OAAA,CAAQhD,eAAA,CAAgB;UAEjCgD,OAAA,GAAUJ,KAAA,CAAMG,KAAK;UACrB,IAAIvB,MAAA,CAAOT,UAAA,EAAY;YACrB,MAAM,IAAIpF,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,iEAAiEyF,MAAA,CAAOvB,IAAA,IAAQ,EAAE,uDAAuDyB,UAAA,CAAUtF,MAAM,CAAC,sEAAsE;UACzT;QACF,CAAC;QACD6G,YAAA,CAAa/E,cAAA,CAAe;QAC5B,OAAOgF,gBAAA;MACT;IACF;EACF;AACF;;;AC3LA,SAAShI,QAAA,IAAAiI,SAAA,EAAU/I,aAAA,QAAqB;AAYjC,SAASgJ,QAAQnE,GAAA,EAAU;EAChC,MAAM1D,IAAA,GAAO,OAAO0D,GAAA;EACpB,OAAOA,GAAA,IAAO,QAAQ1D,IAAA,KAAS,YAAYA,IAAA,KAAS,aAAaA,IAAA,KAAS,YAAY8C,KAAA,CAAMU,OAAA,CAAQE,GAAG,KAAK7E,aAAA,CAAc6E,GAAG;AAC/H;AAUO,SAASoE,yBAAyB1J,KAAA,EAAgBsG,IAAA,GAAe,IAAIqD,cAAA,GAA8CF,OAAA,EAASG,UAAA,EAAkD/C,YAAA,GAA4B,EAAC,EAAGgD,KAAA,EAAuD;EAC1Q,IAAIC,uBAAA;EACJ,IAAI,CAACH,cAAA,CAAe3J,KAAK,GAAG;IAC1B,OAAO;MACL+J,OAAA,EAASzD,IAAA,IAAQ;MACjBtG;IACF;EACF;EACA,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,MAAM;IAC/C,OAAO;EACT;EACA,IAAI6J,KAAA,EAAOnE,GAAA,CAAI1F,KAAK,GAAG,OAAO;EAC9B,MAAMgK,OAAA,GAAUJ,UAAA,IAAc,OAAOA,UAAA,CAAW5J,KAAK,IAAIL,MAAA,CAAOqK,OAAA,CAAQhK,KAAK;EAC7E,MAAMsH,eAAA,GAAkBT,YAAA,CAAa9F,MAAA,GAAS;EAC9C,WAAW,CAACgC,GAAA,EAAKkH,WAAW,KAAKD,OAAA,EAAS;IACxC,MAAMzC,UAAA,GAAajB,IAAA,GAAOA,IAAA,GAAO,MAAMvD,GAAA,GAAMA,GAAA;IAC7C,IAAIuE,eAAA,EAAiB;MACnB,MAAME,UAAA,GAAaX,YAAA,CAAaY,IAAA,CAAKC,OAAA,IAAW;QAC9C,IAAIA,OAAA,YAAmBC,MAAA,EAAQ;UAC7B,OAAOD,OAAA,CAAQE,IAAA,CAAKL,UAAU;QAChC;QACA,OAAOA,UAAA,KAAeG,OAAA;MACxB,CAAC;MACD,IAAIF,UAAA,EAAY;QACd;MACF;IACF;IACA,IAAI,CAACmC,cAAA,CAAeM,WAAW,GAAG;MAChC,OAAO;QACLF,OAAA,EAASxC,UAAA;QACTvH,KAAA,EAAOiK;MACT;IACF;IACA,IAAI,OAAOA,WAAA,KAAgB,UAAU;MACnCH,uBAAA,GAA0BJ,wBAAA,CAAyBO,WAAA,EAAa1C,UAAA,EAAYoC,cAAA,EAAgBC,UAAA,EAAY/C,YAAA,EAAcgD,KAAK;MAC3H,IAAIC,uBAAA,EAAyB;QAC3B,OAAOA,uBAAA;MACT;IACF;EACF;EACA,IAAID,KAAA,IAASK,cAAA,CAAelK,KAAK,GAAG6J,KAAA,CAAMnD,GAAA,CAAI1G,KAAK;EACnD,OAAO;AACT;AACO,SAASkK,eAAelK,KAAA,EAAe;EAC5C,IAAI,CAACL,MAAA,CAAOmG,QAAA,CAAS9F,KAAK,GAAG,OAAO;EACpC,WAAWiK,WAAA,IAAetK,MAAA,CAAOwK,MAAA,CAAOnK,KAAK,GAAG;IAC9C,IAAI,OAAOiK,WAAA,KAAgB,YAAYA,WAAA,KAAgB,MAAM;IAC7D,IAAI,CAACC,cAAA,CAAeD,WAAW,GAAG,OAAO;EAC3C;EACA,OAAO;AACT;AAwEO,SAASG,2CAA2C9G,OAAA,GAAuD,CAAC,GAAe;EAChI,IAAIrB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO,MAAMoB,IAAA,IAAQd,MAAA,IAAUc,IAAA,CAAKd,MAAM;EAC5C,OAAO;IACL,MAAM;MACJkH,cAAA,GAAiBF,OAAA;MACjBG,UAAA;MACAS,cAAA,GAAiB,EAAC;MAClBC,kBAAA,GAAqB,CAAC,YAAY,oBAAoB;MACtDzD,YAAA,GAAe,EAAC;MAChBmC,SAAA,GAAY;MACZuB,WAAA,GAAc;MACdC,aAAA,GAAgB;MAChBC,YAAA,GAAe;IACjB,IAAInH,OAAA;IACJ,MAAMuG,KAAA,GAAqC,CAACY,YAAA,IAAgBC,OAAA,GAAU,mBAAIA,OAAA,CAAQ,IAAI;IACtF,OAAOC,QAAA,IAAYpH,IAAA,IAAQd,MAAA,IAAU;MACnC,IAAI,CAAC+G,SAAA,CAAS/G,MAAM,GAAG;QACrB,OAAOc,IAAA,CAAKd,MAAM;MACpB;MACA,MAAMoF,MAAA,GAAStE,IAAA,CAAKd,MAAM;MAC1B,MAAM6G,YAAA,GAAezF,mBAAA,CAAoBmF,SAAA,EAAW,sCAAsC;MAC1F,IAAI,CAACwB,aAAA,IAAiB,EAAEH,cAAA,CAAetJ,MAAA,IAAUsJ,cAAA,CAAerH,OAAA,CAAQP,MAAA,CAAOb,IAAW,MAAM,KAAK;QACnG0H,YAAA,CAAarF,WAAA,CAAY,MAAM;UAC7B,MAAM2G,+BAAA,GAAkClB,wBAAA,CAAyBjH,MAAA,EAAQ,IAAIkH,cAAA,EAAgBC,UAAA,EAAYU,kBAAA,EAAoBT,KAAK;UAClI,IAAIe,+BAAA,EAAiC;YACnC,MAAM;cACJb,OAAA;cACA/J;YACF,IAAI4K,+BAAA;YACJnH,OAAA,CAAQlB,KAAA,CAAM,sEAAsEwH,OAAO,cAAc/J,KAAA,EAAO,4DAA4DyC,MAAA,EAAQ,yIAAyI,6HAA6H;UAC5b;QACF,CAAC;MACH;MACA,IAAI,CAAC8H,WAAA,EAAa;QAChBjB,YAAA,CAAarF,WAAA,CAAY,MAAM;UAC7B,MAAMmF,KAAA,GAAQuB,QAAA,CAASxB,QAAA,CAAS;UAChC,MAAM0B,8BAAA,GAAiCnB,wBAAA,CAAyBN,KAAA,EAAO,IAAIO,cAAA,EAAgBC,UAAA,EAAY/C,YAAA,EAAcgD,KAAK;UAC1H,IAAIgB,8BAAA,EAAgC;YAClC,MAAM;cACJd,OAAA;cACA/J;YACF,IAAI6K,8BAAA;YACJpH,OAAA,CAAQlB,KAAA,CAAM,sEAAsEwH,OAAO,cAAc/J,KAAA,EAAO;AAAA,2DACjEyC,MAAA,CAAOb,IAAI;AAAA,+HACyD;UACrH;QACF,CAAC;QACD0H,YAAA,CAAa/E,cAAA,CAAe;MAC9B;MACA,OAAOsD,MAAA;IACT;EACF;AACF;;;AN3LA,SAASiD,UAAUC,CAAA,EAAsB;EACvC,OAAO,OAAOA,CAAA,KAAM;AACtB;AAuBO,IAAMC,yBAAA,GAA4BA,CAAA,KAAyC,SAASC,qBAAqB3H,OAAA,EAAS;EACvH,MAAM;IACJlC,KAAA,GAAQ;IACR8J,cAAA,GAAiB;IACjBC,iBAAA,GAAoB;IACpBC,kBAAA,GAAqB;EACvB,IAAI9H,OAAA,IAAW,CAAC;EAChB,IAAI+H,eAAA,GAAkB,IAAI7G,KAAA,CAAoB;EAC9C,IAAIpD,KAAA,EAAO;IACT,IAAI0J,SAAA,CAAU1J,KAAK,GAAG;MACpBiK,eAAA,CAAgBxC,IAAA,CAAKxH,eAAe;IACtC,OAAO;MACLgK,eAAA,CAAgBxC,IAAA,CAAKvH,iBAAA,CAAkBF,KAAA,CAAMkK,aAAa,CAAC;IAC7D;EACF;EACA,IAAIrJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAI+I,cAAA,EAAgB;MAElB,IAAIK,gBAAA,GAA6D,CAAC;MAClE,IAAI,CAACT,SAAA,CAAUI,cAAc,GAAG;QAC9BK,gBAAA,GAAmBL,cAAA;MACrB;MACAG,eAAA,CAAgBG,OAAA,CAAQ1D,uCAAA,CAAwCyD,gBAAgB,CAAC;IAEnF;IACA,IAAIJ,iBAAA,EAAmB;MACrB,IAAIM,mBAAA,GAAmE,CAAC;MACxE,IAAI,CAACX,SAAA,CAAUK,iBAAiB,GAAG;QACjCM,mBAAA,GAAsBN,iBAAA;MACxB;MACAE,eAAA,CAAgBxC,IAAA,CAAKuB,0CAAA,CAA2CqB,mBAAmB,CAAC;IACtF;IACA,IAAIL,kBAAA,EAAoB;MACtB,IAAIM,oBAAA,GAAgE,CAAC;MACrE,IAAI,CAACZ,SAAA,CAAUM,kBAAkB,GAAG;QAClCM,oBAAA,GAAuBN,kBAAA;MACzB;MACAC,eAAA,CAAgBG,OAAA,CAAQnI,sCAAA,CAAuCqI,oBAAoB,CAAC;IACtF;EACF;EACA,OAAOL,eAAA;AACT;;;AO/EO,IAAMM,gBAAA,GAAmB;AACzB,IAAMC,kBAAA,GAAqBA,CAAA,KAAWvJ,OAAA,KAGvC;EACJA,OAAA;EACAC,IAAA,EAAM;IACJ,CAACqJ,gBAAgB,GAAG;EACtB;AACF;AACA,IAAME,oBAAA,GAAwBC,OAAA,IAAoB;EAChD,OAAQC,MAAA,IAAuB;IAC7BC,UAAA,CAAWD,MAAA,EAAQD,OAAO;EAC5B;AACF;AAmCO,IAAMG,iBAAA,GAAoBA,CAAC3I,OAAA,GAA4B;EAC5D1B,IAAA,EAAM;AACR,MAAqB2B,IAAA,IAAQ,IAAI/D,IAAA,KAAS;EACxC,MAAM0M,KAAA,GAAQ3I,IAAA,CAAK,GAAG/D,IAAI;EAC1B,IAAI2M,SAAA,GAAY;EAChB,IAAIC,uBAAA,GAA0B;EAC9B,IAAIC,kBAAA,GAAqB;EACzB,MAAMC,SAAA,GAAY,mBAAI9F,GAAA,CAAgB;EACtC,MAAM+F,aAAA,GAAgBjJ,OAAA,CAAQ1B,IAAA,KAAS,SAAS4K,cAAA,GAAiBlJ,OAAA,CAAQ1B,IAAA,KAAS;EAAA;EAElF,OAAOhB,MAAA,KAAW,eAAeA,MAAA,CAAO6L,qBAAA,GAAwB7L,MAAA,CAAO6L,qBAAA,GAAwBZ,oBAAA,CAAqB,EAAE,IAAIvI,OAAA,CAAQ1B,IAAA,KAAS,aAAa0B,OAAA,CAAQoJ,iBAAA,GAAoBb,oBAAA,CAAqBvI,OAAA,CAAQwI,OAAO;EACxN,MAAMa,eAAA,GAAkBA,CAAA,KAAM;IAG5BN,kBAAA,GAAqB;IACrB,IAAID,uBAAA,EAAyB;MAC3BA,uBAAA,GAA0B;MAC1BE,SAAA,CAAUM,OAAA,CAAQC,CAAA,IAAKA,CAAA,CAAE,CAAC;IAC5B;EACF;EACA,OAAOlN,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAGsM,KAAA,EAAO;IAAA;IAAA;IAG9BY,UAAUC,SAAA,EAAsB;MAK9B,MAAMC,eAAA,GAAmCA,CAAA,KAAMb,SAAA,IAAaY,SAAA,CAAS;MACrE,MAAME,WAAA,GAAcf,KAAA,CAAMY,SAAA,CAAUE,eAAe;MACnDV,SAAA,CAAU5F,GAAA,CAAIqG,SAAQ;MACtB,OAAO,MAAM;QACXE,WAAA,CAAY;QACZX,SAAA,CAAUY,MAAA,CAAOH,SAAQ;MAC3B;IACF;IAAA;IAAA;IAGAI,SAAS1K,MAAA,EAAa;MACpB,IAAI;QAGF0J,SAAA,GAAY,CAAC1J,MAAA,EAAQH,IAAA,GAAOqJ,gBAAgB;QAG5CS,uBAAA,GAA0B,CAACD,SAAA;QAC3B,IAAIC,uBAAA,EAAyB;UAI3B,IAAI,CAACC,kBAAA,EAAoB;YACvBA,kBAAA,GAAqB;YACrBE,aAAA,CAAcI,eAAe;UAC/B;QACF;QAOA,OAAOT,KAAA,CAAMiB,QAAA,CAAS1K,MAAM;MAC9B,UAAE;QAEA0J,SAAA,GAAY;MACd;IACF;EACF,CAAC;AACH;;;AC1GO,IAAMiB,wBAAA,GAAyDC,kBAAA,IAEvC,SAASC,oBAAoBhK,OAAA,EAAS;EACnE,MAAM;IACJiK,SAAA,GAAY;EACd,IAAIjK,OAAA,IAAW,CAAC;EAChB,IAAIkK,aAAA,GAAgB,IAAIhJ,KAAA,CAAuB6I,kBAAkB;EACjE,IAAIE,SAAA,EAAW;IACbC,aAAA,CAAc3E,IAAA,CAAKoD,iBAAA,CAAkB,OAAOsB,SAAA,KAAc,WAAWA,SAAA,GAAY,MAAS,CAAC;EAC7F;EACA,OAAOC,aAAA;AACT;;;AV8DO,SAASC,eAEYnK,OAAA,EAAuE;EACjG,MAAM2H,oBAAA,GAAuBD,yBAAA,CAA6B;EAC1D,MAAM;IACJ0C,OAAA,GAAU;IACVC,UAAA;IACAC,QAAA,GAAW;IACXC,wBAAA,GAA2B;IAC3BC,cAAA,GAAiB;IACjBC,SAAA,GAAY;EACd,IAAIzK,OAAA,IAAW,CAAC;EAChB,IAAI0K,WAAA;EACJ,IAAI,OAAON,OAAA,KAAY,YAAY;IACjCM,WAAA,GAAcN,OAAA;EAChB,WAAWhN,cAAA,CAAcgN,OAAO,GAAG;IACjCM,WAAA,GAAcxN,eAAA,CAAgBkN,OAAO;EACvC,OAAO;IACL,MAAM,IAAI1L,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,CAAC,IAAI,0HAA0H;EACjN;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBwL,UAAA,IAAc,OAAOA,UAAA,KAAe,YAAY;IAC3F,MAAM,IAAI3L,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,uCAAuC;EAC/H;EACA,IAAI6L,eAAA;EACJ,IAAI,OAAON,UAAA,KAAe,YAAY;IACpCM,eAAA,GAAkBN,UAAA,CAAW1C,oBAAoB;IACjD,IAAIhJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB,CAACuC,KAAA,CAAMU,OAAA,CAAQ6I,eAAe,GAAG;MAC5E,MAAM,IAAIjM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,mFAAmF;IAC3K;EACF,OAAO;IACL6L,eAAA,GAAkBhD,oBAAA,CAAqB;EACzC;EACA,IAAIhJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB8L,eAAA,CAAgBxG,IAAA,CAAMyG,IAAA,IAAc,OAAOA,IAAA,KAAS,UAAU,GAAG;IAC5G,MAAM,IAAIlM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,+DAA+D;EACvJ;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB0L,wBAAA,EAA0B;IACrE,IAAIM,oBAAA,GAAuB,mBAAI3H,GAAA,CAAwB;IACvDyH,eAAA,CAAgBrB,OAAA,CAAQwB,WAAA,IAAc;MACpC,IAAID,oBAAA,CAAqBzI,GAAA,CAAI0I,WAAU,GAAG;QACxC,MAAM,IAAIpM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,mHAAmH;MAC5M;MACA+L,oBAAA,CAAqBzH,GAAA,CAAI0H,WAAU;IACrC,CAAC;EACH;EACA,IAAIC,YAAA,GAAe9N,QAAA;EACnB,IAAIqN,QAAA,EAAU;IACZS,YAAA,GAAe1N,mBAAA,CAAoB;MAAA;MAEjC2N,KAAA,EAAOrM,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa;MAChC,IAAI,OAAOyL,QAAA,KAAa,YAAYA,QAAA;IACtC,CAAC;EACH;EACA,MAAMP,kBAAA,GAAqBjN,eAAA,CAAgB,GAAG6N,eAAe;EAC7D,MAAMX,mBAAA,GAAsBF,wBAAA,CAA4BC,kBAAkB;EAC1E,IAAIpL,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB4L,SAAA,IAAa,OAAOA,SAAA,KAAc,YAAY;IACzF,MAAM,IAAI/L,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,sCAAsC;EAC9H;EACA,IAAImM,cAAA,GAAiB,OAAOR,SAAA,KAAc,aAAaA,SAAA,CAAUT,mBAAmB,IAAIA,mBAAA,CAAoB;EAC5G,IAAIrL,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB,CAACuC,KAAA,CAAMU,OAAA,CAAQmJ,cAAc,GAAG;IAC3E,MAAM,IAAIvM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,2CAA2C;EACnI;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBoM,cAAA,CAAe9G,IAAA,CAAMyG,IAAA,IAAc,OAAOA,IAAA,KAAS,UAAU,GAAG;IAC3G,MAAM,IAAIlM,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,CAAC,IAAI,6DAA6D;EACrJ;EACA,IAAIH,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB8L,eAAA,CAAgBlN,MAAA,IAAU,CAACwN,cAAA,CAAeC,QAAA,CAASnB,kBAAkB,GAAG;IACnH5J,OAAA,CAAQlB,KAAA,CAAM,kIAAkI;EAClJ;EACA,MAAMkM,gBAAA,GAAuCJ,YAAA,CAAa,GAAGE,cAAc;EAC3E,OAAOlO,WAAA,CAAY2N,WAAA,EAAaF,cAAA,EAAqBW,gBAAgB;AACvE;;;AWxJA,SAAShQ,OAAA,IAAWiQ,gBAAA,EAAiB3P,OAAA,IAAA4P,QAAA,EAAS/K,WAAA,IAAAgL,YAAA,QAAmB;;;ACwG1D,SAASC,8BAAiCC,eAAA,EAAmK;EAClN,MAAMC,UAAA,GAAmC,CAAC;EAC1C,MAAMC,cAAA,GAAwD,EAAC;EAC/D,IAAIC,kBAAA;EACJ,MAAMC,OAAA,GAAU;IACdC,QAAQC,mBAAA,EAAuD1B,OAAA,EAAyB;MACtF,IAAIzL,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QAMzC,IAAI6M,cAAA,CAAejO,MAAA,GAAS,GAAG;UAC7B,MAAM,IAAIiB,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,6EAA6E;QACrK;QACA,IAAI6M,kBAAA,EAAoB;UACtB,MAAM,IAAIjN,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,iFAAiF;QAC1K;MACF;MACA,MAAMR,IAAA,GAAO,OAAOwN,mBAAA,KAAwB,WAAWA,mBAAA,GAAsBA,mBAAA,CAAoBxN,IAAA;MACjG,IAAI,CAACA,IAAA,EAAM;QACT,MAAM,IAAII,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,8DAA8D;MACvJ;MACA,IAAIR,IAAA,IAAQmN,UAAA,EAAY;QACtB,MAAM,IAAI/M,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,oFAAuFR,IAAI,GAAG;MACvL;MACAmN,UAAA,CAAWnN,IAAI,IAAI8L,OAAA;MACnB,OAAOwB,OAAA;IACT;IACAG,WAAcC,OAAA,EAAuB5B,OAAA,EAA4D;MAC/F,IAAIzL,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAI8M,kBAAA,EAAoB;UACtB,MAAM,IAAIjN,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,oFAAoF;QAC7K;MACF;MACA4M,cAAA,CAAenG,IAAA,CAAK;QAClByG,OAAA;QACA5B;MACF,CAAC;MACD,OAAOwB,OAAA;IACT;IACAK,eAAe7B,OAAA,EAAiC;MAC9C,IAAIzL,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAI8M,kBAAA,EAAoB;UACtB,MAAM,IAAIjN,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,kDAAkD;QAC3I;MACF;MACA6M,kBAAA,GAAqBvB,OAAA;MACrB,OAAOwB,OAAA;IACT;EACF;EACAJ,eAAA,CAAgBI,OAAO;EACvB,OAAO,CAACH,UAAA,EAAYC,cAAA,EAAgBC,kBAAkB;AACxD;;;ADzGA,SAASO,gBAAmBzE,CAAA,EAA0B;EACpD,OAAO,OAAOA,CAAA,KAAM;AACtB;AAqEO,SAAS0E,cAA0CC,YAAA,EAA6BC,oBAAA,EAAiG;EACtL,IAAI1N,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAI,OAAOwN,oBAAA,KAAyB,UAAU;MAC5C,MAAM,IAAI3N,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,CAAC,IAAI,8JAA8J;IACrP;EACF;EACA,IAAI,CAAC2M,UAAA,EAAYa,mBAAA,EAAqBC,uBAAuB,IAAIhB,6BAAA,CAA8Bc,oBAAoB;EAGnH,IAAIG,eAAA;EACJ,IAAIN,eAAA,CAAgBE,YAAY,GAAG;IACjCI,eAAA,GAAkBA,CAAA,KAAMzK,eAAA,CAAgBqK,YAAA,CAAa,CAAC;EACxD,OAAO;IACL,MAAMK,kBAAA,GAAqB1K,eAAA,CAAgBqK,YAAY;IACvDI,eAAA,GAAkBA,CAAA,KAAMC,kBAAA;EAC1B;EACA,SAASrC,QAAQtE,KAAA,GAAQ0G,eAAA,CAAgB,GAAGrN,MAAA,EAAgB;IAC1D,IAAIuN,YAAA,GAAe,CAACjB,UAAA,CAAWtM,MAAA,CAAOb,IAAI,GAAG,GAAGgO,mBAAA,CAAoBK,MAAA,CAAO,CAAC;MAC1EX;IACF,MAAMA,OAAA,CAAQ7M,MAAM,CAAC,EAAE+C,GAAA,CAAI,CAAC;MAC1BkI,OAAA,EAAAwC;IACF,MAAMA,QAAO,CAAC;IACd,IAAIF,YAAA,CAAaC,MAAA,CAAOE,EAAA,IAAM,CAAC,CAACA,EAAE,EAAEpP,MAAA,KAAW,GAAG;MAChDiP,YAAA,GAAe,CAACH,uBAAuB;IACzC;IACA,OAAOG,YAAA,CAAaI,MAAA,CAAO,CAACC,aAAA,EAAeC,WAAA,KAAmB;MAC5D,IAAIA,WAAA,EAAa;QACf,IAAI3B,QAAA,CAAQ0B,aAAa,GAAG;UAI1B,MAAME,KAAA,GAAQF,aAAA;UACd,MAAMxI,MAAA,GAASyI,WAAA,CAAYC,KAAA,EAAO9N,MAAM;UACxC,IAAIoF,MAAA,KAAW,QAAW;YACxB,OAAOwI,aAAA;UACT;UACA,OAAOxI,MAAA;QACT,WAAW,CAAC+G,YAAA,CAAYyB,aAAa,GAAG;UAGtC,MAAMxI,MAAA,GAASyI,WAAA,CAAYD,aAAA,EAAsB5N,MAAM;UACvD,IAAIoF,MAAA,KAAW,QAAW;YACxB,IAAIwI,aAAA,KAAkB,MAAM;cAC1B,OAAOA,aAAA;YACT;YACA,MAAMrO,KAAA,CAAM,mEAAmE;UACjF;UACA,OAAO6F,MAAA;QACT,OAAO;UAIL,OAAO6G,gBAAA,CAAgB2B,aAAA,EAAgBE,KAAA,IAAoB;YACzD,OAAOD,WAAA,CAAYC,KAAA,EAAO9N,MAAM;UAClC,CAAC;QACH;MACF;MACA,OAAO4N,aAAA;IACT,GAAGjH,KAAK;EACV;EACAsE,OAAA,CAAQoC,eAAA,GAAkBA,eAAA;EAC1B,OAAOpC,OAAA;AACT;;;AElLA,IAAM8C,OAAA,GAAUA,CAAClB,OAAA,EAAuB7M,MAAA,KAAgB;EACtD,IAAIjB,gBAAA,CAAiB8N,OAAO,GAAG;IAC7B,OAAOA,OAAA,CAAQ5N,KAAA,CAAMe,MAAM;EAC7B,OAAO;IACL,OAAO6M,OAAA,CAAQ7M,MAAM;EACvB;AACF;AAWO,SAASgO,QAAA,GAA4CC,QAAA,EAAoB;EAC9E,OAAQjO,MAAA,IAAyD;IAC/D,OAAOiO,QAAA,CAASjJ,IAAA,CAAK6H,OAAA,IAAWkB,OAAA,CAAQlB,OAAA,EAAS7M,MAAM,CAAC;EAC1D;AACF;AAWO,SAASkO,QAAA,GAA4CD,QAAA,EAAoB;EAC9E,OAAQjO,MAAA,IAAyD;IAC/D,OAAOiO,QAAA,CAAS7N,KAAA,CAAMyM,OAAA,IAAWkB,OAAA,CAAQlB,OAAA,EAAS7M,MAAM,CAAC;EAC3D;AACF;AAQO,SAASmO,2BAA2BnO,MAAA,EAAaoO,WAAA,EAAgC;EACtF,IAAI,CAACpO,MAAA,IAAU,CAACA,MAAA,CAAOH,IAAA,EAAM,OAAO;EACpC,MAAMwO,iBAAA,GAAoB,OAAOrO,MAAA,CAAOH,IAAA,CAAKyO,SAAA,KAAc;EAC3D,MAAMC,qBAAA,GAAwBH,WAAA,CAAY7N,OAAA,CAAQP,MAAA,CAAOH,IAAA,CAAK2O,aAAa,IAAI;EAC/E,OAAOH,iBAAA,IAAqBE,qBAAA;AAC9B;AACA,SAASE,kBAAkBC,CAAA,EAAkD;EAC3E,OAAO,OAAOA,CAAA,CAAE,CAAC,MAAM,cAAc,aAAaA,CAAA,CAAE,CAAC,KAAK,eAAeA,CAAA,CAAE,CAAC,KAAK,cAAcA,CAAA,CAAE,CAAC;AACpG;AA2BO,SAASC,UAAA,GAAsEC,WAAA,EAAkC;EACtH,IAAIA,WAAA,CAAYtQ,MAAA,KAAW,GAAG;IAC5B,OAAQ0B,MAAA,IAAgBmO,0BAAA,CAA2BnO,MAAA,EAAQ,CAAC,SAAS,CAAC;EACxE;EACA,IAAI,CAACyO,iBAAA,CAAkBG,WAAW,GAAG;IACnC,OAAOD,SAAA,CAAU,EAAEC,WAAA,CAAY,CAAC,CAAC;EACnC;EACA,OAAOZ,OAAA,CAAQ,GAAGY,WAAA,CAAY7L,GAAA,CAAI8L,UAAA,IAAcA,UAAA,CAAWC,OAAO,CAAC;AACrE;AA2BO,SAASC,WAAA,GAAuEH,WAAA,EAAkC;EACvH,IAAIA,WAAA,CAAYtQ,MAAA,KAAW,GAAG;IAC5B,OAAQ0B,MAAA,IAAgBmO,0BAAA,CAA2BnO,MAAA,EAAQ,CAAC,UAAU,CAAC;EACzE;EACA,IAAI,CAACyO,iBAAA,CAAkBG,WAAW,GAAG;IACnC,OAAOG,UAAA,CAAW,EAAEH,WAAA,CAAY,CAAC,CAAC;EACpC;EACA,OAAOZ,OAAA,CAAQ,GAAGY,WAAA,CAAY7L,GAAA,CAAI8L,UAAA,IAAcA,UAAA,CAAWG,QAAQ,CAAC;AACtE;AA+BO,SAASC,oBAAA,GAAgFL,WAAA,EAAkC;EAChI,MAAMM,OAAA,GAAWlP,MAAA,IAA+B;IAC9C,OAAOA,MAAA,IAAUA,MAAA,CAAOH,IAAA,IAAQG,MAAA,CAAOH,IAAA,CAAKsP,iBAAA;EAC9C;EACA,IAAIP,WAAA,CAAYtQ,MAAA,KAAW,GAAG;IAC5B,OAAO4P,OAAA,CAAQa,UAAA,CAAW,GAAGH,WAAW,GAAGM,OAAO;EACpD;EACA,IAAI,CAACT,iBAAA,CAAkBG,WAAW,GAAG;IACnC,OAAOK,mBAAA,CAAoB,EAAEL,WAAA,CAAY,CAAC,CAAC;EAC7C;EACA,OAAOV,OAAA,CAAQa,UAAA,CAAW,GAAGH,WAAW,GAAGM,OAAO;AACpD;AA2BO,SAASE,YAAA,GAAwER,WAAA,EAAkC;EACxH,IAAIA,WAAA,CAAYtQ,MAAA,KAAW,GAAG;IAC5B,OAAQ0B,MAAA,IAAgBmO,0BAAA,CAA2BnO,MAAA,EAAQ,CAAC,WAAW,CAAC;EAC1E;EACA,IAAI,CAACyO,iBAAA,CAAkBG,WAAW,GAAG;IACnC,OAAOQ,WAAA,CAAY,EAAER,WAAA,CAAY,CAAC,CAAC;EACrC;EACA,OAAOZ,OAAA,CAAQ,GAAGY,WAAA,CAAY7L,GAAA,CAAI8L,UAAA,IAAcA,UAAA,CAAWQ,SAAS,CAAC;AACvE;AAoCO,SAASC,mBAAA,GAA+EV,WAAA,EAAkC;EAC/H,IAAIA,WAAA,CAAYtQ,MAAA,KAAW,GAAG;IAC5B,OAAQ0B,MAAA,IAAgBmO,0BAAA,CAA2BnO,MAAA,EAAQ,CAAC,WAAW,aAAa,UAAU,CAAC;EACjG;EACA,IAAI,CAACyO,iBAAA,CAAkBG,WAAW,GAAG;IACnC,OAAOU,kBAAA,CAAmB,EAAEV,WAAA,CAAY,CAAC,CAAC;EAC5C;EACA,OAAOZ,OAAA,CAAQ,GAAGY,WAAA,CAAYW,OAAA,CAAQV,UAAA,IAAc,CAACA,UAAA,CAAWC,OAAA,EAASD,UAAA,CAAWG,QAAA,EAAUH,UAAA,CAAWQ,SAAS,CAAC,CAAC;AACtH;;;ACzPA,IAAIG,WAAA,GAAc;AAMX,IAAIC,MAAA,GAASA,CAACC,IAAA,GAAO,OAAO;EACjC,IAAIC,EAAA,GAAK;EAET,IAAIC,CAAA,GAAIF,IAAA;EACR,OAAOE,CAAA,IAAK;IAEVD,EAAA,IAAMH,WAAA,CAAYK,IAAA,CAAKC,MAAA,CAAO,IAAI,KAAK,CAAC;EAC1C;EACA,OAAOH,EAAA;AACT;;;ACSA,IAAMI,gBAAA,GAAiD,CAAC,QAAQ,WAAW,SAAS,MAAM;AAC1F,IAAMC,eAAA,GAAN,MAA6C;EAM3C9N,YAA4BtC,OAAA,EAAkCC,IAAA,EAAoB;IAAtD,KAAAD,OAAA,GAAAA,OAAA;IAAkC,KAAAC,IAAA,GAAAA,IAAA;EAAqB;EAAA;AAAA;AAAA;AAAA;EADlEoQ,KAAA;AAEnB;AACA,IAAMC,eAAA,GAAN,MAA8C;EAM5ChO,YAA4BtC,OAAA,EAAkCC,IAAA,EAAqB;IAAvD,KAAAD,OAAA,GAAAA,OAAA;IAAkC,KAAAC,IAAA,GAAAA,IAAA;EAAsB;EAAA;AAAA;AAAA;AAAA;EADnEoQ,KAAA;AAEnB;AAQO,IAAME,kBAAA,GAAsB5S,KAAA,IAAgC;EACjE,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,MAAM;IAC/C,MAAM6S,WAAA,GAA+B,CAAC;IACtC,WAAWC,QAAA,IAAYN,gBAAA,EAAkB;MACvC,IAAI,OAAOxS,KAAA,CAAM8S,QAAQ,MAAM,UAAU;QACvCD,WAAA,CAAYC,QAAQ,IAAI9S,KAAA,CAAM8S,QAAQ;MACxC;IACF;IACA,OAAOD,WAAA;EACT;EACA,OAAO;IACLE,OAAA,EAASC,MAAA,CAAOhT,KAAK;EACvB;AACF;AA4MA,IAAMiT,oBAAA,GAAuB;AACtB,IAAMC,gBAAA,GAAmC,sBAAM;EACpD,SAASC,kBAA8EC,UAAA,EAAoBC,cAAA,EAA8E/P,OAAA,EAAuG;IAK9R,MAAMwO,SAAA,GAAkFnQ,YAAA,CAAayR,UAAA,GAAa,cAAc,CAAC/Q,OAAA,EAAmB0O,SAAA,EAAmBuC,GAAA,EAAehR,IAAA,MAA0B;MAC9MD,OAAA;MACAC,IAAA,EAAM;QACJ,IAAIA,IAAA,IAAe,CAAC;QACpBgR,GAAA;QACAvC,SAAA;QACAE,aAAA,EAAe;MACjB;IACF,EAAE;IACF,MAAMM,OAAA,GAAoE5P,YAAA,CAAayR,UAAA,GAAa,YAAY,CAACrC,SAAA,EAAmBuC,GAAA,EAAehR,IAAA,MAAwB;MACzKD,OAAA,EAAS;MACTC,IAAA,EAAM;QACJ,IAAIA,IAAA,IAAe,CAAC;QACpBgR,GAAA;QACAvC,SAAA;QACAE,aAAA,EAAe;MACjB;IACF,EAAE;IACF,MAAMQ,QAAA,GAAsE9P,YAAA,CAAayR,UAAA,GAAa,aAAa,CAAC7Q,KAAA,EAAqBwO,SAAA,EAAmBuC,GAAA,EAAejR,OAAA,EAAyBC,IAAA,MAAyB;MAC3ND,OAAA;MACAE,KAAA,GAAQe,OAAA,IAAWA,OAAA,CAAQiQ,cAAA,IAAkBX,kBAAA,EAAoBrQ,KAAA,IAAS,UAAU;MACpFD,IAAA,EAAM;QACJ,IAAIA,IAAA,IAAe,CAAC;QACpBgR,GAAA;QACAvC,SAAA;QACAa,iBAAA,EAAmB,CAAC,CAACvP,OAAA;QACrB4O,aAAA,EAAe;QACfuC,OAAA,EAASjR,KAAA,EAAOkR,IAAA,KAAS;QACzBC,SAAA,EAAWnR,KAAA,EAAOkR,IAAA,KAAS;MAC7B;IACF,EAAE;IACF,SAAS3R,cAAcwR,GAAA,EAAe;MACpCK;IACF,IAA8B,CAAC,GAAmE;MAChG,OAAO,CAACxG,QAAA,EAAUhE,QAAA,EAAUyK,KAAA,KAAU;QACpC,MAAM7C,SAAA,GAAYzN,OAAA,EAASuQ,WAAA,GAAcvQ,OAAA,CAAQuQ,WAAA,CAAYP,GAAG,IAAIpB,MAAA,CAAO;QAC3E,MAAM4B,eAAA,GAAkB,IAAIC,eAAA,CAAgB;QAC5C,IAAIC,YAAA;QACJ,IAAIC,WAAA;QACJ,SAASC,MAAMC,MAAA,EAAiB;UAC9BF,WAAA,GAAcE,MAAA;UACdL,eAAA,CAAgBI,KAAA,CAAM;QACxB;QACA,IAAIP,MAAA,EAAQ;UACV,IAAIA,MAAA,CAAOH,OAAA,EAAS;YAClBU,KAAA,CAAMjB,oBAAoB;UAC5B,OAAO;YACLU,MAAA,CAAOS,gBAAA,CAAiB,SAAS,MAAMF,KAAA,CAAMjB,oBAAoB,GAAG;cAClEoB,IAAA,EAAM;YACR,CAAC;UACH;QACF;QACA,MAAMC,OAAA,GAAU,kBAAkB;UAChC,IAAIC,WAAA;UACJ,IAAI;YACF,IAAIC,eAAA,GAAkBlR,OAAA,EAASoQ,SAAA,GAAYJ,GAAA,EAAK;cAC9CnK,QAAA;cACAyK;YACF,CAAC;YACD,IAAIa,UAAA,CAAWD,eAAe,GAAG;cAC/BA,eAAA,GAAkB,MAAMA,eAAA;YAC1B;YACA,IAAIA,eAAA,KAAoB,SAASV,eAAA,CAAgBH,MAAA,CAAOH,OAAA,EAAS;cAE/D,MAAM;gBACJC,IAAA,EAAM;gBACNV,OAAA,EAAS;cACX;YACF;YACA,MAAM2B,cAAA,GAAiB,IAAIC,OAAA,CAAe,CAACnM,CAAA,EAAGoM,MAAA,KAAW;cACvDZ,YAAA,GAAeA,CAAA,KAAM;gBACnBY,MAAA,CAAO;kBACLnB,IAAA,EAAM;kBACNV,OAAA,EAASkB,WAAA,IAAe;gBAC1B,CAAC;cACH;cACAH,eAAA,CAAgBH,MAAA,CAAOS,gBAAA,CAAiB,SAASJ,YAAY;YAC/D,CAAC;YACD7G,QAAA,CAASoE,OAAA,CAAQR,SAAA,EAAWuC,GAAA,EAAKhQ,OAAA,EAASuR,cAAA,GAAiB;cACzD9D,SAAA;cACAuC;YACF,GAAG;cACDnK,QAAA;cACAyK;YACF,CAAC,CAAC,CAAQ;YACVW,WAAA,GAAc,MAAMI,OAAA,CAAQG,IAAA,CAAK,CAACJ,cAAA,EAAgBC,OAAA,CAAQI,OAAA,CAAQ1B,cAAA,CAAeC,GAAA,EAAK;cACpFnG,QAAA;cACAhE,QAAA;cACAyK,KAAA;cACA7C,SAAA;cACA4C,MAAA,EAAQG,eAAA,CAAgBH,MAAA;cACxBO,KAAA;cACAc,eAAA,EAAkBA,CAAChV,KAAA,EAAsBsC,IAAA,KAAwB;gBAC/D,OAAO,IAAImQ,eAAA,CAAgBzS,KAAA,EAAOsC,IAAI;cACxC;cACA2S,gBAAA,EAAmBA,CAACjV,KAAA,EAAgBsC,IAAA,KAAyB;gBAC3D,OAAO,IAAIqQ,eAAA,CAAgB3S,KAAA,EAAOsC,IAAI;cACxC;YACF,CAAC,CAAC,EAAE4S,IAAA,CAAKrN,MAAA,IAAU;cACjB,IAAIA,MAAA,YAAkB4K,eAAA,EAAiB;gBACrC,MAAM5K,MAAA;cACR;cACA,IAAIA,MAAA,YAAkB8K,eAAA,EAAiB;gBACrC,OAAOb,SAAA,CAAUjK,MAAA,CAAOxF,OAAA,EAAS0O,SAAA,EAAWuC,GAAA,EAAKzL,MAAA,CAAOvF,IAAI;cAC9D;cACA,OAAOwP,SAAA,CAAUjK,MAAA,EAAekJ,SAAA,EAAWuC,GAAG;YAChD,CAAC,CAAC,CAAC;UACL,SAAS6B,GAAA,EAAK;YACZZ,WAAA,GAAcY,GAAA,YAAe1C,eAAA,GAAkBhB,QAAA,CAAS,MAAMV,SAAA,EAAWuC,GAAA,EAAK6B,GAAA,CAAI9S,OAAA,EAAS8S,GAAA,CAAI7S,IAAI,IAAImP,QAAA,CAAS0D,GAAA,EAAYpE,SAAA,EAAWuC,GAAG;UAC5I,UAAE;YACA,IAAIU,YAAA,EAAc;cAChBF,eAAA,CAAgBH,MAAA,CAAOyB,mBAAA,CAAoB,SAASpB,YAAY;YAClE;UACF;UAMA,MAAMqB,YAAA,GAAe/R,OAAA,IAAW,CAACA,OAAA,CAAQgS,0BAAA,IAA8B7D,QAAA,CAAS/P,KAAA,CAAM6S,WAAW,KAAMA,WAAA,CAAoBjS,IAAA,CAAKoR,SAAA;UAChI,IAAI,CAAC2B,YAAA,EAAc;YACjBlI,QAAA,CAASoH,WAAkB;UAC7B;UACA,OAAOA,WAAA;QACT,EAAE;QACF,OAAO5U,MAAA,CAAOC,MAAA,CAAO0U,OAAA,EAA6B;UAChDJ,KAAA;UACAnD,SAAA;UACAuC,GAAA;UACAiC,OAAA,EAAS;YACP,OAAOjB,OAAA,CAAQY,IAAA,CAAUM,YAAY;UACvC;QACF,CAAC;MACH;IACF;IACA,OAAO7V,MAAA,CAAOC,MAAA,CAAOkC,aAAA,EAA8E;MACjGyP,OAAA;MACAE,QAAA;MACAK,SAAA;MACA2D,OAAA,EAAShF,OAAA,CAAQgB,QAAA,EAAUK,SAAS;MACpCsB;IACF,CAAC;EACH;EACAD,iBAAA,CAAiBjT,SAAA,GAAY,MAAMiT,iBAAA;EACnC,OAAOA,iBAAA;AACT,GAAG;AAaI,SAASqC,aAA0C/S,MAAA,EAAsC;EAC9F,IAAIA,MAAA,CAAOH,IAAA,IAAQG,MAAA,CAAOH,IAAA,CAAKsP,iBAAA,EAAmB;IAChD,MAAMnP,MAAA,CAAOJ,OAAA;EACf;EACA,IAAII,MAAA,CAAOF,KAAA,EAAO;IAChB,MAAME,MAAA,CAAOF,KAAA;EACf;EACA,OAAOE,MAAA,CAAOJ,OAAA;AAChB;AAEA,SAASoS,WAAWzU,KAAA,EAAuC;EACzD,OAAOA,KAAA,KAAU,QAAQ,OAAOA,KAAA,KAAU,YAAY,OAAOA,KAAA,CAAMkV,IAAA,KAAS;AAC9E;;;AC/aA,IAAMQ,gBAAA,GAAkC,eAAA3Q,MAAA,CAAO4Q,GAAA,CAAI,4BAA4B;AAExE,IAAMC,iBAAA,GAET;EACF,CAACF,gBAAgB,GAAGxC;AACtB;AAwLO,IAAK2C,WAAA,GAAL,gBAAKC,YAAA,IAAL;EACLA,YAAA,cAAU;EACVA,YAAA,yBAAqB;EACrBA,YAAA,iBAAa;EAHH,OAAAA,YAAA;AAAA,GAAAD,WAAA;AAoIZ,SAASE,QAAQtN,KAAA,EAAeuN,SAAA,EAA2B;EACzD,OAAO,GAAGvN,KAAK,IAAIuN,SAAS;AAC9B;AAMO,SAASC,iBAAiB;EAC/BC;AACF,IAA4B,CAAC,GAAG;EAC9B,MAAMC,GAAA,GAAMD,QAAA,EAAU5E,UAAA,GAAaoE,gBAAgB;EACnD,OAAO,SAASU,aAAmK9S,OAAA,EAA0I;IAC3T,MAAM;MACJmQ,IAAA;MACA4C,WAAA,GAAc5C;IAChB,IAAInQ,OAAA;IACJ,IAAI,CAACmQ,IAAA,EAAM;MACT,MAAM,IAAIzR,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,6CAA6C;IACrI;IACA,IAAI,OAAOH,OAAA,KAAY,eAAeA,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAe;MAC5E,IAAImB,OAAA,CAAQoM,YAAA,KAAiB,QAAW;QACtCjM,OAAA,CAAQlB,KAAA,CAAM,0GAA0G;MAC1H;IACF;IACA,MAAM+T,QAAA,IAAY,OAAOhT,OAAA,CAAQgT,QAAA,KAAa,aAAahT,OAAA,CAAQgT,QAAA,CAASC,oBAAA,CAA4B,CAAC,IAAIjT,OAAA,CAAQgT,QAAA,KAAa,CAAC;IACnI,MAAME,YAAA,GAAe7W,MAAA,CAAOiD,IAAA,CAAK0T,QAAQ;IACzC,MAAMG,OAAA,GAAyC;MAC7CC,uBAAA,EAAyB,CAAC;MAC1BC,uBAAA,EAAyB,CAAC;MAC1BC,cAAA,EAAgB,CAAC;MACjBC,aAAA,EAAe;IACjB;IACA,MAAMC,cAAA,GAAuD;MAC3D3H,QAAQC,mBAAA,EAAuDc,QAAA,EAA6B;QAC1F,MAAMtO,IAAA,GAAO,OAAOwN,mBAAA,KAAwB,WAAWA,mBAAA,GAAsBA,mBAAA,CAAoBxN,IAAA;QACjG,IAAI,CAACA,IAAA,EAAM;UACT,MAAM,IAAII,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,8DAA8D;QACvJ;QACA,IAAIR,IAAA,IAAQ6U,OAAA,CAAQE,uBAAA,EAAyB;UAC3C,MAAM,IAAI3U,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,oFAAoFR,IAAI;QACjL;QACA6U,OAAA,CAAQE,uBAAA,CAAwB/U,IAAI,IAAIsO,QAAA;QACxC,OAAO4G,cAAA;MACT;MACAzH,WAAWC,OAAA,EAASY,QAAA,EAAS;QAC3BuG,OAAA,CAAQI,aAAA,CAAchO,IAAA,CAAK;UACzByG,OAAA;UACA5B,OAAA,EAAAwC;QACF,CAAC;QACD,OAAO4G,cAAA;MACT;MACAC,aAAaC,KAAA,EAAMlV,aAAA,EAAe;QAChC2U,OAAA,CAAQG,cAAA,CAAeI,KAAI,IAAIlV,aAAA;QAC/B,OAAOgV,cAAA;MACT;MACAG,kBAAkBD,KAAA,EAAM9G,QAAA,EAAS;QAC/BuG,OAAA,CAAQC,uBAAA,CAAwBM,KAAI,IAAI9G,QAAA;QACxC,OAAO4G,cAAA;MACT;IACF;IACAN,YAAA,CAAa5J,OAAA,CAAQsK,WAAA,IAAe;MAClC,MAAMC,iBAAA,GAAoBb,QAAA,CAASY,WAAW;MAC9C,MAAME,cAAA,GAAiC;QACrCF,WAAA;QACAtV,IAAA,EAAMmU,OAAA,CAAQtC,IAAA,EAAMyD,WAAW;QAC/BG,cAAA,EAAgB,OAAO/T,OAAA,CAAQgT,QAAA,KAAa;MAC9C;MACA,IAAIgB,kCAAA,CAA0CH,iBAAiB,GAAG;QAChEI,gCAAA,CAAiCH,cAAA,EAAgBD,iBAAA,EAAmBL,cAAA,EAAgBX,GAAG;MACzF,OAAO;QACLqB,6BAAA,CAAqCJ,cAAA,EAAgBD,iBAAA,EAA0BL,cAAc;MAC/F;IACF,CAAC;IACD,SAASW,aAAA,EAAe;MACtB,IAAIxV,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAI,OAAOmB,OAAA,CAAQoU,aAAA,KAAkB,UAAU;UAC7C,MAAM,IAAI1V,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,wKAAwK;QACjQ;MACF;MACA,MAAM,CAACsV,aAAA,GAAgB,CAAC,GAAG1I,cAAA,GAAiB,EAAC,EAAGC,kBAAA,GAAqB,MAAS,IAAI,OAAO3L,OAAA,CAAQoU,aAAA,KAAkB,aAAa7I,6BAAA,CAA8BvL,OAAA,CAAQoU,aAAa,IAAI,CAACpU,OAAA,CAAQoU,aAAa;MAC7M,MAAMC,iBAAA,GAAoB;QACxB,GAAGD,aAAA;QACH,GAAGjB,OAAA,CAAQE;MACb;MACA,OAAOlH,aAAA,CAAcnM,OAAA,CAAQoM,YAAA,EAAcR,OAAA,IAAW;QACpD,SAASnM,GAAA,IAAO4U,iBAAA,EAAmB;UACjCzI,OAAA,CAAQC,OAAA,CAAQpM,GAAA,EAAK4U,iBAAA,CAAkB5U,GAAG,CAAqB;QACjE;QACA,SAAS6U,EAAA,IAAMnB,OAAA,CAAQI,aAAA,EAAe;UACpC3H,OAAA,CAAQG,UAAA,CAAWuI,EAAA,CAAGtI,OAAA,EAASsI,EAAA,CAAGlK,OAAO;QAC3C;QACA,SAASmK,CAAA,IAAK7I,cAAA,EAAgB;UAC5BE,OAAA,CAAQG,UAAA,CAAWwI,CAAA,CAAEvI,OAAA,EAASuI,CAAA,CAAEnK,OAAO;QACzC;QACA,IAAIuB,kBAAA,EAAoB;UACtBC,OAAA,CAAQK,cAAA,CAAeN,kBAAkB;QAC3C;MACF,CAAC;IACH;IACA,MAAM6I,UAAA,GAAc1O,KAAA,IAAiBA,KAAA;IACrC,MAAM2O,qBAAA,GAAwB,mBAAIC,GAAA,CAAsG;IACxI,MAAMC,kBAAA,GAAqB,mBAAIC,OAAA,CAA0C;IACzE,IAAIC,QAAA;IACJ,SAASzK,QAAQtE,KAAA,EAA0B3G,MAAA,EAAuB;MAChE,IAAI,CAAC0V,QAAA,EAAUA,QAAA,GAAWV,YAAA,CAAa;MACvC,OAAOU,QAAA,CAAS/O,KAAA,EAAO3G,MAAM;IAC/B;IACA,SAASqN,gBAAA,EAAkB;MACzB,IAAI,CAACqI,QAAA,EAAUA,QAAA,GAAWV,YAAA,CAAa;MACvC,OAAOU,QAAA,CAASrI,eAAA,CAAgB;IAClC;IACA,SAASsI,kBAAmEC,YAAA,EAAiCC,QAAA,GAAW,OAA4I;MAClQ,SAASC,YAAYnP,KAAA,EAA6C;QAChE,IAAIoP,UAAA,GAAapP,KAAA,CAAMiP,YAAW;QAClC,IAAI,OAAOG,UAAA,KAAe,aAAa;UACrC,IAAIF,QAAA,EAAU;YACZE,UAAA,GAAajT,mBAAA,CAAoB0S,kBAAA,EAAoBM,WAAA,EAAazI,eAAe;UACnF,WAAW7N,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;YAChD,MAAM,IAAIH,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,gEAAgE;UACzJ;QACF;QACA,OAAOoW,UAAA;MACT;MACA,SAASC,aAAaC,WAAA,GAAyCZ,UAAA,EAAY;QACzE,MAAMa,aAAA,GAAgBpT,mBAAA,CAAoBwS,qBAAA,EAAuBO,QAAA,EAAU,MAAM,mBAAIJ,OAAA,CAAQ,CAAC;QAC9F,OAAO3S,mBAAA,CAAoBoT,aAAA,EAAeD,WAAA,EAAa,MAAM;UAC3D,MAAMlT,GAAA,GAA0C,CAAC;UACjD,WAAW,CAACwR,KAAA,EAAMlX,QAAQ,KAAKH,MAAA,CAAOqK,OAAA,CAAQ1G,OAAA,CAAQsV,SAAA,IAAa,CAAC,CAAC,GAAG;YACtEpT,GAAA,CAAIwR,KAAI,IAAI6B,YAAA,CAAa/Y,QAAA,EAAU4Y,WAAA,EAAa,MAAMnT,mBAAA,CAAoB0S,kBAAA,EAAoBS,WAAA,EAAa5I,eAAe,GAAGwI,QAAQ;UACvI;UACA,OAAO9S,GAAA;QACT,CAAC;MACH;MACA,OAAO;QACL6Q,WAAA,EAAAgC,YAAA;QACAI,YAAA;QACA,IAAIG,UAAA,EAAY;UACd,OAAOH,YAAA,CAAaF,WAAW;QACjC;QACAA;MACF;IACF;IACA,MAAM9P,KAAA,GAAkE;MACtEgL,IAAA;MACA/F,OAAA;MACAoL,OAAA,EAASrC,OAAA,CAAQG,cAAA;MACjB5G,YAAA,EAAcyG,OAAA,CAAQC,uBAAA;MACtB5G,eAAA;MACA,GAAGsI,iBAAA,CAAkB/B,WAAW;MAChC0C,WAAWC,UAAA,EAAY;QACrB3C,WAAA,EAAa4C,OAAA;QACb,GAAGC;MACL,IAAI,CAAC,GAAG;QACN,MAAMC,cAAA,GAAiBF,OAAA,IAAW5C,WAAA;QAClC2C,UAAA,CAAWI,MAAA,CAAO;UAChB/C,WAAA,EAAa8C,cAAA;UACbzL;QACF,GAAGwL,MAAM;QACT,OAAO;UACL,GAAGzQ,KAAA;UACH,GAAG2P,iBAAA,CAAkBe,cAAA,EAAgB,IAAI;QAC3C;MACF;IACF;IACA,OAAO1Q,KAAA;EACT;AACF;AACA,SAASoQ,aAAyD/Y,QAAA,EAAa4Y,WAAA,EAAwC5I,eAAA,EAA8BwI,QAAA,EAAoB;EACvK,SAASe,QAAQC,SAAA,KAAwB9Z,IAAA,EAAa;IACpD,IAAIgZ,UAAA,GAAaE,WAAA,CAAYY,SAAS;IACtC,IAAI,OAAOd,UAAA,KAAe,aAAa;MACrC,IAAIF,QAAA,EAAU;QACZE,UAAA,GAAa1I,eAAA,CAAgB;MAC/B,WAAW7N,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QAChD,MAAM,IAAIH,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,gEAAgE;MACzJ;IACF;IACA,OAAOtC,QAAA,CAAS0Y,UAAA,EAAY,GAAGhZ,IAAI;EACrC;EACA6Z,OAAA,CAAQE,SAAA,GAAYzZ,QAAA;EACpB,OAAOuZ,OAAA;AACT;AAUO,IAAMG,WAAA,GAA6B,eAAAvD,gBAAA,CAAiB;AAkE3D,SAASM,qBAAA,EAAsD;EAC7D,SAASjF,WAAW+B,cAAA,EAAoD6F,MAAA,EAAgG;IACtK,OAAO;MACLO,sBAAA,EAAwB;MACxBpG,cAAA;MACA,GAAG6F;IACL;EACF;EACA5H,UAAA,CAAWpR,SAAA,GAAY,MAAMoR,UAAA;EAC7B,OAAO;IACL5D,QAAQ4C,WAAA,EAAsC;MAC5C,OAAO3Q,MAAA,CAAOC,MAAA,CAAO;QAAA;QAAA;QAGnB,CAAC0Q,WAAA,CAAYmD,IAAI,KAAKjU,IAAA,EAAsC;UAC1D,OAAO8Q,WAAA,CAAY,GAAG9Q,IAAI;QAC5B;MACF,EAAE8Q,WAAA,CAAYmD,IAAI,GAAG;QACnBgG,sBAAA,EAAwB;MAC1B,CAAU;IACZ;IACAC,gBAAgBC,OAAA,EAASjM,OAAA,EAAS;MAChC,OAAO;QACL+L,sBAAA,EAAwB;QACxBE,OAAA;QACAjM;MACF;IACF;IACA4D;EACF;AACF;AACA,SAASkG,8BAAqC;EAC5C5V,IAAA;EACAsV,WAAA;EACAG;AACF,GAAmBuC,uBAAA,EAGuDnD,OAAA,EAA+C;EACvH,IAAInG,WAAA;EACJ,IAAIuJ,eAAA;EACJ,IAAI,aAAaD,uBAAA,EAAyB;IACxC,IAAIvC,cAAA,IAAkB,CAACyC,kCAAA,CAAmCF,uBAAuB,GAAG;MAClF,MAAM,IAAI5X,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,2GAA2G;IACpM;IACAkO,WAAA,GAAcsJ,uBAAA,CAAwBlM,OAAA;IACtCmM,eAAA,GAAkBD,uBAAA,CAAwBD,OAAA;EAC5C,OAAO;IACLrJ,WAAA,GAAcsJ,uBAAA;EAChB;EACAnD,OAAA,CAAQtH,OAAA,CAAQvN,IAAA,EAAM0O,WAAW,EAAE2G,iBAAA,CAAkBC,WAAA,EAAa5G,WAAW,EAAEyG,YAAA,CAAaG,WAAA,EAAa2C,eAAA,GAAkBlY,YAAA,CAAaC,IAAA,EAAMiY,eAAe,IAAIlY,YAAA,CAAaC,IAAI,CAAC;AACrL;AACA,SAAS0V,mCAA0CH,iBAAA,EAAqG;EACtJ,OAAOA,iBAAA,CAAkBsC,sBAAA,KAA2B;AACtD;AACA,SAASK,mCAA0C3C,iBAAA,EAA2F;EAC5I,OAAOA,iBAAA,CAAkBsC,sBAAA,KAA2B;AACtD;AACA,SAASlC,iCAAwC;EAC/C3V,IAAA;EACAsV;AACF,GAAmBC,iBAAA,EAA2EV,OAAA,EAA+CN,GAAA,EAA2C;EACtL,IAAI,CAACA,GAAA,EAAK;IACR,MAAM,IAAInU,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,wLAA6L;EACtR;EACA,MAAM;IACJiR,cAAA;IACAvB,SAAA;IACAP,OAAA;IACAE,QAAA;IACAgE,OAAA;IACAnS;EACF,IAAI6T,iBAAA;EACJ,MAAM/V,KAAA,GAAQ+U,GAAA,CAAIvU,IAAA,EAAMyR,cAAA,EAAgB/P,OAAc;EACtDmT,OAAA,CAAQM,YAAA,CAAaG,WAAA,EAAa9V,KAAK;EACvC,IAAI0Q,SAAA,EAAW;IACb2E,OAAA,CAAQtH,OAAA,CAAQ/N,KAAA,CAAM0Q,SAAA,EAAWA,SAAS;EAC5C;EACA,IAAIP,OAAA,EAAS;IACXkF,OAAA,CAAQtH,OAAA,CAAQ/N,KAAA,CAAMmQ,OAAA,EAASA,OAAO;EACxC;EACA,IAAIE,QAAA,EAAU;IACZgF,OAAA,CAAQtH,OAAA,CAAQ/N,KAAA,CAAMqQ,QAAA,EAAUA,QAAQ;EAC1C;EACA,IAAIgE,OAAA,EAAS;IACXgB,OAAA,CAAQpH,UAAA,CAAWjO,KAAA,CAAMqU,OAAA,EAASA,OAAO;EAC3C;EACAgB,OAAA,CAAQQ,iBAAA,CAAkBC,WAAA,EAAa;IACrCpF,SAAA,EAAWA,SAAA,IAAaiI,IAAA;IACxBxI,OAAA,EAASA,OAAA,IAAWwI,IAAA;IACpBtI,QAAA,EAAUA,QAAA,IAAYsI,IAAA;IACtBtE,OAAA,EAASA,OAAA,IAAWsE;EACtB,CAAC;AACH;AACA,SAASA,KAAA,EAAO,CAAC;;;AC/qBV,SAASC,sBAAA,EAAoE;EAClF,OAAO;IACLC,GAAA,EAAK,EAAC;IACNC,QAAA,EAAU,CAAC;EACb;AACF;AACO,SAASC,0BAAkDC,YAAA,EAAoE;EAGpI,SAAStK,gBAAgBuK,eAAA,GAAuB,CAAC,GAAGH,QAAA,EAA8C;IAChG,MAAM9Q,KAAA,GAAQzJ,MAAA,CAAOC,MAAA,CAAOoa,qBAAA,CAAsB,GAAGK,eAAe;IACpE,OAAOH,QAAA,GAAWE,YAAA,CAAaE,MAAA,CAAOlR,KAAA,EAAO8Q,QAAQ,IAAI9Q,KAAA;EAC3D;EACA,OAAO;IACL0G;EACF;AACF;;;ACTO,SAASyK,uBAAA,EAAiD;EAG/D,SAAS9B,aAAgBC,WAAA,EAAgDpV,OAAA,GAA+B,CAAC,GAAgC;IACvI,MAAM;MACJrE,cAAA,EAAAQ,eAAA,GAAiBU;IACnB,IAAImD,OAAA;IACJ,MAAMkX,SAAA,GAAapR,KAAA,IAA8BA,KAAA,CAAM6Q,GAAA;IACvD,MAAMQ,cAAA,GAAkBrR,KAAA,IAA8BA,KAAA,CAAM8Q,QAAA;IAC5D,MAAMQ,SAAA,GAAYjb,eAAA,CAAe+a,SAAA,EAAWC,cAAA,EAAgB,CAACR,GAAA,EAAKC,QAAA,KAAkBD,GAAA,CAAIzU,GAAA,CAAI4M,EAAA,IAAM8H,QAAA,CAAS9H,EAAE,CAAE,CAAC;IAChH,MAAMuI,QAAA,GAAWA,CAACnS,CAAA,EAAY4J,EAAA,KAAWA,EAAA;IACzC,MAAMwI,UAAA,GAAaA,CAACV,QAAA,EAAyB9H,EAAA,KAAW8H,QAAA,CAAS9H,EAAE;IACnE,MAAMyI,WAAA,GAAcpb,eAAA,CAAe+a,SAAA,EAAWP,GAAA,IAAOA,GAAA,CAAIlZ,MAAM;IAC/D,IAAI,CAAC2X,WAAA,EAAa;MAChB,OAAO;QACL8B,SAAA;QACAC,cAAA;QACAC,SAAA;QACAG,WAAA;QACAD,UAAA,EAAYnb,eAAA,CAAegb,cAAA,EAAgBE,QAAA,EAAUC,UAAU;MACjE;IACF;IACA,MAAME,wBAAA,GAA2Brb,eAAA,CAAeiZ,WAAA,EAAgD+B,cAAc;IAC9G,OAAO;MACLD,SAAA,EAAW/a,eAAA,CAAeiZ,WAAA,EAAa8B,SAAS;MAChDC,cAAA,EAAgBK,wBAAA;MAChBJ,SAAA,EAAWjb,eAAA,CAAeiZ,WAAA,EAAagC,SAAS;MAChDG,WAAA,EAAapb,eAAA,CAAeiZ,WAAA,EAAamC,WAAW;MACpDD,UAAA,EAAYnb,eAAA,CAAeqb,wBAAA,EAA0BH,QAAA,EAAUC,UAAU;IAC3E;EACF;EACA,OAAO;IACLnC;EACF;AACF;;;AC1CA,SAASha,OAAA,IAAWsc,gBAAA,EAAiBhc,OAAA,IAAAic,QAAA,QAAe;AAK7C,IAAMC,YAAA,GAAeD,QAAA;AACrB,SAASE,kCAA0DC,OAAA,EAAuD;EAC/H,MAAMC,QAAA,GAAWC,mBAAA,CAAoB,CAAC7S,CAAA,EAAcY,KAAA,KAAuC+R,OAAA,CAAQ/R,KAAK,CAAC;EACzG,OAAO,SAASkS,UAAiDlS,KAAA,EAAgC;IAC/F,OAAOgS,QAAA,CAAShS,KAAA,EAAY,MAAS;EACvC;AACF;AACO,SAASiS,oBAA+CF,OAAA,EAA+D;EAC5H,OAAO,SAASG,UAAiDlS,KAAA,EAAUkK,GAAA,EAA8B;IACvG,SAASiI,wBAAwBC,IAAA,EAAoD;MACnF,OAAO7Y,KAAA,CAAM6Y,IAAG;IAClB;IACA,MAAMC,UAAA,GAAclL,KAAA,IAAuC;MACzD,IAAIgL,uBAAA,CAAwBjI,GAAG,GAAG;QAChC6H,OAAA,CAAQ7H,GAAA,CAAIjR,OAAA,EAASkO,KAAK;MAC5B,OAAO;QACL4K,OAAA,CAAQ7H,GAAA,EAAK/C,KAAK;MACpB;IACF;IACA,IAAI0K,YAAA,CAA0C7R,KAAK,GAAG;MAIpDqS,UAAA,CAAWrS,KAAK;MAGhB,OAAOA,KAAA;IACT;IACA,OAAO2R,gBAAA,CAAgB3R,KAAA,EAAOqS,UAAU;EAC1C;AACF;;;AClCA,SAAS/c,OAAA,IAAAgd,QAAA,EAAS3c,OAAA,IAAA4c,QAAA,QAAe;AAE1B,SAASC,cAAsCC,MAAA,EAAWlB,QAAA,EAA6B;EAC5F,MAAM5X,GAAA,GAAM4X,QAAA,CAASkB,MAAM;EAC3B,IAAI5Z,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBY,GAAA,KAAQ,QAAW;IAC9DU,OAAA,CAAQC,IAAA,CAAK,0EAA0E,mEAAmE,+BAA+BmY,MAAA,EAAQ,kCAAkClB,QAAA,CAASnY,QAAA,CAAS,CAAC;EACxP;EACA,OAAOO,GAAA;AACT;AACO,SAAS+Y,oBAA4C5B,QAAA,EAAsD;EAChH,IAAI,CAACxV,KAAA,CAAMU,OAAA,CAAQ8U,QAAQ,GAAG;IAC5BA,QAAA,GAAWva,MAAA,CAAOwK,MAAA,CAAO+P,QAAQ;EACnC;EACA,OAAOA,QAAA;AACT;AACO,SAAS6B,WAAc/b,KAAA,EAAwB;EACpD,OAAQ2b,QAAA,CAAQ3b,KAAK,IAAI0b,QAAA,CAAQ1b,KAAK,IAAIA,KAAA;AAC5C;AACO,SAASgc,0BAAkDC,WAAA,EAA2CtB,QAAA,EAA6BvR,KAAA,EAAkE;EAC1M6S,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;EAC7C,MAAMC,gBAAA,GAAmBH,UAAA,CAAW3S,KAAA,CAAM6Q,GAAG;EAC7C,MAAMkC,WAAA,GAAc,IAAI3V,GAAA,CAAQ0V,gBAAgB;EAChD,MAAME,KAAA,GAAa,EAAC;EACpB,MAAMC,QAAA,GAAW,mBAAI7V,GAAA,CAAQ,EAAE;EAC/B,MAAM8V,OAAA,GAA2B,EAAC;EAClC,WAAWT,MAAA,IAAUI,WAAA,EAAa;IAChC,MAAM7J,EAAA,GAAKwJ,aAAA,CAAcC,MAAA,EAAQlB,QAAQ;IACzC,IAAIwB,WAAA,CAAYzW,GAAA,CAAI0M,EAAE,KAAKiK,QAAA,CAAS3W,GAAA,CAAI0M,EAAE,GAAG;MAC3CkK,OAAA,CAAQzT,IAAA,CAAK;QACXuJ,EAAA;QACAmK,OAAA,EAASV;MACX,CAAC;IACH,OAAO;MACLQ,QAAA,CAAS3V,GAAA,CAAI0L,EAAE;MACfgK,KAAA,CAAMvT,IAAA,CAAKgT,MAAM;IACnB;EACF;EACA,OAAO,CAACO,KAAA,EAAOE,OAAA,EAASJ,gBAAgB;AAC1C;;;ACnCO,SAASM,2BAAmD7B,QAAA,EAAwD;EAEzH,SAAS8B,cAAcZ,MAAA,EAAWzS,KAAA,EAAgB;IAChD,MAAMrG,GAAA,GAAM6Y,aAAA,CAAcC,MAAA,EAAQlB,QAAQ;IAC1C,IAAI5X,GAAA,IAAOqG,KAAA,CAAM8Q,QAAA,EAAU;MACzB;IACF;IACA9Q,KAAA,CAAM6Q,GAAA,CAAIpR,IAAA,CAAK9F,GAAqB;IACnCqG,KAAA,CAAM8Q,QAAA,CAA2BnX,GAAG,IAAI8Y,MAAA;EAC3C;EACA,SAASa,eAAeT,WAAA,EAA2C7S,KAAA,EAAgB;IACjF6S,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,WAAWJ,MAAA,IAAUI,WAAA,EAAa;MAChCQ,aAAA,CAAcZ,MAAA,EAAQzS,KAAK;IAC7B;EACF;EACA,SAASuT,cAAcd,MAAA,EAAWzS,KAAA,EAAgB;IAChD,MAAMrG,GAAA,GAAM6Y,aAAA,CAAcC,MAAA,EAAQlB,QAAQ;IAC1C,IAAI,EAAE5X,GAAA,IAAOqG,KAAA,CAAM8Q,QAAA,GAAW;MAC5B9Q,KAAA,CAAM6Q,GAAA,CAAIpR,IAAA,CAAK9F,GAAqB;IACtC;IACA;IACCqG,KAAA,CAAM8Q,QAAA,CAA2BnX,GAAG,IAAI8Y,MAAA;EAC3C;EACA,SAASe,eAAeX,WAAA,EAA2C7S,KAAA,EAAgB;IACjF6S,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,WAAWJ,MAAA,IAAUI,WAAA,EAAa;MAChCU,aAAA,CAAcd,MAAA,EAAQzS,KAAK;IAC7B;EACF;EACA,SAASyT,cAAcZ,WAAA,EAA2C7S,KAAA,EAAgB;IAChF6S,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C7S,KAAA,CAAM6Q,GAAA,GAAM,EAAC;IACb7Q,KAAA,CAAM8Q,QAAA,GAAW,CAAC;IAClBwC,cAAA,CAAeT,WAAA,EAAa7S,KAAK;EACnC;EACA,SAAS0T,iBAAiB/Z,GAAA,EAASqG,KAAA,EAAgB;IACjD,OAAO2T,iBAAA,CAAkB,CAACha,GAAG,GAAGqG,KAAK;EACvC;EACA,SAAS2T,kBAAkBna,IAAA,EAAqBwG,KAAA,EAAgB;IAC9D,IAAI4T,SAAA,GAAY;IAChBpa,IAAA,CAAKgK,OAAA,CAAQ7J,GAAA,IAAO;MAClB,IAAIA,GAAA,IAAOqG,KAAA,CAAM8Q,QAAA,EAAU;QACzB,OAAQ9Q,KAAA,CAAM8Q,QAAA,CAA2BnX,GAAG;QAC5Cia,SAAA,GAAY;MACd;IACF,CAAC;IACD,IAAIA,SAAA,EAAW;MACb5T,KAAA,CAAM6Q,GAAA,GAAO7Q,KAAA,CAAM6Q,GAAA,CAAahK,MAAA,CAAOmC,EAAA,IAAMA,EAAA,IAAMhJ,KAAA,CAAM8Q,QAAQ;IACnE;EACF;EACA,SAAS+C,iBAAiB7T,KAAA,EAAgB;IACxCzJ,MAAA,CAAOC,MAAA,CAAOwJ,KAAA,EAAO;MACnB6Q,GAAA,EAAK,EAAC;MACNC,QAAA,EAAU,CAAC;IACb,CAAC;EACH;EACA,SAASgD,WAAWta,IAAA,EAEjBua,MAAA,EAAuB/T,KAAA,EAAmB;IAC3C,MAAMgU,SAAA,GAA2BhU,KAAA,CAAM8Q,QAAA,CAA2BiD,MAAA,CAAO/K,EAAE;IAC3E,IAAIgL,SAAA,KAAa,QAAW;MAC1B,OAAO;IACT;IACA,MAAMd,OAAA,GAAa3c,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAGwd,SAAA,EAAUD,MAAA,CAAOZ,OAAO;IAC7D,MAAMc,MAAA,GAASzB,aAAA,CAAcU,OAAA,EAAS3B,QAAQ;IAC9C,MAAM2C,SAAA,GAAYD,MAAA,KAAWF,MAAA,CAAO/K,EAAA;IACpC,IAAIkL,SAAA,EAAW;MACb1a,IAAA,CAAKua,MAAA,CAAO/K,EAAE,IAAIiL,MAAA;MAClB,OAAQjU,KAAA,CAAM8Q,QAAA,CAA2BiD,MAAA,CAAO/K,EAAE;IACpD;IACA;IACChJ,KAAA,CAAM8Q,QAAA,CAA2BmD,MAAM,IAAIf,OAAA;IAC5C,OAAOgB,SAAA;EACT;EACA,SAASC,iBAAiBJ,MAAA,EAAuB/T,KAAA,EAAgB;IAC/D,OAAOoU,iBAAA,CAAkB,CAACL,MAAM,GAAG/T,KAAK;EAC1C;EACA,SAASoU,kBAAkBC,OAAA,EAAuCrU,KAAA,EAAgB;IAChF,MAAMsU,OAAA,GAEF,CAAC;IACL,MAAMC,gBAAA,GAEF,CAAC;IACLF,OAAA,CAAQ7Q,OAAA,CAAQuQ,MAAA,IAAU;MAExB,IAAIA,MAAA,CAAO/K,EAAA,IAAMhJ,KAAA,CAAM8Q,QAAA,EAAU;QAE/ByD,gBAAA,CAAiBR,MAAA,CAAO/K,EAAE,IAAI;UAC5BA,EAAA,EAAI+K,MAAA,CAAO/K,EAAA;UAAA;UAAA;UAGXmK,OAAA,EAAS;YACP,GAAGoB,gBAAA,CAAiBR,MAAA,CAAO/K,EAAE,GAAGmK,OAAA;YAChC,GAAGY,MAAA,CAAOZ;UACZ;QACF;MACF;IACF,CAAC;IACDkB,OAAA,GAAU9d,MAAA,CAAOwK,MAAA,CAAOwT,gBAAgB;IACxC,MAAMC,iBAAA,GAAoBH,OAAA,CAAQ1c,MAAA,GAAS;IAC3C,IAAI6c,iBAAA,EAAmB;MACrB,MAAMC,YAAA,GAAeJ,OAAA,CAAQxN,MAAA,CAAOkN,MAAA,IAAUD,UAAA,CAAWQ,OAAA,EAASP,MAAA,EAAQ/T,KAAK,CAAC,EAAErI,MAAA,GAAS;MAC3F,IAAI8c,YAAA,EAAc;QAChBzU,KAAA,CAAM6Q,GAAA,GAAMta,MAAA,CAAOwK,MAAA,CAAOf,KAAA,CAAM8Q,QAAQ,EAAE1U,GAAA,CAAIsY,CAAA,IAAKlC,aAAA,CAAckC,CAAA,EAAQnD,QAAQ,CAAC;MACpF;IACF;EACF;EACA,SAASoD,iBAAiBlC,MAAA,EAAWzS,KAAA,EAAgB;IACnD,OAAO4U,iBAAA,CAAkB,CAACnC,MAAM,GAAGzS,KAAK;EAC1C;EACA,SAAS4U,kBAAkB/B,WAAA,EAA2C7S,KAAA,EAAgB;IACpF,MAAM,CAACgT,KAAA,EAAOE,OAAO,IAAIN,yBAAA,CAAiCC,WAAA,EAAatB,QAAA,EAAUvR,KAAK;IACtFsT,cAAA,CAAeN,KAAA,EAAOhT,KAAK;IAC3BoU,iBAAA,CAAkBlB,OAAA,EAASlT,KAAK;EAClC;EACA,OAAO;IACL6U,SAAA,EAAW/C,iCAAA,CAAkC+B,gBAAgB;IAC7DiB,MAAA,EAAQ7C,mBAAA,CAAoBoB,aAAa;IACzC0B,OAAA,EAAS9C,mBAAA,CAAoBqB,cAAc;IAC3C0B,MAAA,EAAQ/C,mBAAA,CAAoBsB,aAAa;IACzC0B,OAAA,EAAShD,mBAAA,CAAoBuB,cAAc;IAC3CtC,MAAA,EAAQe,mBAAA,CAAoBwB,aAAa;IACzCyB,SAAA,EAAWjD,mBAAA,CAAoBkC,gBAAgB;IAC/CgB,UAAA,EAAYlD,mBAAA,CAAoBmC,iBAAiB;IACjDgB,SAAA,EAAWnD,mBAAA,CAAoB0C,gBAAgB;IAC/CU,UAAA,EAAYpD,mBAAA,CAAoB2C,iBAAiB;IACjDU,SAAA,EAAWrD,mBAAA,CAAoByB,gBAAgB;IAC/C6B,UAAA,EAAYtD,mBAAA,CAAoB0B,iBAAiB;EACnD;AACF;;;ACjIO,SAAS6B,gBAAmBC,WAAA,EAAkB3Q,IAAA,EAAS4Q,kBAAA,EAAyC;EACrG,IAAIC,QAAA,GAAW;EACf,IAAIC,SAAA,GAAYH,WAAA,CAAY9d,MAAA;EAC5B,OAAOge,QAAA,GAAWC,SAAA,EAAW;IAC3B,IAAIC,WAAA,GAAcF,QAAA,GAAWC,SAAA,KAAc;IAC3C,MAAME,WAAA,GAAcL,WAAA,CAAYI,WAAW;IAC3C,MAAME,GAAA,GAAML,kBAAA,CAAmB5Q,IAAA,EAAMgR,WAAW;IAChD,IAAIC,GAAA,IAAO,GAAG;MACZJ,QAAA,GAAWE,WAAA,GAAc;IAC3B,OAAO;MACLD,SAAA,GAAYC,WAAA;IACd;EACF;EACA,OAAOF,QAAA;AACT;AACO,SAASK,OAAUP,WAAA,EAAkB3Q,IAAA,EAAS4Q,kBAAA,EAAsC;EACzF,MAAMO,aAAA,GAAgBT,eAAA,CAAgBC,WAAA,EAAa3Q,IAAA,EAAM4Q,kBAAkB;EAC3ED,WAAA,CAAYjW,MAAA,CAAOyW,aAAA,EAAe,GAAGnR,IAAI;EACzC,OAAO2Q,WAAA;AACT;AACO,SAASS,yBAAiD3E,QAAA,EAA6B4E,QAAA,EAAkD;EAE9I,MAAM;IACJb,SAAA;IACAC,UAAA;IACAV;EACF,IAAIzB,0BAAA,CAA2B7B,QAAQ;EACvC,SAAS8B,cAAcZ,MAAA,EAAWzS,KAAA,EAAgB;IAChD,OAAOsT,cAAA,CAAe,CAACb,MAAM,GAAGzS,KAAK;EACvC;EACA,SAASsT,eAAeT,WAAA,EAA2C7S,KAAA,EAAU+S,WAAA,EAA0B;IACrGF,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,MAAMuD,YAAA,GAAe,IAAIhZ,GAAA,CAAQ2V,WAAA,IAAeJ,UAAA,CAAW3S,KAAA,CAAM6Q,GAAG,CAAC;IACrE,MAAMwF,MAAA,GAASxD,WAAA,CAAYhM,MAAA,CAAOyP,KAAA,IAAS,CAACF,YAAA,CAAa9Z,GAAA,CAAIkW,aAAA,CAAc8D,KAAA,EAAO/E,QAAQ,CAAC,CAAC;IAC5F,IAAI8E,MAAA,CAAO1e,MAAA,KAAW,GAAG;MACvB4e,aAAA,CAAcvW,KAAA,EAAOqW,MAAM;IAC7B;EACF;EACA,SAAS9C,cAAcd,MAAA,EAAWzS,KAAA,EAAgB;IAChD,OAAOwT,cAAA,CAAe,CAACf,MAAM,GAAGzS,KAAK;EACvC;EACA,SAASwT,eAAeX,WAAA,EAA2C7S,KAAA,EAAgB;IACjF6S,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C,IAAIA,WAAA,CAAYlb,MAAA,KAAW,GAAG;MAC5B,WAAWmN,IAAA,IAAQ+N,WAAA,EAAa;QAC9B,OAAQ7S,KAAA,CAAM8Q,QAAA,CAA2BS,QAAA,CAASzM,IAAI,CAAC;MACzD;MACAyR,aAAA,CAAcvW,KAAA,EAAO6S,WAAW;IAClC;EACF;EACA,SAASY,cAAcZ,WAAA,EAA2C7S,KAAA,EAAgB;IAChF6S,WAAA,GAAcH,mBAAA,CAAoBG,WAAW;IAC7C7S,KAAA,CAAM8Q,QAAA,GAAW,CAAC;IAClB9Q,KAAA,CAAM6Q,GAAA,GAAM,EAAC;IACbyC,cAAA,CAAeT,WAAA,EAAa7S,KAAA,EAAO,EAAE;EACvC;EACA,SAASmU,iBAAiBJ,MAAA,EAAuB/T,KAAA,EAAgB;IAC/D,OAAOoU,iBAAA,CAAkB,CAACL,MAAM,GAAG/T,KAAK;EAC1C;EACA,SAASoU,kBAAkBC,OAAA,EAAuCrU,KAAA,EAAgB;IAChF,IAAIwW,cAAA,GAAiB;IACrB,IAAIC,WAAA,GAAc;IAClB,SAAS1C,MAAA,IAAUM,OAAA,EAAS;MAC1B,MAAM5B,MAAA,GAAyBzS,KAAA,CAAM8Q,QAAA,CAA2BiD,MAAA,CAAO/K,EAAE;MACzE,IAAI,CAACyJ,MAAA,EAAQ;QACX;MACF;MACA+D,cAAA,GAAiB;MACjBjgB,MAAA,CAAOC,MAAA,CAAOic,MAAA,EAAQsB,MAAA,CAAOZ,OAAO;MACpC,MAAMuD,KAAA,GAAQnF,QAAA,CAASkB,MAAM;MAC7B,IAAIsB,MAAA,CAAO/K,EAAA,KAAO0N,KAAA,EAAO;QAGvBD,WAAA,GAAc;QACd,OAAQzW,KAAA,CAAM8Q,QAAA,CAA2BiD,MAAA,CAAO/K,EAAE;QAClD,MAAM2N,QAAA,GAAY3W,KAAA,CAAM6Q,GAAA,CAAajX,OAAA,CAAQma,MAAA,CAAO/K,EAAE;QACtDhJ,KAAA,CAAM6Q,GAAA,CAAI8F,QAAQ,IAAID,KAAA;QACrB1W,KAAA,CAAM8Q,QAAA,CAA2B4F,KAAK,IAAIjE,MAAA;MAC7C;IACF;IACA,IAAI+D,cAAA,EAAgB;MAClBD,aAAA,CAAcvW,KAAA,EAAO,EAAC,EAAGwW,cAAA,EAAgBC,WAAW;IACtD;EACF;EACA,SAAS9B,iBAAiBlC,MAAA,EAAWzS,KAAA,EAAgB;IACnD,OAAO4U,iBAAA,CAAkB,CAACnC,MAAM,GAAGzS,KAAK;EAC1C;EACA,SAAS4U,kBAAkB/B,WAAA,EAA2C7S,KAAA,EAAgB;IACpF,MAAM,CAACgT,KAAA,EAAOE,OAAA,EAASJ,gBAAgB,IAAIF,yBAAA,CAAiCC,WAAA,EAAatB,QAAA,EAAUvR,KAAK;IACxG,IAAIgT,KAAA,CAAMrb,MAAA,EAAQ;MAChB2b,cAAA,CAAeN,KAAA,EAAOhT,KAAA,EAAO8S,gBAAgB;IAC/C;IACA,IAAII,OAAA,CAAQvb,MAAA,EAAQ;MAClByc,iBAAA,CAAkBlB,OAAA,EAASlT,KAAK;IAClC;EACF;EACA,SAAS4W,eAAe7O,CAAA,EAAuB8O,CAAA,EAAuB;IACpE,IAAI9O,CAAA,CAAEpQ,MAAA,KAAWkf,CAAA,CAAElf,MAAA,EAAQ;MACzB,OAAO;IACT;IACA,SAASsR,CAAA,GAAI,GAAGA,CAAA,GAAIlB,CAAA,CAAEpQ,MAAA,EAAQsR,CAAA,IAAK;MACjC,IAAIlB,CAAA,CAAEkB,CAAC,MAAM4N,CAAA,CAAE5N,CAAC,GAAG;QACjB;MACF;MACA,OAAO;IACT;IACA,OAAO;EACT;EAEA,MAAMsN,aAAA,GAA+BA,CAACvW,KAAA,EAAO8W,UAAA,EAAYN,cAAA,EAAgBC,WAAA,KAAgB;IACvF,MAAMM,eAAA,GAAkBpE,UAAA,CAAW3S,KAAA,CAAM8Q,QAAQ;IACjD,MAAMkG,UAAA,GAAarE,UAAA,CAAW3S,KAAA,CAAM6Q,GAAG;IACvC,MAAMoG,aAAA,GAAgBjX,KAAA,CAAM8Q,QAAA;IAC5B,IAAID,GAAA,GAAoBmG,UAAA;IACxB,IAAIP,WAAA,EAAa;MACf5F,GAAA,GAAM,IAAIzT,GAAA,CAAI4Z,UAAU;IAC1B;IACA,IAAIE,cAAA,GAAsB,EAAC;IAC3B,WAAWlO,EAAA,IAAM6H,GAAA,EAAK;MACpB,MAAM4B,MAAA,GAASsE,eAAA,CAAgB/N,EAAE;MACjC,IAAIyJ,MAAA,EAAQ;QACVyE,cAAA,CAAezX,IAAA,CAAKgT,MAAM;MAC5B;IACF;IACA,MAAM0E,kBAAA,GAAqBD,cAAA,CAAevf,MAAA,KAAW;IAGrD,WAAWmN,IAAA,IAAQgS,UAAA,EAAY;MAC7BG,aAAA,CAAc1F,QAAA,CAASzM,IAAI,CAAC,IAAIA,IAAA;MAChC,IAAI,CAACqS,kBAAA,EAAoB;QAEvBnB,MAAA,CAAOkB,cAAA,EAAgBpS,IAAA,EAAMqR,QAAQ;MACvC;IACF;IACA,IAAIgB,kBAAA,EAAoB;MAEtBD,cAAA,GAAiBJ,UAAA,CAAWzX,KAAA,CAAM,EAAE+X,IAAA,CAAKjB,QAAQ;IACnD,WAAWK,cAAA,EAAgB;MAEzBU,cAAA,CAAeE,IAAA,CAAKjB,QAAQ;IAC9B;IACA,MAAMkB,YAAA,GAAeH,cAAA,CAAe9a,GAAA,CAAImV,QAAQ;IAChD,IAAI,CAACqF,cAAA,CAAeI,UAAA,EAAYK,YAAY,GAAG;MAC7CrX,KAAA,CAAM6Q,GAAA,GAAMwG,YAAA;IACd;EACF;EACA,OAAO;IACL/B,SAAA;IACAC,UAAA;IACAV,SAAA;IACAC,MAAA,EAAQ7C,mBAAA,CAAoBoB,aAAa;IACzC6B,SAAA,EAAWjD,mBAAA,CAAoBkC,gBAAgB;IAC/CiB,SAAA,EAAWnD,mBAAA,CAAoB0C,gBAAgB;IAC/CK,MAAA,EAAQ/C,mBAAA,CAAoBsB,aAAa;IACzC0B,OAAA,EAAShD,mBAAA,CAAoBuB,cAAc;IAC3CtC,MAAA,EAAQe,mBAAA,CAAoBwB,aAAa;IACzCsB,OAAA,EAAS9C,mBAAA,CAAoBqB,cAAc;IAC3C6B,UAAA,EAAYlD,mBAAA,CAAoBmC,iBAAiB;IACjDiB,UAAA,EAAYpD,mBAAA,CAAoB2C,iBAAiB;EACnD;AACF;;;ACrJO,SAAS0C,oBAAuBpd,OAAA,GAA6C,CAAC,GAA+B;EAClH,MAAM;IACJqX,QAAA;IACAgG;EACF,IAAiD;IAC/CA,YAAA,EAAc;IACdhG,QAAA,EAAWiG,QAAA,IAAkBA,QAAA,CAASxO,EAAA;IACtC,GAAG9O;EACL;EACA,MAAM8W,YAAA,GAAeuG,YAAA,GAAerB,wBAAA,CAAyB3E,QAAA,EAAUgG,YAAY,IAAInE,0BAAA,CAA2B7B,QAAQ;EAC1H,MAAMkG,YAAA,GAAe1G,yBAAA,CAA0BC,YAAY;EAC3D,MAAM0G,gBAAA,GAAmBvG,sBAAA,CAAoC;EAC7D,OAAO;IACLI,QAAA;IACAgG,YAAA;IACA,GAAGE,YAAA;IACH,GAAGC,gBAAA;IACH,GAAG1G;EACL;AACF;;;AClCA,SAAS7Y,QAAA,IAAAwf,SAAA,QAAgB;;;ACDzB,IAAMC,IAAA,GAAO;AACb,IAAMC,QAAA,GAAW;AACjB,IAAMC,SAAA,GAAY;AAClB,IAAMC,SAAA,GAAY;AAGX,IAAMC,aAAA,GAAgB,QAAQD,SAAS;AACvC,IAAME,aAAA,GAAgB,QAAQH,SAAS;AACvC,IAAMI,iBAAA,GAAoB,GAAGL,QAAQ,IAAIE,SAAS;AAClD,IAAMI,iBAAA,GAAoB,GAAGN,QAAQ,IAAIC,SAAS;AAClD,IAAMM,cAAA,GAAN,MAAgD;EAGrD7c,YAAmB8c,IAAA,EAA0B;IAA1B,KAAAA,IAAA,GAAAA,IAAA;IACjB,KAAK1O,OAAA,GAAU,GAAGiO,IAAI,IAAIG,SAAS,aAAaM,IAAI;EACtD;EAJAhO,IAAA,GAAO;EACPV,OAAA;AAIF;;;ACfO,IAAM2O,cAAA,GAAuGA,CAACC,IAAA,EAAeC,QAAA,KAAqB;EACvJ,IAAI,OAAOD,IAAA,KAAS,YAAY;IAC9B,MAAM,IAAIE,SAAA,CAAU5f,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,GAAGwf,QAAQ,oBAAoB;EAC3H;AACF;AACO,IAAME,KAAA,GAAO/H,CAAA,KAAM,CAAC;AACpB,IAAMgI,cAAA,GAAiBA,CAAKzN,OAAA,EAAqB0N,OAAA,GAAUF,KAAA,KAAqB;EACrFxN,OAAA,CAAQ2N,KAAA,CAAMD,OAAO;EACrB,OAAO1N,OAAA;AACT;AACO,IAAM4N,sBAAA,GAAyBA,CAACC,WAAA,EAA0BC,QAAA,KAAmC;EAClGD,WAAA,CAAY/N,gBAAA,CAAiB,SAASgO,QAAA,EAAU;IAC9C/N,IAAA,EAAM;EACR,CAAC;EACD,OAAO,MAAM8N,WAAA,CAAY/M,mBAAA,CAAoB,SAASgN,QAAQ;AAChE;AAYO,IAAMC,yBAAA,GAA4BA,CAAKvO,eAAA,EAAkCK,MAAA,KAAoB;EAElG,MAAMR,MAAA,GAASG,eAAA,CAAgBH,MAAA;EAC/B,IAAIA,MAAA,CAAOH,OAAA,EAAS;IAClB;EACF;EAMA,IAAI,EAAE,YAAYG,MAAA,GAAS;IACzBhU,MAAA,CAAO2iB,cAAA,CAAe3O,MAAA,EAAQ,UAAU;MACtC4O,UAAA,EAAY;MACZviB,KAAA,EAAOmU,MAAA;MACPqO,YAAA,EAAc;MACdC,QAAA,EAAU;IACZ,CAAC;EACH;EACA;EACC3O,eAAA,CAAgBI,KAAA,CAAkCC,MAAM;AAC3D;;;ACxCO,IAAMuO,cAAA,GAAkB/O,MAAA,IAA8B;EAC3D,IAAIA,MAAA,CAAOH,OAAA,EAAS;IAClB,MAAM;MACJW;IACF,IAAIR,MAAA;IACJ,MAAM,IAAI6N,cAAA,CAAerN,MAAM;EACjC;AACF;AAOO,SAASwO,eAAkBhP,MAAA,EAAuCW,OAAA,EAAiC;EACxG,IAAIsO,OAAA,GAAUd,KAAA;EACd,OAAO,IAAInN,OAAA,CAAW,CAACI,OAAA,EAASH,MAAA,KAAW;IACzC,MAAMiO,eAAA,GAAkBA,CAAA,KAAMjO,MAAA,CAAO,IAAI4M,cAAA,CAAe7N,MAAA,CAAOQ,MAAM,CAAC;IACtE,IAAIR,MAAA,CAAOH,OAAA,EAAS;MAClBqP,eAAA,CAAgB;MAChB;IACF;IACAD,OAAA,GAAUV,sBAAA,CAAuBvO,MAAA,EAAQkP,eAAe;IACxDvO,OAAA,CAAQwO,OAAA,CAAQ,MAAMF,OAAA,CAAQ,CAAC,EAAE1N,IAAA,CAAKH,OAAA,EAASH,MAAM;EACvD,CAAC,EAAEkO,OAAA,CAAQ,MAAM;IAEfF,OAAA,GAAUd,KAAA;EACZ,CAAC;AACH;AASO,IAAMiB,OAAA,GAAU,MAAAA,CAAWC,KAAA,EAAwBC,OAAA,KAAiD;EACzG,IAAI;IACF,MAAMtO,OAAA,CAAQI,OAAA,CAAQ;IACtB,MAAM/U,KAAA,GAAQ,MAAMgjB,KAAA,CAAK;IACzB,OAAO;MACLE,MAAA,EAAQ;MACRljB;IACF;EACF,SAASuC,KAAA,EAAY;IACnB,OAAO;MACL2gB,MAAA,EAAQ3gB,KAAA,YAAiBif,cAAA,GAAiB,cAAc;MACxDjf;IACF;EACF,UAAE;IACA0gB,OAAA,GAAU;EACZ;AACF;AASO,IAAME,WAAA,GAAmBxP,MAAA,IAAwB;EACtD,OAAQW,OAAA,IAAoC;IAC1C,OAAOyN,cAAA,CAAeY,cAAA,CAAehP,MAAA,EAAQW,OAAO,EAAEY,IAAA,CAAKkO,MAAA,IAAU;MACnEV,cAAA,CAAe/O,MAAM;MACrB,OAAOyP,MAAA;IACT,CAAC,CAAC;EACJ;AACF;AAQO,IAAMC,WAAA,GAAe1P,MAAA,IAAwB;EAClD,MAAM2P,KAAA,GAAQH,WAAA,CAAkBxP,MAAM;EACtC,OAAQ4P,SAAA,IAAqC;IAC3C,OAAOD,KAAA,CAAM,IAAI3O,OAAA,CAAcI,OAAA,IAAW/I,UAAA,CAAW+I,OAAA,EAASwO,SAAS,CAAC,CAAC;EAC3E;AACF;;;AH9EA,IAAM;EACJ3jB;AACF,IAAID,MAAA;AAIJ,IAAM6jB,kBAAA,GAAqB,CAAC;AAC5B,IAAMC,GAAA,GAAM;AACZ,IAAMC,UAAA,GAAaA,CAACC,iBAAA,EAAmDC,sBAAA,KAA2C;EAChH,MAAMC,eAAA,GAAmBC,UAAA,IAAgC5B,sBAAA,CAAuByB,iBAAA,EAAmB,MAAMtB,yBAAA,CAA0ByB,UAAA,EAAYH,iBAAA,CAAkBxP,MAAM,CAAC;EACxK,OAAO,CAAK4P,YAAA,EAAqCC,IAAA,KAAsC;IACrFtC,cAAA,CAAeqC,YAAA,EAAc,cAAc;IAC3C,MAAME,oBAAA,GAAuB,IAAIlQ,eAAA,CAAgB;IACjD8P,eAAA,CAAgBI,oBAAoB;IACpC,MAAMpc,MAAA,GAASkb,OAAA,CAAW,YAAwB;MAChDL,cAAA,CAAeiB,iBAAiB;MAChCjB,cAAA,CAAeuB,oBAAA,CAAqBtQ,MAAM;MAC1C,MAAMuQ,OAAA,GAAU,MAAMH,YAAA,CAAa;QACjCT,KAAA,EAAOH,WAAA,CAAYc,oBAAA,CAAqBtQ,MAAM;QAC9CwQ,KAAA,EAAOd,WAAA,CAAYY,oBAAA,CAAqBtQ,MAAM;QAC9CA,MAAA,EAAQsQ,oBAAA,CAAqBtQ;MAC/B,CAAC;MACD+O,cAAA,CAAeuB,oBAAA,CAAqBtQ,MAAM;MAC1C,OAAOuQ,OAAA;IACT,GAAG,MAAM7B,yBAAA,CAA0B4B,oBAAA,EAAsB5C,aAAa,CAAC;IACvE,IAAI2C,IAAA,EAAMI,QAAA,EAAU;MAClBR,sBAAA,CAAuB/a,IAAA,CAAKhB,MAAA,CAAOoa,KAAA,CAAMH,KAAI,CAAC;IAChD;IACA,OAAO;MACLja,MAAA,EAAQsb,WAAA,CAA2BQ,iBAAiB,EAAE9b,MAAM;MAC5Dwc,OAAA,EAAS;QACPhC,yBAAA,CAA0B4B,oBAAA,EAAsB7C,aAAa;MAC/D;IACF;EACF;AACF;AACA,IAAMkD,iBAAA,GAAoBA,CAAKC,cAAA,EAAwE5Q,MAAA,KAAwC;EAQ7I,MAAM6Q,IAAA,GAAO,MAAAA,CAA2CC,SAAA,EAAc3Y,OAAA,KAAgC;IACpG4W,cAAA,CAAe/O,MAAM;IAGrB,IAAI1G,WAAA,GAAmCA,CAAA,KAAM,CAAC;IAC9C,MAAMyX,YAAA,GAAe,IAAI/P,OAAA,CAAwB,CAACI,OAAA,EAASH,MAAA,KAAW;MAEpE,IAAI+P,aAAA,GAAgBJ,cAAA,CAAe;QACjCE,SAAA;QACAG,MAAA,EAAQA,CAACniB,MAAA,EAAQoiB,WAAA,KAAsB;UAErCA,WAAA,CAAY5X,WAAA,CAAY;UAExB8H,OAAA,CAAQ,CAACtS,MAAA,EAAQoiB,WAAA,CAAY1b,QAAA,CAAS,GAAG0b,WAAA,CAAYC,gBAAA,CAAiB,CAAC,CAAC;QAC1E;MACF,CAAC;MACD7X,WAAA,GAAcA,CAAA,KAAM;QAClB0X,aAAA,CAAc;QACd/P,MAAA,CAAO;MACT;IACF,CAAC;IACD,MAAMmQ,QAAA,GAAwD,CAACL,YAAY;IAC3E,IAAI5Y,OAAA,IAAW,MAAM;MACnBiZ,QAAA,CAASlc,IAAA,CAAK,IAAI8L,OAAA,CAAcI,OAAA,IAAW/I,UAAA,CAAW+I,OAAA,EAASjJ,OAAA,EAAS,IAAI,CAAC,CAAC;IAChF;IACA,IAAI;MACF,MAAMsX,MAAA,GAAS,MAAMT,cAAA,CAAehP,MAAA,EAAQgB,OAAA,CAAQG,IAAA,CAAKiQ,QAAQ,CAAC;MAClErC,cAAA,CAAe/O,MAAM;MACrB,OAAOyP,MAAA;IACT,UAAE;MAEAnW,WAAA,CAAY;IACd;EACF;EACA,OAAQ,CAACwX,SAAA,EAAoC3Y,OAAA,KAAgCiW,cAAA,CAAeyC,IAAA,CAAKC,SAAA,EAAW3Y,OAAO,CAAC;AACtH;AACA,IAAMkZ,yBAAA,GAA6B1hB,OAAA,IAAwC;EACzE,IAAI;IACF1B,IAAA;IACAE,aAAA;IACAwN,OAAA;IACAmV,SAAA;IACAG;EACF,IAAIthB,OAAA;EACJ,IAAI1B,IAAA,EAAM;IACR6iB,SAAA,GAAY9iB,YAAA,CAAaC,IAAI,EAAEF,KAAA;EACjC,WAAWI,aAAA,EAAe;IACxBF,IAAA,GAAOE,aAAA,CAAeF,IAAA;IACtB6iB,SAAA,GAAY3iB,aAAA,CAAcJ,KAAA;EAC5B,WAAW4N,OAAA,EAAS;IAClBmV,SAAA,GAAYnV,OAAA;EACd,WAAWmV,SAAA,EAAW,CAEtB,OAAO;IACL,MAAM,IAAIziB,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,yFAAyF;EACjL;EACAsf,cAAA,CAAekD,MAAA,EAAQ,kBAAkB;EACzC,OAAO;IACLH,SAAA;IACA7iB,IAAA;IACAgjB;EACF;AACF;AAGO,IAAMK,mBAAA,GAAwE,eAAArlB,MAAA,CAAQ0D,OAAA,IAAwC;EACnI,MAAM;IACJ1B,IAAA;IACA6iB,SAAA;IACAG;EACF,IAAII,yBAAA,CAA0B1hB,OAAO;EACrC,MAAM4hB,KAAA,GAAgC;IACpC9S,EAAA,EAAIF,MAAA,CAAO;IACX0S,MAAA;IACAhjB,IAAA;IACA6iB,SAAA;IACAlT,OAAA,EAAS,mBAAI/K,GAAA,CAAqB;IAClCyG,WAAA,EAAaA,CAAA,KAAM;MACjB,MAAM,IAAIjL,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,6BAA6B;IACtH;EACF;EACA,OAAO8iB,KAAA;AACT,GAAG;EACDhlB,SAAA,EAAWA,CAAA,KAAM+kB;AACnB,CAAC;AACD,IAAME,iBAAA,GAAoBA,CAACC,WAAA,EAAyC9hB,OAAA,KAAwC;EAC1G,MAAM;IACJ1B,IAAA;IACAgjB,MAAA;IACAH;EACF,IAAIO,yBAAA,CAA0B1hB,OAAO;EACrC,OAAOoB,KAAA,CAAM2gB,IAAA,CAAKD,WAAA,CAAYjb,MAAA,CAAO,CAAC,EAAEmb,IAAA,CAAKJ,KAAA,IAAS;IACpD,MAAMK,oBAAA,GAAuB,OAAO3jB,IAAA,KAAS,WAAWsjB,KAAA,CAAMtjB,IAAA,KAASA,IAAA,GAAOsjB,KAAA,CAAMT,SAAA,KAAcA,SAAA;IAClG,OAAOc,oBAAA,IAAwBL,KAAA,CAAMN,MAAA,KAAWA,MAAA;EAClD,CAAC;AACH;AACA,IAAMY,qBAAA,GAAyBN,KAAA,IAA2D;EACxFA,KAAA,CAAM3T,OAAA,CAAQ3E,OAAA,CAAQkX,UAAA,IAAc;IAClCzB,yBAAA,CAA0ByB,UAAA,EAAYxC,iBAAiB;EACzD,CAAC;AACH;AACA,IAAMmE,6BAAA,GAAiCL,WAAA,IAA4C;EACjF,OAAO,MAAM;IACXA,WAAA,CAAYxY,OAAA,CAAQ4Y,qBAAqB;IACzCJ,WAAA,CAAYM,KAAA,CAAM;EACpB;AACF;AASA,IAAMC,iBAAA,GAAoBA,CAACC,YAAA,EAAoCC,aAAA,EAAwBC,SAAA,KAAuC;EAC5H,IAAI;IACFF,YAAA,CAAaC,aAAA,EAAeC,SAAS;EACvC,SAASC,iBAAA,EAAmB;IAG1B/Z,UAAA,CAAW,MAAM;MACf,MAAM+Z,iBAAA;IACR,GAAG,CAAC;EACN;AACF;AAKO,IAAMC,WAAA,GAA6B,eAAApmB,MAAA,CAAsB,eAAA+B,YAAA,CAAa,GAAG8hB,GAAG,MAAM,GAAG;EAC1FvjB,SAAA,EAAWA,CAAA,KAAM8lB;AACnB,CAAC;AAKM,IAAMC,iBAAA,GAAmC,eAAAtkB,YAAA,CAAa,GAAG8hB,GAAG,YAAY;AAKxE,IAAMyC,cAAA,GAAgC,eAAAtmB,MAAA,CAAsB,eAAA+B,YAAA,CAAa,GAAG8hB,GAAG,SAAS,GAAG;EAChGvjB,SAAA,EAAWA,CAAA,KAAMgmB;AACnB,CAAC;AACD,IAAMC,mBAAA,GAA4CA,CAAA,GAAI3mB,IAAA,KAAoB;EACxEiE,OAAA,CAAQlB,KAAA,CAAM,GAAGkhB,GAAG,UAAU,GAAGjkB,IAAI;AACvC;AAKO,IAAM4mB,wBAAA,GAA2BA,CAAyIC,iBAAA,GAAoE,CAAC,MAAM;EAC1P,MAAMjB,WAAA,GAAc,mBAAIpN,GAAA,CAA2B;EACnD,MAAM;IACJpE,KAAA;IACAoO,OAAA,GAAUmE;EACZ,IAAIE,iBAAA;EACJ3E,cAAA,CAAeM,OAAA,EAAS,SAAS;EACjC,MAAMsE,WAAA,GAAepB,KAAA,IAAyB;IAC5CA,KAAA,CAAMjY,WAAA,GAAc,MAAMmY,WAAA,CAAYlY,MAAA,CAAOgY,KAAA,CAAM9S,EAAE;IACrDgT,WAAA,CAAYxf,GAAA,CAAIsf,KAAA,CAAM9S,EAAA,EAAI8S,KAAK;IAC/B,OAAQqB,aAAA,IAA+C;MACrDrB,KAAA,CAAMjY,WAAA,CAAY;MAClB,IAAIsZ,aAAA,EAAeC,YAAA,EAAc;QAC/BhB,qBAAA,CAAsBN,KAAK;MAC7B;IACF;EACF;EACA,MAAMX,cAAA,GAAmBjhB,OAAA,IAAwC;IAC/D,MAAM4hB,KAAA,GAAQC,iBAAA,CAAkBC,WAAA,EAAa9hB,OAAO,KAAK2hB,mBAAA,CAAoB3hB,OAAc;IAC3F,OAAOgjB,WAAA,CAAYpB,KAAK;EAC1B;EACAtlB,MAAA,CAAO2kB,cAAA,EAAgB;IACrBrkB,SAAA,EAAWA,CAAA,KAAMqkB;EACnB,CAAC;EACD,MAAMI,aAAA,GAAiBrhB,OAAA,IAA8E;IACnG,MAAM4hB,KAAA,GAAQC,iBAAA,CAAkBC,WAAA,EAAa9hB,OAAO;IACpD,IAAI4hB,KAAA,EAAO;MACTA,KAAA,CAAMjY,WAAA,CAAY;MAClB,IAAI3J,OAAA,CAAQkjB,YAAA,EAAc;QACxBhB,qBAAA,CAAsBN,KAAK;MAC7B;IACF;IACA,OAAO,CAAC,CAACA,KAAA;EACX;EACAtlB,MAAA,CAAO+kB,aAAA,EAAe;IACpBzkB,SAAA,EAAWA,CAAA,KAAMykB;EACnB,CAAC;EACD,MAAM8B,cAAA,GAAiB,MAAAA,CAAOvB,KAAA,EAAwDziB,MAAA,EAAiBikB,GAAA,EAAoB5B,gBAAA,KAAsC;IAC/J,MAAM6B,sBAAA,GAAyB,IAAI5S,eAAA,CAAgB;IACnD,MAAMyQ,IAAA,GAAOF,iBAAA,CAAkBC,cAAA,EAA6CoC,sBAAA,CAAuBhT,MAAM;IACzG,MAAMiT,gBAAA,GAAmC,EAAC;IAC1C,IAAI;MACF1B,KAAA,CAAM3T,OAAA,CAAQ7K,GAAA,CAAIigB,sBAAsB;MACxC,MAAMhS,OAAA,CAAQI,OAAA,CAAQmQ,KAAA,CAAMN,MAAA,CAAOniB,MAAA;MAAA;MAEnC7C,MAAA,CAAO,CAAC,GAAG8mB,GAAA,EAAK;QACd5B,gBAAA;QACApR,SAAA,EAAWA,CAAC+Q,SAAA,EAAsC3Y,OAAA,KAAqB0Y,IAAA,CAAKC,SAAA,EAAW3Y,OAAO,EAAEoJ,IAAA,CAAK2R,OAAO;QAC5GrC,IAAA;QACAL,KAAA,EAAOd,WAAA,CAAYsD,sBAAA,CAAuBhT,MAAM;QAChD2P,KAAA,EAAOH,WAAA,CAAiBwD,sBAAA,CAAuBhT,MAAM;QACrDC,KAAA;QACAD,MAAA,EAAQgT,sBAAA,CAAuBhT,MAAA;QAC/BmT,IAAA,EAAMpD,UAAA,CAAWiD,sBAAA,CAAuBhT,MAAA,EAAQiT,gBAAgB;QAChE3Z,WAAA,EAAaiY,KAAA,CAAMjY,WAAA;QACnBH,SAAA,EAAWA,CAAA,KAAM;UACfsY,WAAA,CAAYxf,GAAA,CAAIsf,KAAA,CAAM9S,EAAA,EAAI8S,KAAK;QACjC;QACAM,qBAAA,EAAuBA,CAAA,KAAM;UAC3BN,KAAA,CAAM3T,OAAA,CAAQ3E,OAAA,CAAQ,CAACkX,UAAA,EAAYtb,CAAA,EAAG5C,GAAA,KAAQ;YAC5C,IAAIke,UAAA,KAAe6C,sBAAA,EAAwB;cACzCtE,yBAAA,CAA0ByB,UAAA,EAAYxC,iBAAiB;cACvD1b,GAAA,CAAIsH,MAAA,CAAO4W,UAAU;YACvB;UACF,CAAC;QACH;QACAO,MAAA,EAAQA,CAAA,KAAM;UACZhC,yBAAA,CAA0BsE,sBAAA,EAAwBrF,iBAAiB;UACnE4D,KAAA,CAAM3T,OAAA,CAAQrE,MAAA,CAAOyZ,sBAAsB;QAC7C;QACAI,gBAAA,EAAkBA,CAAA,KAAM;UACtBrE,cAAA,CAAeiE,sBAAA,CAAuBhT,MAAM;QAC9C;MACF,CAAC,CAAC,CAAC;IACL,SAASqT,aAAA,EAAe;MACtB,IAAI,EAAEA,aAAA,YAAyBxF,cAAA,GAAiB;QAC9CmE,iBAAA,CAAkB3D,OAAA,EAASgF,aAAA,EAAe;UACxCC,QAAA,EAAU;QACZ,CAAC;MACH;IACF,UAAE;MACA,MAAMtS,OAAA,CAAQuS,GAAA,CAAIN,gBAAgB;MAClCvE,yBAAA,CAA0BsE,sBAAA,EAAwBpF,iBAAiB;MACnE2D,KAAA,CAAM3T,OAAA,CAAQrE,MAAA,CAAOyZ,sBAAsB;IAC7C;EACF;EACA,MAAMQ,uBAAA,GAA0B1B,6BAAA,CAA8BL,WAAW;EACzE,MAAMzX,UAAA,GAAyE+Y,GAAA,IAAOnjB,IAAA,IAAQd,MAAA,IAAU;IACtG,IAAI,CAACse,SAAA,CAASte,MAAM,GAAG;MAErB,OAAOc,IAAA,CAAKd,MAAM;IACpB;IACA,IAAIujB,WAAA,CAAYtkB,KAAA,CAAMe,MAAM,GAAG;MAC7B,OAAO8hB,cAAA,CAAe9hB,MAAA,CAAOJ,OAAc;IAC7C;IACA,IAAI4jB,iBAAA,CAAkBvkB,KAAA,CAAMe,MAAM,GAAG;MACnC0kB,uBAAA,CAAwB;MACxB;IACF;IACA,IAAIjB,cAAA,CAAexkB,KAAA,CAAMe,MAAM,GAAG;MAChC,OAAOkiB,aAAA,CAAcliB,MAAA,CAAOJ,OAAO;IACrC;IAGA,IAAI+kB,aAAA,GAAuDV,GAAA,CAAIvd,QAAA,CAAS;IAIxE,MAAM2b,gBAAA,GAAmBA,CAAA,KAAiB;MACxC,IAAIsC,aAAA,KAAkB5D,kBAAA,EAAoB;QACxC,MAAM,IAAIxhB,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,GAAGqhB,GAAG,qDAAqD;MACpJ;MACA,OAAO2D,aAAA;IACT;IACA,IAAIvf,MAAA;IACJ,IAAI;MAEFA,MAAA,GAAStE,IAAA,CAAKd,MAAM;MACpB,IAAI2iB,WAAA,CAAYjT,IAAA,GAAO,GAAG;QACxB,MAAMkV,YAAA,GAAeX,GAAA,CAAIvd,QAAA,CAAS;QAElC,MAAMme,eAAA,GAAkB5iB,KAAA,CAAM2gB,IAAA,CAAKD,WAAA,CAAYjb,MAAA,CAAO,CAAC;QACvD,WAAW+a,KAAA,IAASoC,eAAA,EAAiB;UACnC,IAAIC,WAAA,GAAc;UAClB,IAAI;YACFA,WAAA,GAAcrC,KAAA,CAAMT,SAAA,CAAUhiB,MAAA,EAAQ4kB,YAAA,EAAcD,aAAa;UACnE,SAASI,cAAA,EAAgB;YACvBD,WAAA,GAAc;YACd5B,iBAAA,CAAkB3D,OAAA,EAASwF,cAAA,EAAgB;cACzCP,QAAA,EAAU;YACZ,CAAC;UACH;UACA,IAAI,CAACM,WAAA,EAAa;YAChB;UACF;UACAd,cAAA,CAAevB,KAAA,EAAOziB,MAAA,EAAQikB,GAAA,EAAK5B,gBAAgB;QACrD;MACF;IACF,UAAE;MAEAsC,aAAA,GAAgB5D,kBAAA;IAClB;IACA,OAAO3b,MAAA;EACT;EACA,OAAO;IACL8F,UAAA;IACA4W,cAAA;IACAI,aAAA;IACA8C,cAAA,EAAgBN;EAClB;AACF;;;AIvWA,SAAS7mB,OAAA,IAAAonB,QAAA,QAAe;AAOxB,IAAMC,qBAAA,GAA8Gha,UAAA,KAA4F;EAC9MA,UAAA;EACAia,OAAA,EAAS,mBAAI5P,GAAA,CAAI;AACnB;AACA,IAAM6P,aAAA,GAAiBC,UAAA,IAAwBrlB,MAAA,IAI1CA,MAAA,EAAQH,IAAA,EAAMwlB,UAAA,KAAeA,UAAA;AAC3B,IAAMC,uBAAA,GAA0BA,CAAA,KAA2I;EAChL,MAAMD,UAAA,GAAa5V,MAAA,CAAO;EAC1B,MAAM8V,aAAA,GAAgB,mBAAIhQ,GAAA,CAAgF;EAC1G,MAAMiQ,cAAA,GAAiBtoB,MAAA,CAAOC,MAAA,CAAO+B,YAAA,CAAa,yBAAyB,IAAIumB,WAAA,MAAyD;IACtI7lB,OAAA,EAAS6lB,WAAA;IACT5lB,IAAA,EAAM;MACJwlB;IACF;EACF,EAAE,GAAG;IACH5nB,SAAA,EAAWA,CAAA,KAAM+nB;EACnB,CAAC;EACD,MAAME,aAAA,GAAgBxoB,MAAA,CAAOC,MAAA,CAAO,SAASwoB,eAAA,GAAiBF,WAAA,EAAqD;IACjHA,WAAA,CAAYtb,OAAA,CAAQwB,WAAA,IAAc;MAChC7I,mBAAA,CAAoByiB,aAAA,EAAe5Z,WAAA,EAAYuZ,qBAAqB;IACtE,CAAC;EACH,GAAG;IACDznB,SAAA,EAAWA,CAAA,KAAMioB;EACnB,CAAC;EACD,MAAME,kBAAA,GAA0D3B,GAAA,IAAO;IACrE,MAAM4B,iBAAA,GAAoB5jB,KAAA,CAAM2gB,IAAA,CAAK2C,aAAA,CAAc7d,MAAA,CAAO,CAAC,EAAE3E,GAAA,CAAI0f,KAAA,IAAS3f,mBAAA,CAAoB2f,KAAA,CAAM0C,OAAA,EAASlB,GAAA,EAAKxB,KAAA,CAAMvX,UAAU,CAAC;IACnI,OAAO+Z,QAAA,CAAQ,GAAGY,iBAAiB;EACrC;EACA,MAAMC,gBAAA,GAAmB5X,OAAA,CAAQsX,cAAA,EAAgBJ,aAAA,CAAcC,UAAU,CAAC;EAC1E,MAAMna,UAAA,GAAqD+Y,GAAA,IAAOnjB,IAAA,IAAQd,MAAA,IAAU;IAClF,IAAI8lB,gBAAA,CAAiB9lB,MAAM,GAAG;MAC5B0lB,aAAA,CAAc,GAAG1lB,MAAA,CAAOJ,OAAO;MAC/B,OAAOqkB,GAAA,CAAIvZ,QAAA;IACb;IACA,OAAOkb,kBAAA,CAAmB3B,GAAG,EAAEnjB,IAAI,EAAEd,MAAM;EAC7C;EACA,OAAO;IACLkL,UAAA;IACAwa,aAAA;IACAF,cAAA;IACAH;EACF;AACF;;;ACnDA,SAAStnB,eAAA,IAAAgoB,gBAAA,QAAuB;AAqOhC,IAAMC,WAAA,GAAeC,cAAA,IAA8E,iBAAiBA,cAAA,IAAkB,OAAOA,cAAA,CAAerS,WAAA,KAAgB;AAC5K,IAAMsS,WAAA,GAAeC,MAAA,IAA6CA,MAAA,CAAO5W,OAAA,CAAQ6W,UAAA,IAAcJ,WAAA,CAAYI,UAAU,IAAI,CAAC,CAACA,UAAA,CAAWxS,WAAA,EAAawS,UAAA,CAAWnb,OAAO,CAAU,IAAI/N,MAAA,CAAOqK,OAAA,CAAQ6e,UAAU,CAAC;AAC7M,IAAMC,cAAA,GAAiB/jB,MAAA,CAAO4Q,GAAA,CAAI,0BAA0B;AAC5D,IAAMoT,YAAA,GAAgB/oB,KAAA,IAAe,CAAC,CAACA,KAAA,IAAS,CAAC,CAACA,KAAA,CAAM8oB,cAAc;AACtE,IAAME,aAAA,GAAgB,mBAAI9Q,OAAA,CAAwB;AAClD,IAAM+Q,gBAAA,GAAmBA,CAAwB7f,KAAA,EAAc8f,UAAA,EAAmDC,iBAAA,KAAoD5jB,mBAAA,CAAoByjB,aAAA,EAAe5f,KAAA,EAAO,MAAM,IAAIggB,KAAA,CAAMhgB,KAAA,EAAO;EACrOzD,GAAA,EAAKA,CAAC0jB,MAAA,EAAQC,IAAA,EAAMC,QAAA,KAAa;IAC/B,IAAID,IAAA,KAASR,cAAA,EAAgB,OAAOO,MAAA;IACpC,MAAMxhB,MAAA,GAAS2hB,OAAA,CAAQ7jB,GAAA,CAAI0jB,MAAA,EAAQC,IAAA,EAAMC,QAAQ;IACjD,IAAI,OAAO1hB,MAAA,KAAW,aAAa;MACjC,MAAM4hB,MAAA,GAASN,iBAAA,CAAkBG,IAAI;MACrC,IAAI,OAAOG,MAAA,KAAW,aAAa,OAAOA,MAAA;MAC1C,MAAM/b,OAAA,GAAUwb,UAAA,CAAWI,IAAI;MAC/B,IAAI5b,OAAA,EAAS;QAEX,MAAMgc,aAAA,GAAgBhc,OAAA,CAAQ,QAAW;UACvC9L,IAAA,EAAMsQ,MAAA,CAAO;QACf,CAAC;QACD,IAAI,OAAOwX,aAAA,KAAkB,aAAa;UACxC,MAAM,IAAI1nB,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAwB,EAAE,IAAI,8BAA8BknB,IAAA,CAAK9mB,QAAA,CAAS,CAAC,mRAAuS;QAC5a;QACA2mB,iBAAA,CAAkBG,IAAI,IAAII,aAAA;QAC1B,OAAOA,aAAA;MACT;IACF;IACA,OAAO7hB,MAAA;EACT;AACF,CAAC,CAAC;AACF,IAAMhJ,QAAA,GAAYuK,KAAA,IAAe;EAC/B,IAAI,CAAC2f,YAAA,CAAa3f,KAAK,GAAG;IACxB,MAAM,IAAIpH,KAAA,CAAMC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAeC,sBAAA,CAAyB,EAAE,IAAI,sCAAsC;EAC/H;EACA,OAAOgH,KAAA,CAAM0f,cAAc;AAC7B;AACA,IAAMa,WAAA,GAAc,CAAC;AACrB,IAAMC,WAAA,GAA4CA,CAACxgB,KAAA,GAAQugB,WAAA,KAAgBvgB,KAAA;AACpE,SAASygB,cAAA,GAAkEjB,MAAA,EAAgE;EAChJ,MAAMM,UAAA,GAAavpB,MAAA,CAAOmqB,WAAA,CAAqBnB,WAAA,CAAYC,MAAM,CAAC;EAClE,MAAMmB,UAAA,GAAaA,CAAA,KAAMpqB,MAAA,CAAOiD,IAAA,CAAKsmB,UAAU,EAAEnoB,MAAA,GAASynB,gBAAA,CAAgBU,UAAU,IAAIU,WAAA;EACxF,IAAIlc,OAAA,GAAUqc,UAAA,CAAW;EACzB,SAASC,gBAAgB5gB,KAAA,EAAgC3G,MAAA,EAAuB;IAC9E,OAAOiL,OAAA,CAAQtE,KAAA,EAAO3G,MAAM;EAC9B;EACAunB,eAAA,CAAgBC,oBAAA,GAAuB,MAAMD,eAAA;EAC7C,MAAMb,iBAAA,GAAkD,CAAC;EACzD,MAAM/P,MAAA,GAASA,CAAC3Q,KAAA,EAAqByQ,MAAA,GAAuB,CAAC,MAA8B;IACzF,MAAM;MACJ7C,WAAA;MACA3I,OAAA,EAASwc;IACX,IAAIzhB,KAAA;IACJ,MAAM0hB,cAAA,GAAiBjB,UAAA,CAAW7S,WAAW;IAC7C,IAAI,CAAC6C,MAAA,CAAOkR,gBAAA,IAAoBD,cAAA,IAAkBA,cAAA,KAAmBD,eAAA,EAAiB;MACpF,IAAI,OAAOjoB,OAAA,KAAY,eAAeA,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eAAe;QAC5EsB,OAAA,CAAQlB,KAAA,CAAM,0DAA0D8T,WAAW,gDAAgD;MACrI;MACA,OAAO2T,eAAA;IACT;IACA,IAAI9Q,MAAA,CAAOkR,gBAAA,IAAoBD,cAAA,KAAmBD,eAAA,EAAiB;MACjE,OAAOf,iBAAA,CAAkB9S,WAAW;IACtC;IACA6S,UAAA,CAAW7S,WAAW,IAAI6T,eAAA;IAC1Bxc,OAAA,GAAUqc,UAAA,CAAW;IACrB,OAAOC,eAAA;EACT;EACA,MAAMlqB,QAAA,GAAWH,MAAA,CAAOC,MAAA,CAAO,SAASyqB,aAAkEC,UAAA,EAAkD5R,WAAA,EAA8D;IACxN,OAAO,SAAS6R,UAASnhB,KAAA,KAAiB5J,IAAA,EAAY;MACpD,OAAO8qB,UAAA,CAAWrB,gBAAA,CAAiBvQ,WAAA,GAAcA,WAAA,CAAYtP,KAAA,EAAc,GAAG5J,IAAI,IAAI4J,KAAA,EAAO8f,UAAA,EAAYC,iBAAiB,GAAG,GAAG3pB,IAAI;IACtI;EACF,GAAG;IACDX;EACF,CAAC;EACD,OAAOc,MAAA,CAAOC,MAAA,CAAOoqB,eAAA,EAAiB;IACpC5Q,MAAA;IACAtZ;EACF,CAAC;AACH;;;AC3SO,SAASsC,uBAAuBqf,IAAA,EAAc;EACnD,OAAO,iCAAiCA,IAAI,oDAAoDA,IAAI;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}