{"ast": null, "code": "export default function (a, b) {\n  return a = +a, b = +b, function (t) {\n    return a * (1 - t) + b * t;\n  };\n}", "map": {"version": 3, "names": ["a", "b", "t"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-interpolate/src/number.js"], "sourcesContent": ["export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC,EAAE,UAASC,CAAC,EAAE;IACjC,OAAOF,CAAC,IAAI,CAAC,GAAGE,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}