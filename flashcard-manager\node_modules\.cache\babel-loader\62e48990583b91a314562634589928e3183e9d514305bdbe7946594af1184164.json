{"ast": null, "code": "export var selectChartWidth = state => state.layout.width;\nexport var selectChartHeight = state => state.layout.height;\nexport var selectContainerScale = state => state.layout.scale;\nexport var selectMargin = state => state.layout.margin;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "layout", "width", "selectChartHeight", "height", "selectContainerScale", "scale", "<PERSON><PERSON><PERSON><PERSON>", "margin"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/selectors/containerSelectors.js"], "sourcesContent": ["export var selectChartWidth = state => state.layout.width;\nexport var selectChartHeight = state => state.layout.height;\nexport var selectContainerScale = state => state.layout.scale;\nexport var selectMargin = state => state.layout.margin;"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAGC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACC,KAAK;AACzD,OAAO,IAAIC,iBAAiB,GAAGH,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACG,MAAM;AAC3D,OAAO,IAAIC,oBAAoB,GAAGL,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACK,KAAK;AAC7D,OAAO,IAAIC,YAAY,GAAGP,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACO,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}