{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/use-sync-external-store/shim/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,iEAAiE,CAAC;AAC7F,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,kEAAkE,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}