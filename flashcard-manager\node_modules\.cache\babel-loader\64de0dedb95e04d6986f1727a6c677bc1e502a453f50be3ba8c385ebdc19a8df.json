{"ast": null, "code": "/**\n * @fileOverview calculate tick values of scale\n * <AUTHOR> arcthur\n * @date 2015-09-17\n */\nimport Decimal from 'decimal.js-light';\nimport { compose, range, memoize, map, reverse } from './util/utils';\nimport { getDigitCount, rangeStep } from './util/arithmetic';\n\n/**\n * Calculate a interval of a minimum value and a maximum value\n *\n * @param  {Number} min       The minimum value\n * @param  {Number} max       The maximum value\n * @return {Array} An interval\n */\nexport var getValidInterval = _ref => {\n  var [min, max] = _ref;\n  var [validMin, validMax] = [min, max];\n\n  // exchange\n  if (min > max) {\n    [validMin, validMax] = [max, min];\n  }\n  return [validMin, validMax];\n};\n\n/**\n * Calculate the step which is easy to understand between ticks, like 10, 20, 25\n *\n * @param  {Decimal} roughStep        The rough step calculated by dividing the\n * difference by the tickCount\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Integer} correctionFactor A correction factor\n * @return {Decimal} The step which is easy to understand between two ticks\n */\nexport var getFormatStep = (roughStep, allowDecimals, correctionFactor) => {\n  if (roughStep.lte(0)) {\n    return new Decimal(0);\n  }\n  var digitCount = getDigitCount(roughStep.toNumber());\n  // The ratio between the rough step and the smallest number which has a bigger\n  // order of magnitudes than the rough step\n  var digitCountValue = new Decimal(10).pow(digitCount);\n  var stepRatio = roughStep.div(digitCountValue);\n  // When an integer and a float multiplied, the accuracy of result may be wrong\n  var stepRatioScale = digitCount !== 1 ? 0.05 : 0.1;\n  var amendStepRatio = new Decimal(Math.ceil(stepRatio.div(stepRatioScale).toNumber())).add(correctionFactor).mul(stepRatioScale);\n  var formatStep = amendStepRatio.mul(digitCountValue);\n  return allowDecimals ? new Decimal(formatStep.toNumber()) : new Decimal(Math.ceil(formatStep.toNumber()));\n};\n\n/**\n * calculate the ticks when the minimum value equals to the maximum value\n *\n * @param  value         The minimum value which is also the maximum value\n * @param  tickCount     The count of ticks\n * @param  allowDecimals Allow the ticks to be decimals or not\n * @return array of ticks\n */\nexport var getTickOfSingleValue = (value, tickCount, allowDecimals) => {\n  var step = new Decimal(1);\n  // calculate the middle value of ticks\n  var middle = new Decimal(value);\n  if (!middle.isint() && allowDecimals) {\n    var absVal = Math.abs(value);\n    if (absVal < 1) {\n      // The step should be a float number when the difference is smaller than 1\n      step = new Decimal(10).pow(getDigitCount(value) - 1);\n      middle = new Decimal(Math.floor(middle.div(step).toNumber())).mul(step);\n    } else if (absVal > 1) {\n      // Return the maximum integer which is smaller than 'value' when 'value' is greater than 1\n      middle = new Decimal(Math.floor(value));\n    }\n  } else if (value === 0) {\n    middle = new Decimal(Math.floor((tickCount - 1) / 2));\n  } else if (!allowDecimals) {\n    middle = new Decimal(Math.floor(value));\n  }\n  var middleIndex = Math.floor((tickCount - 1) / 2);\n  var fn = compose(map(n => middle.add(new Decimal(n - middleIndex).mul(step)).toNumber()), range);\n  return fn(0, tickCount);\n};\n\n/**\n * Calculate the step\n *\n * @param  {Number}  min              The minimum value of an interval\n * @param  {Number}  max              The maximum value of an interval\n * @param  {Integer} tickCount        The count of ticks\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Number}  correctionFactor A correction factor\n * @return {Object}  The step, minimum value of ticks, maximum value of ticks\n */\nvar _calculateStep = function calculateStep(min, max, tickCount, allowDecimals) {\n  var correctionFactor = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n  // dirty hack (for recharts' test)\n  if (!Number.isFinite((max - min) / (tickCount - 1))) {\n    return {\n      step: new Decimal(0),\n      tickMin: new Decimal(0),\n      tickMax: new Decimal(0)\n    };\n  }\n\n  // The step which is easy to understand between two ticks\n  var step = getFormatStep(new Decimal(max).sub(min).div(tickCount - 1), allowDecimals, correctionFactor);\n\n  // A medial value of ticks\n  var middle;\n\n  // When 0 is inside the interval, 0 should be a tick\n  if (min <= 0 && max >= 0) {\n    middle = new Decimal(0);\n  } else {\n    // calculate the middle value\n    middle = new Decimal(min).add(max).div(2);\n    // minus modulo value\n    middle = middle.sub(new Decimal(middle).mod(step));\n  }\n  var belowCount = Math.ceil(middle.sub(min).div(step).toNumber());\n  var upCount = Math.ceil(new Decimal(max).sub(middle).div(step).toNumber());\n  var scaleCount = belowCount + upCount + 1;\n  if (scaleCount > tickCount) {\n    // When more ticks need to cover the interval, step should be bigger.\n    return _calculateStep(min, max, tickCount, allowDecimals, correctionFactor + 1);\n  }\n  if (scaleCount < tickCount) {\n    // When less ticks can cover the interval, we should add some additional ticks\n    upCount = max > 0 ? upCount + (tickCount - scaleCount) : upCount;\n    belowCount = max > 0 ? belowCount : belowCount + (tickCount - scaleCount);\n  }\n  return {\n    step,\n    tickMin: middle.sub(new Decimal(belowCount).mul(step)),\n    tickMax: middle.add(new Decimal(upCount).mul(step))\n  };\n};\n\n/**\n * Calculate the ticks of an interval, the count of ticks will be guaranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\nexport { _calculateStep as calculateStep };\nfunction getNiceTickValuesFn(_ref2) {\n  var [min, max] = _ref2;\n  var tickCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 6;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var count = Math.max(tickCount, 2);\n  var [cormin, cormax] = getValidInterval([min, max]);\n  if (cormin === -Infinity || cormax === Infinity) {\n    var _values = cormax === Infinity ? [cormin, ...range(0, tickCount - 1).map(() => Infinity)] : [...range(0, tickCount - 1).map(() => -Infinity), cormax];\n    return min > max ? reverse(_values) : _values;\n  }\n  if (cormin === cormax) {\n    return getTickOfSingleValue(cormin, tickCount, allowDecimals);\n  }\n\n  // Get the step between two ticks\n  var {\n    step,\n    tickMin,\n    tickMax\n  } = _calculateStep(cormin, cormax, count, allowDecimals, 0);\n  var values = rangeStep(tickMin, tickMax.add(new Decimal(0.1).mul(step)), step);\n  return min > max ? reverse(values) : values;\n}\n\n/**\n * Calculate the ticks of an interval, the count of ticks won't be guraranteed,\n * but the domain will be guaranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\nfunction getTickValuesFixedDomainFn(_ref3, tickCount) {\n  var [min, max] = _ref3;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var [cormin, cormax] = getValidInterval([min, max]);\n  if (cormin === -Infinity || cormax === Infinity) {\n    return [min, max];\n  }\n  if (cormin === cormax) {\n    return [cormin];\n  }\n  var count = Math.max(tickCount, 2);\n  var step = getFormatStep(new Decimal(cormax).sub(cormin).div(count - 1), allowDecimals, 0);\n  var values = [...rangeStep(new Decimal(cormin), new Decimal(cormax).sub(new Decimal(0.99).mul(step)), step), cormax];\n  return min > max ? reverse(values) : values;\n}\nexport var getNiceTickValues = memoize(getNiceTickValuesFn);\nexport var getTickValuesFixedDomain = memoize(getTickValuesFixedDomainFn);", "map": {"version": 3, "names": ["Decimal", "compose", "range", "memoize", "map", "reverse", "getDigitCount", "rangeStep", "getValidInterval", "_ref", "min", "max", "validMin", "validMax", "getFormatStep", "roughStep", "allowDecimals", "correctionFactor", "lte", "digitCount", "toNumber", "digitCountValue", "pow", "stepRatio", "div", "stepRatioScale", "amendStepRatio", "Math", "ceil", "add", "mul", "formatStep", "getTickOfSingleValue", "value", "tickCount", "step", "middle", "isint", "absVal", "abs", "floor", "middleIndex", "fn", "n", "_calculateStep", "calculateStep", "arguments", "length", "undefined", "Number", "isFinite", "tickMin", "tickMax", "sub", "mod", "belowCount", "upCount", "scaleCount", "getNiceTickValuesFn", "_ref2", "count", "cormin", "cormax", "Infinity", "_values", "values", "getTickValuesFixedDomainFn", "_ref3", "getNiceTickValues", "getTickValuesFixedDomain"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/util/scale/getNiceTickValues.js"], "sourcesContent": ["/**\n * @fileOverview calculate tick values of scale\n * <AUTHOR> arcthur\n * @date 2015-09-17\n */\nimport Decimal from 'decimal.js-light';\nimport { compose, range, memoize, map, reverse } from './util/utils';\nimport { getDigitCount, rangeStep } from './util/arithmetic';\n\n/**\n * Calculate a interval of a minimum value and a maximum value\n *\n * @param  {Number} min       The minimum value\n * @param  {Number} max       The maximum value\n * @return {Array} An interval\n */\nexport var getValidInterval = _ref => {\n  var [min, max] = _ref;\n  var [validMin, validMax] = [min, max];\n\n  // exchange\n  if (min > max) {\n    [validMin, validMax] = [max, min];\n  }\n  return [validMin, validMax];\n};\n\n/**\n * Calculate the step which is easy to understand between ticks, like 10, 20, 25\n *\n * @param  {Decimal} roughStep        The rough step calculated by dividing the\n * difference by the tickCount\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Integer} correctionFactor A correction factor\n * @return {Decimal} The step which is easy to understand between two ticks\n */\nexport var getFormatStep = (roughStep, allowDecimals, correctionFactor) => {\n  if (roughStep.lte(0)) {\n    return new Decimal(0);\n  }\n  var digitCount = getDigitCount(roughStep.toNumber());\n  // The ratio between the rough step and the smallest number which has a bigger\n  // order of magnitudes than the rough step\n  var digitCountValue = new Decimal(10).pow(digitCount);\n  var stepRatio = roughStep.div(digitCountValue);\n  // When an integer and a float multiplied, the accuracy of result may be wrong\n  var stepRatioScale = digitCount !== 1 ? 0.05 : 0.1;\n  var amendStepRatio = new Decimal(Math.ceil(stepRatio.div(stepRatioScale).toNumber())).add(correctionFactor).mul(stepRatioScale);\n  var formatStep = amendStepRatio.mul(digitCountValue);\n  return allowDecimals ? new Decimal(formatStep.toNumber()) : new Decimal(Math.ceil(formatStep.toNumber()));\n};\n\n/**\n * calculate the ticks when the minimum value equals to the maximum value\n *\n * @param  value         The minimum value which is also the maximum value\n * @param  tickCount     The count of ticks\n * @param  allowDecimals Allow the ticks to be decimals or not\n * @return array of ticks\n */\nexport var getTickOfSingleValue = (value, tickCount, allowDecimals) => {\n  var step = new Decimal(1);\n  // calculate the middle value of ticks\n  var middle = new Decimal(value);\n  if (!middle.isint() && allowDecimals) {\n    var absVal = Math.abs(value);\n    if (absVal < 1) {\n      // The step should be a float number when the difference is smaller than 1\n      step = new Decimal(10).pow(getDigitCount(value) - 1);\n      middle = new Decimal(Math.floor(middle.div(step).toNumber())).mul(step);\n    } else if (absVal > 1) {\n      // Return the maximum integer which is smaller than 'value' when 'value' is greater than 1\n      middle = new Decimal(Math.floor(value));\n    }\n  } else if (value === 0) {\n    middle = new Decimal(Math.floor((tickCount - 1) / 2));\n  } else if (!allowDecimals) {\n    middle = new Decimal(Math.floor(value));\n  }\n  var middleIndex = Math.floor((tickCount - 1) / 2);\n  var fn = compose(map(n => middle.add(new Decimal(n - middleIndex).mul(step)).toNumber()), range);\n  return fn(0, tickCount);\n};\n\n/**\n * Calculate the step\n *\n * @param  {Number}  min              The minimum value of an interval\n * @param  {Number}  max              The maximum value of an interval\n * @param  {Integer} tickCount        The count of ticks\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Number}  correctionFactor A correction factor\n * @return {Object}  The step, minimum value of ticks, maximum value of ticks\n */\nvar _calculateStep = function calculateStep(min, max, tickCount, allowDecimals) {\n  var correctionFactor = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n  // dirty hack (for recharts' test)\n  if (!Number.isFinite((max - min) / (tickCount - 1))) {\n    return {\n      step: new Decimal(0),\n      tickMin: new Decimal(0),\n      tickMax: new Decimal(0)\n    };\n  }\n\n  // The step which is easy to understand between two ticks\n  var step = getFormatStep(new Decimal(max).sub(min).div(tickCount - 1), allowDecimals, correctionFactor);\n\n  // A medial value of ticks\n  var middle;\n\n  // When 0 is inside the interval, 0 should be a tick\n  if (min <= 0 && max >= 0) {\n    middle = new Decimal(0);\n  } else {\n    // calculate the middle value\n    middle = new Decimal(min).add(max).div(2);\n    // minus modulo value\n    middle = middle.sub(new Decimal(middle).mod(step));\n  }\n  var belowCount = Math.ceil(middle.sub(min).div(step).toNumber());\n  var upCount = Math.ceil(new Decimal(max).sub(middle).div(step).toNumber());\n  var scaleCount = belowCount + upCount + 1;\n  if (scaleCount > tickCount) {\n    // When more ticks need to cover the interval, step should be bigger.\n    return _calculateStep(min, max, tickCount, allowDecimals, correctionFactor + 1);\n  }\n  if (scaleCount < tickCount) {\n    // When less ticks can cover the interval, we should add some additional ticks\n    upCount = max > 0 ? upCount + (tickCount - scaleCount) : upCount;\n    belowCount = max > 0 ? belowCount : belowCount + (tickCount - scaleCount);\n  }\n  return {\n    step,\n    tickMin: middle.sub(new Decimal(belowCount).mul(step)),\n    tickMax: middle.add(new Decimal(upCount).mul(step))\n  };\n};\n\n/**\n * Calculate the ticks of an interval, the count of ticks will be guaranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\nexport { _calculateStep as calculateStep };\nfunction getNiceTickValuesFn(_ref2) {\n  var [min, max] = _ref2;\n  var tickCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 6;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var count = Math.max(tickCount, 2);\n  var [cormin, cormax] = getValidInterval([min, max]);\n  if (cormin === -Infinity || cormax === Infinity) {\n    var _values = cormax === Infinity ? [cormin, ...range(0, tickCount - 1).map(() => Infinity)] : [...range(0, tickCount - 1).map(() => -Infinity), cormax];\n    return min > max ? reverse(_values) : _values;\n  }\n  if (cormin === cormax) {\n    return getTickOfSingleValue(cormin, tickCount, allowDecimals);\n  }\n\n  // Get the step between two ticks\n  var {\n    step,\n    tickMin,\n    tickMax\n  } = _calculateStep(cormin, cormax, count, allowDecimals, 0);\n  var values = rangeStep(tickMin, tickMax.add(new Decimal(0.1).mul(step)), step);\n  return min > max ? reverse(values) : values;\n}\n\n/**\n * Calculate the ticks of an interval, the count of ticks won't be guraranteed,\n * but the domain will be guaranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\nfunction getTickValuesFixedDomainFn(_ref3, tickCount) {\n  var [min, max] = _ref3;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var [cormin, cormax] = getValidInterval([min, max]);\n  if (cormin === -Infinity || cormax === Infinity) {\n    return [min, max];\n  }\n  if (cormin === cormax) {\n    return [cormin];\n  }\n  var count = Math.max(tickCount, 2);\n  var step = getFormatStep(new Decimal(cormax).sub(cormin).div(count - 1), allowDecimals, 0);\n  var values = [...rangeStep(new Decimal(cormin), new Decimal(cormax).sub(new Decimal(0.99).mul(step)), step), cormax];\n  return min > max ? reverse(values) : values;\n}\nexport var getNiceTickValues = memoize(getNiceTickValuesFn);\nexport var getTickValuesFixedDomain = memoize(getTickValuesFixedDomainFn);"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAOA,OAAO,MAAM,kBAAkB;AACtC,SAASC,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,QAAQ,cAAc;AACpE,SAASC,aAAa,EAAEC,SAAS,QAAQ,mBAAmB;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAGC,IAAI,IAAI;EACpC,IAAI,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGF,IAAI;EACrB,IAAI,CAACG,QAAQ,EAAEC,QAAQ,CAAC,GAAG,CAACH,GAAG,EAAEC,GAAG,CAAC;;EAErC;EACA,IAAID,GAAG,GAAGC,GAAG,EAAE;IACb,CAACC,QAAQ,EAAEC,QAAQ,CAAC,GAAG,CAACF,GAAG,EAAED,GAAG,CAAC;EACnC;EACA,OAAO,CAACE,QAAQ,EAAEC,QAAQ,CAAC;AAC7B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAGA,CAACC,SAAS,EAAEC,aAAa,EAAEC,gBAAgB,KAAK;EACzE,IAAIF,SAAS,CAACG,GAAG,CAAC,CAAC,CAAC,EAAE;IACpB,OAAO,IAAIlB,OAAO,CAAC,CAAC,CAAC;EACvB;EACA,IAAImB,UAAU,GAAGb,aAAa,CAACS,SAAS,CAACK,QAAQ,CAAC,CAAC,CAAC;EACpD;EACA;EACA,IAAIC,eAAe,GAAG,IAAIrB,OAAO,CAAC,EAAE,CAAC,CAACsB,GAAG,CAACH,UAAU,CAAC;EACrD,IAAII,SAAS,GAAGR,SAAS,CAACS,GAAG,CAACH,eAAe,CAAC;EAC9C;EACA,IAAII,cAAc,GAAGN,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG;EAClD,IAAIO,cAAc,GAAG,IAAI1B,OAAO,CAAC2B,IAAI,CAACC,IAAI,CAACL,SAAS,CAACC,GAAG,CAACC,cAAc,CAAC,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACS,GAAG,CAACZ,gBAAgB,CAAC,CAACa,GAAG,CAACL,cAAc,CAAC;EAC/H,IAAIM,UAAU,GAAGL,cAAc,CAACI,GAAG,CAACT,eAAe,CAAC;EACpD,OAAOL,aAAa,GAAG,IAAIhB,OAAO,CAAC+B,UAAU,CAACX,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAIpB,OAAO,CAAC2B,IAAI,CAACC,IAAI,CAACG,UAAU,CAACX,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3G,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIY,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,SAAS,EAAElB,aAAa,KAAK;EACrE,IAAImB,IAAI,GAAG,IAAInC,OAAO,CAAC,CAAC,CAAC;EACzB;EACA,IAAIoC,MAAM,GAAG,IAAIpC,OAAO,CAACiC,KAAK,CAAC;EAC/B,IAAI,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,IAAIrB,aAAa,EAAE;IACpC,IAAIsB,MAAM,GAAGX,IAAI,CAACY,GAAG,CAACN,KAAK,CAAC;IAC5B,IAAIK,MAAM,GAAG,CAAC,EAAE;MACd;MACAH,IAAI,GAAG,IAAInC,OAAO,CAAC,EAAE,CAAC,CAACsB,GAAG,CAAChB,aAAa,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAAC;MACpDG,MAAM,GAAG,IAAIpC,OAAO,CAAC2B,IAAI,CAACa,KAAK,CAACJ,MAAM,CAACZ,GAAG,CAACW,IAAI,CAAC,CAACf,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACU,GAAG,CAACK,IAAI,CAAC;IACzE,CAAC,MAAM,IAAIG,MAAM,GAAG,CAAC,EAAE;MACrB;MACAF,MAAM,GAAG,IAAIpC,OAAO,CAAC2B,IAAI,CAACa,KAAK,CAACP,KAAK,CAAC,CAAC;IACzC;EACF,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;IACtBG,MAAM,GAAG,IAAIpC,OAAO,CAAC2B,IAAI,CAACa,KAAK,CAAC,CAACN,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EACvD,CAAC,MAAM,IAAI,CAAClB,aAAa,EAAE;IACzBoB,MAAM,GAAG,IAAIpC,OAAO,CAAC2B,IAAI,CAACa,KAAK,CAACP,KAAK,CAAC,CAAC;EACzC;EACA,IAAIQ,WAAW,GAAGd,IAAI,CAACa,KAAK,CAAC,CAACN,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;EACjD,IAAIQ,EAAE,GAAGzC,OAAO,CAACG,GAAG,CAACuC,CAAC,IAAIP,MAAM,CAACP,GAAG,CAAC,IAAI7B,OAAO,CAAC2C,CAAC,GAAGF,WAAW,CAAC,CAACX,GAAG,CAACK,IAAI,CAAC,CAAC,CAACf,QAAQ,CAAC,CAAC,CAAC,EAAElB,KAAK,CAAC;EAChG,OAAOwC,EAAE,CAAC,CAAC,EAAER,SAAS,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIU,cAAc,GAAG,SAASC,aAAaA,CAACnC,GAAG,EAAEC,GAAG,EAAEuB,SAAS,EAAElB,aAAa,EAAE;EAC9E,IAAIC,gBAAgB,GAAG6B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAC5F;EACA,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAACvC,GAAG,GAAGD,GAAG,KAAKwB,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE;IACnD,OAAO;MACLC,IAAI,EAAE,IAAInC,OAAO,CAAC,CAAC,CAAC;MACpBmD,OAAO,EAAE,IAAInD,OAAO,CAAC,CAAC,CAAC;MACvBoD,OAAO,EAAE,IAAIpD,OAAO,CAAC,CAAC;IACxB,CAAC;EACH;;EAEA;EACA,IAAImC,IAAI,GAAGrB,aAAa,CAAC,IAAId,OAAO,CAACW,GAAG,CAAC,CAAC0C,GAAG,CAAC3C,GAAG,CAAC,CAACc,GAAG,CAACU,SAAS,GAAG,CAAC,CAAC,EAAElB,aAAa,EAAEC,gBAAgB,CAAC;;EAEvG;EACA,IAAImB,MAAM;;EAEV;EACA,IAAI1B,GAAG,IAAI,CAAC,IAAIC,GAAG,IAAI,CAAC,EAAE;IACxByB,MAAM,GAAG,IAAIpC,OAAO,CAAC,CAAC,CAAC;EACzB,CAAC,MAAM;IACL;IACAoC,MAAM,GAAG,IAAIpC,OAAO,CAACU,GAAG,CAAC,CAACmB,GAAG,CAAClB,GAAG,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC;IACzC;IACAY,MAAM,GAAGA,MAAM,CAACiB,GAAG,CAAC,IAAIrD,OAAO,CAACoC,MAAM,CAAC,CAACkB,GAAG,CAACnB,IAAI,CAAC,CAAC;EACpD;EACA,IAAIoB,UAAU,GAAG5B,IAAI,CAACC,IAAI,CAACQ,MAAM,CAACiB,GAAG,CAAC3C,GAAG,CAAC,CAACc,GAAG,CAACW,IAAI,CAAC,CAACf,QAAQ,CAAC,CAAC,CAAC;EAChE,IAAIoC,OAAO,GAAG7B,IAAI,CAACC,IAAI,CAAC,IAAI5B,OAAO,CAACW,GAAG,CAAC,CAAC0C,GAAG,CAACjB,MAAM,CAAC,CAACZ,GAAG,CAACW,IAAI,CAAC,CAACf,QAAQ,CAAC,CAAC,CAAC;EAC1E,IAAIqC,UAAU,GAAGF,UAAU,GAAGC,OAAO,GAAG,CAAC;EACzC,IAAIC,UAAU,GAAGvB,SAAS,EAAE;IAC1B;IACA,OAAOU,cAAc,CAAClC,GAAG,EAAEC,GAAG,EAAEuB,SAAS,EAAElB,aAAa,EAAEC,gBAAgB,GAAG,CAAC,CAAC;EACjF;EACA,IAAIwC,UAAU,GAAGvB,SAAS,EAAE;IAC1B;IACAsB,OAAO,GAAG7C,GAAG,GAAG,CAAC,GAAG6C,OAAO,IAAItB,SAAS,GAAGuB,UAAU,CAAC,GAAGD,OAAO;IAChED,UAAU,GAAG5C,GAAG,GAAG,CAAC,GAAG4C,UAAU,GAAGA,UAAU,IAAIrB,SAAS,GAAGuB,UAAU,CAAC;EAC3E;EACA,OAAO;IACLtB,IAAI;IACJgB,OAAO,EAAEf,MAAM,CAACiB,GAAG,CAAC,IAAIrD,OAAO,CAACuD,UAAU,CAAC,CAACzB,GAAG,CAACK,IAAI,CAAC,CAAC;IACtDiB,OAAO,EAAEhB,MAAM,CAACP,GAAG,CAAC,IAAI7B,OAAO,CAACwD,OAAO,CAAC,CAAC1B,GAAG,CAACK,IAAI,CAAC;EACpD,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,cAAc,IAAIC,aAAa;AACxC,SAASa,mBAAmBA,CAACC,KAAK,EAAE;EAClC,IAAI,CAACjD,GAAG,EAAEC,GAAG,CAAC,GAAGgD,KAAK;EACtB,IAAIzB,SAAS,GAAGY,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACrF,IAAI9B,aAAa,GAAG8B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC5F;EACA,IAAIc,KAAK,GAAGjC,IAAI,CAAChB,GAAG,CAACuB,SAAS,EAAE,CAAC,CAAC;EAClC,IAAI,CAAC2B,MAAM,EAAEC,MAAM,CAAC,GAAGtD,gBAAgB,CAAC,CAACE,GAAG,EAAEC,GAAG,CAAC,CAAC;EACnD,IAAIkD,MAAM,KAAK,CAACE,QAAQ,IAAID,MAAM,KAAKC,QAAQ,EAAE;IAC/C,IAAIC,OAAO,GAAGF,MAAM,KAAKC,QAAQ,GAAG,CAACF,MAAM,EAAE,GAAG3D,KAAK,CAAC,CAAC,EAAEgC,SAAS,GAAG,CAAC,CAAC,CAAC9B,GAAG,CAAC,MAAM2D,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG7D,KAAK,CAAC,CAAC,EAAEgC,SAAS,GAAG,CAAC,CAAC,CAAC9B,GAAG,CAAC,MAAM,CAAC2D,QAAQ,CAAC,EAAED,MAAM,CAAC;IACxJ,OAAOpD,GAAG,GAAGC,GAAG,GAAGN,OAAO,CAAC2D,OAAO,CAAC,GAAGA,OAAO;EAC/C;EACA,IAAIH,MAAM,KAAKC,MAAM,EAAE;IACrB,OAAO9B,oBAAoB,CAAC6B,MAAM,EAAE3B,SAAS,EAAElB,aAAa,CAAC;EAC/D;;EAEA;EACA,IAAI;IACFmB,IAAI;IACJgB,OAAO;IACPC;EACF,CAAC,GAAGR,cAAc,CAACiB,MAAM,EAAEC,MAAM,EAAEF,KAAK,EAAE5C,aAAa,EAAE,CAAC,CAAC;EAC3D,IAAIiD,MAAM,GAAG1D,SAAS,CAAC4C,OAAO,EAAEC,OAAO,CAACvB,GAAG,CAAC,IAAI7B,OAAO,CAAC,GAAG,CAAC,CAAC8B,GAAG,CAACK,IAAI,CAAC,CAAC,EAAEA,IAAI,CAAC;EAC9E,OAAOzB,GAAG,GAAGC,GAAG,GAAGN,OAAO,CAAC4D,MAAM,CAAC,GAAGA,MAAM;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACC,KAAK,EAAEjC,SAAS,EAAE;EACpD,IAAI,CAACxB,GAAG,EAAEC,GAAG,CAAC,GAAGwD,KAAK;EACtB,IAAInD,aAAa,GAAG8B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC5F;EACA,IAAI,CAACe,MAAM,EAAEC,MAAM,CAAC,GAAGtD,gBAAgB,CAAC,CAACE,GAAG,EAAEC,GAAG,CAAC,CAAC;EACnD,IAAIkD,MAAM,KAAK,CAACE,QAAQ,IAAID,MAAM,KAAKC,QAAQ,EAAE;IAC/C,OAAO,CAACrD,GAAG,EAAEC,GAAG,CAAC;EACnB;EACA,IAAIkD,MAAM,KAAKC,MAAM,EAAE;IACrB,OAAO,CAACD,MAAM,CAAC;EACjB;EACA,IAAID,KAAK,GAAGjC,IAAI,CAAChB,GAAG,CAACuB,SAAS,EAAE,CAAC,CAAC;EAClC,IAAIC,IAAI,GAAGrB,aAAa,CAAC,IAAId,OAAO,CAAC8D,MAAM,CAAC,CAACT,GAAG,CAACQ,MAAM,CAAC,CAACrC,GAAG,CAACoC,KAAK,GAAG,CAAC,CAAC,EAAE5C,aAAa,EAAE,CAAC,CAAC;EAC1F,IAAIiD,MAAM,GAAG,CAAC,GAAG1D,SAAS,CAAC,IAAIP,OAAO,CAAC6D,MAAM,CAAC,EAAE,IAAI7D,OAAO,CAAC8D,MAAM,CAAC,CAACT,GAAG,CAAC,IAAIrD,OAAO,CAAC,IAAI,CAAC,CAAC8B,GAAG,CAACK,IAAI,CAAC,CAAC,EAAEA,IAAI,CAAC,EAAE2B,MAAM,CAAC;EACpH,OAAOpD,GAAG,GAAGC,GAAG,GAAGN,OAAO,CAAC4D,MAAM,CAAC,GAAGA,MAAM;AAC7C;AACA,OAAO,IAAIG,iBAAiB,GAAGjE,OAAO,CAACuD,mBAAmB,CAAC;AAC3D,OAAO,IAAIW,wBAAwB,GAAGlE,OAAO,CAAC+D,0BAA0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}