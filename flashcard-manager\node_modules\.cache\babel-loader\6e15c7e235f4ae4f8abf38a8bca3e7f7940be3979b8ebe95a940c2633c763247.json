{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst uniqBy$1 = require('../../array/uniqBy.js');\nconst identity = require('../../function/identity.js');\nconst isArrayLikeObject = require('../predicate/isArrayLikeObject.js');\nconst iteratee = require('../util/iteratee.js');\nfunction uniqBy(array, iteratee$1 = identity.identity) {\n  if (!isArrayLikeObject.isArrayLikeObject(array)) {\n    return [];\n  }\n  return uniqBy$1.uniqBy(Array.from(array), iteratee.iteratee(iteratee$1));\n}\nexports.uniqBy = uniqBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "uniqBy$1", "require", "identity", "isArrayLikeObject", "iteratee", "uniqBy", "array", "iteratee$1", "Array", "from"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/dist/compat/array/uniqBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst uniqBy$1 = require('../../array/uniqBy.js');\nconst identity = require('../../function/identity.js');\nconst isArrayLikeObject = require('../predicate/isArrayLikeObject.js');\nconst iteratee = require('../util/iteratee.js');\n\nfunction uniqBy(array, iteratee$1 = identity.identity) {\n    if (!isArrayLikeObject.isArrayLikeObject(array)) {\n        return [];\n    }\n    return uniqBy$1.uniqBy(Array.from(array), iteratee.iteratee(iteratee$1));\n}\n\nexports.uniqBy = uniqBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AACjD,MAAMC,QAAQ,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AACtD,MAAME,iBAAiB,GAAGF,OAAO,CAAC,mCAAmC,CAAC;AACtE,MAAMG,QAAQ,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAE/C,SAASI,MAAMA,CAACC,KAAK,EAAEC,UAAU,GAAGL,QAAQ,CAACA,QAAQ,EAAE;EACnD,IAAI,CAACC,iBAAiB,CAACA,iBAAiB,CAACG,KAAK,CAAC,EAAE;IAC7C,OAAO,EAAE;EACb;EACA,OAAON,QAAQ,CAACK,MAAM,CAACG,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,EAAEF,QAAQ,CAACA,QAAQ,CAACG,UAAU,CAAC,CAAC;AAC5E;AAEAX,OAAO,CAACS,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}