{"ast": null, "code": "import { isNan } from '../../../util/DataUtils';\nexport var combineActiveLabel = (tooltipTicks, activeIndex) => {\n  var _tooltipTicks$n;\n  var n = Number(activeIndex);\n  if (isNan(n) || activeIndex == null) {\n    return undefined;\n  }\n  return n >= 0 ? tooltipTicks === null || tooltipTicks === void 0 || (_tooltipTicks$n = tooltipTicks[n]) === null || _tooltipTicks$n === void 0 ? void 0 : _tooltipTicks$n.value : undefined;\n};", "map": {"version": 3, "names": ["isNan", "combineActiveLabel", "tooltipTicks", "activeIndex", "_tooltipTicks$n", "n", "Number", "undefined", "value"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/selectors/combiners/combineActiveLabel.js"], "sourcesContent": ["import { isNan } from '../../../util/DataUtils';\nexport var combineActiveLabel = (tooltipTicks, activeIndex) => {\n  var _tooltipTicks$n;\n  var n = Number(activeIndex);\n  if (isNan(n) || activeIndex == null) {\n    return undefined;\n  }\n  return n >= 0 ? tooltipTicks === null || tooltipTicks === void 0 || (_tooltipTicks$n = tooltipTicks[n]) === null || _tooltipTicks$n === void 0 ? void 0 : _tooltipTicks$n.value : undefined;\n};"], "mappings": "AAAA,SAASA,KAAK,QAAQ,yBAAyB;AAC/C,OAAO,IAAIC,kBAAkB,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;EAC7D,IAAIC,eAAe;EACnB,IAAIC,CAAC,GAAGC,MAAM,CAACH,WAAW,CAAC;EAC3B,IAAIH,KAAK,CAACK,CAAC,CAAC,IAAIF,WAAW,IAAI,IAAI,EAAE;IACnC,OAAOI,SAAS;EAClB;EACA,OAAOF,CAAC,IAAI,CAAC,GAAGH,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAI,CAACE,eAAe,GAAGF,YAAY,CAACG,CAAC,CAAC,MAAM,IAAI,IAAID,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACI,KAAK,GAAGD,SAAS;AAC7L,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}