{"ast": null, "code": "import { useEffect } from 'react';\nimport { updateOptions } from './rootPropsSlice';\nimport { useAppDispatch } from './hooks';\nexport function ReportChartProps(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(updateOptions(props));\n  }, [dispatch, props]);\n  return null;\n}", "map": {"version": 3, "names": ["useEffect", "updateOptions", "useAppDispatch", "ReportChartProps", "props", "dispatch"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/ReportChartProps.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { updateOptions } from './rootPropsSlice';\nimport { useAppDispatch } from './hooks';\nexport function ReportChartProps(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(updateOptions(props));\n  }, [dispatch, props]);\n  return null;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,SAAS;AACxC,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,IAAIC,QAAQ,GAAGH,cAAc,CAAC,CAAC;EAC/BF,SAAS,CAAC,MAAM;IACdK,QAAQ,CAACJ,aAAa,CAACG,KAAK,CAAC,CAAC;EAChC,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}