{"ast": null, "code": "module.exports = require('../dist/compat/math/maxBy.js').maxBy;", "map": {"version": 3, "names": ["module", "exports", "require", "maxBy"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/compat/maxBy.js"], "sourcesContent": ["module.exports = require('../dist/compat/math/maxBy.js').maxBy;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,8BAA8B,CAAC,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}