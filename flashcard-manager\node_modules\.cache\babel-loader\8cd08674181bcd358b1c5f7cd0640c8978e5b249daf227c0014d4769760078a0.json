{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { mathSign, isNumber } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { isVisible, getTickBoundaries, getNumberIntervalTicks, getAngledTickWidth } from '../util/TickUtils';\nimport { getEquidistantTicks } from './getEquidistantTicks';\nfunction getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap) {\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var {\n    start\n  } = boundaries;\n  var {\n    end\n  } = boundaries;\n  var _loop = function _loop(i) {\n    var entry = result[i];\n    var size;\n    var getSize = () => {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === len - 1) {\n      var gap = sign * (entry.coordinate + sign * getSize() / 2 - end);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap > 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      end = entry.tickCoord - sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = len - 1; i >= 0; i--) {\n    _loop(i);\n  }\n  return result;\n}\nfunction getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, preserveEnd) {\n  // This method is mutating the array so clone is indeed necessary here\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var {\n    start,\n    end\n  } = boundaries;\n  if (preserveEnd) {\n    // Try to guarantee the tail to be displayed\n    var tail = ticks[len - 1];\n    var tailSize = getTickSize(tail, len - 1);\n    var tailGap = sign * (tail.coordinate + sign * tailSize / 2 - end);\n    result[len - 1] = tail = _objectSpread(_objectSpread({}, tail), {}, {\n      tickCoord: tailGap > 0 ? tail.coordinate - tailGap * sign : tail.coordinate\n    });\n    var isTailShow = isVisible(sign, tail.tickCoord, () => tailSize, start, end);\n    if (isTailShow) {\n      end = tail.tickCoord - sign * (tailSize / 2 + minTickGap);\n      result[len - 1] = _objectSpread(_objectSpread({}, tail), {}, {\n        isShow: true\n      });\n    }\n  }\n  var count = preserveEnd ? len - 1 : len;\n  var _loop2 = function _loop2(i) {\n    var entry = result[i];\n    var size;\n    var getSize = () => {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === 0) {\n      var gap = sign * (entry.coordinate - sign * getSize() / 2 - start);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap < 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      start = entry.tickCoord + sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = 0; i < count; i++) {\n    _loop2(i);\n  }\n  return result;\n}\nexport function getTicks(props, fontSize, letterSpacing) {\n  var {\n    tick,\n    ticks,\n    viewBox,\n    minTickGap,\n    orientation,\n    interval,\n    tickFormatter,\n    unit,\n    angle\n  } = props;\n  if (!ticks || !ticks.length || !tick) {\n    return [];\n  }\n  if (isNumber(interval) || Global.isSsr) {\n    var _getNumberIntervalTic;\n    return (_getNumberIntervalTic = getNumberIntervalTicks(ticks, isNumber(interval) ? interval : 0)) !== null && _getNumberIntervalTic !== void 0 ? _getNumberIntervalTic : [];\n  }\n  var candidates = [];\n  var sizeKey = orientation === 'top' || orientation === 'bottom' ? 'width' : 'height';\n  var unitSize = unit && sizeKey === 'width' ? getStringSize(unit, {\n    fontSize,\n    letterSpacing\n  }) : {\n    width: 0,\n    height: 0\n  };\n  var getTickSize = (content, index) => {\n    var value = typeof tickFormatter === 'function' ? tickFormatter(content.value, index) : content.value;\n    // Recharts only supports angles when sizeKey === 'width'\n    return sizeKey === 'width' ? getAngledTickWidth(getStringSize(value, {\n      fontSize,\n      letterSpacing\n    }), unitSize, angle) : getStringSize(value, {\n      fontSize,\n      letterSpacing\n    })[sizeKey];\n  };\n  var sign = ticks.length >= 2 ? mathSign(ticks[1].coordinate - ticks[0].coordinate) : 1;\n  var boundaries = getTickBoundaries(viewBox, sign, sizeKey);\n  if (interval === 'equidistantPreserveStart') {\n    return getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  if (interval === 'preserveStart' || interval === 'preserveStartEnd') {\n    candidates = getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, interval === 'preserveStartEnd');\n  } else {\n    candidates = getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  return candidates.filter(entry => entry.isShow);\n}", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "mathSign", "isNumber", "getStringSize", "Global", "isVisible", "getTickBoundaries", "getNumberIntervalTicks", "getAngledTickWidth", "getEquidistantTicks", "getTicksEnd", "sign", "boundaries", "getTickSize", "ticks", "minTickGap", "result", "slice", "len", "start", "end", "_loop", "entry", "size", "getSize", "undefined", "gap", "coordinate", "tickCoord", "isShow", "getTicksStart", "preserveEnd", "tail", "tailSize", "tailGap", "isTailShow", "count", "_loop2", "getTicks", "props", "fontSize", "letterSpacing", "tick", "viewBox", "orientation", "interval", "tick<PERSON><PERSON><PERSON><PERSON>", "unit", "angle", "isSsr", "_getNumberIntervalTic", "candidates", "sizeKey", "unitSize", "width", "height", "content", "index"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/cartesian/getTicks.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { mathSign, isNumber } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { isVisible, getTickBoundaries, getNumberIntervalTicks, getAngledTickWidth } from '../util/TickUtils';\nimport { getEquidistantTicks } from './getEquidistantTicks';\nfunction getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap) {\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var {\n    start\n  } = boundaries;\n  var {\n    end\n  } = boundaries;\n  var _loop = function _loop(i) {\n    var entry = result[i];\n    var size;\n    var getSize = () => {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === len - 1) {\n      var gap = sign * (entry.coordinate + sign * getSize() / 2 - end);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap > 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      end = entry.tickCoord - sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = len - 1; i >= 0; i--) {\n    _loop(i);\n  }\n  return result;\n}\nfunction getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, preserveEnd) {\n  // This method is mutating the array so clone is indeed necessary here\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var {\n    start,\n    end\n  } = boundaries;\n  if (preserveEnd) {\n    // Try to guarantee the tail to be displayed\n    var tail = ticks[len - 1];\n    var tailSize = getTickSize(tail, len - 1);\n    var tailGap = sign * (tail.coordinate + sign * tailSize / 2 - end);\n    result[len - 1] = tail = _objectSpread(_objectSpread({}, tail), {}, {\n      tickCoord: tailGap > 0 ? tail.coordinate - tailGap * sign : tail.coordinate\n    });\n    var isTailShow = isVisible(sign, tail.tickCoord, () => tailSize, start, end);\n    if (isTailShow) {\n      end = tail.tickCoord - sign * (tailSize / 2 + minTickGap);\n      result[len - 1] = _objectSpread(_objectSpread({}, tail), {}, {\n        isShow: true\n      });\n    }\n  }\n  var count = preserveEnd ? len - 1 : len;\n  var _loop2 = function _loop2(i) {\n    var entry = result[i];\n    var size;\n    var getSize = () => {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === 0) {\n      var gap = sign * (entry.coordinate - sign * getSize() / 2 - start);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap < 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      start = entry.tickCoord + sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = 0; i < count; i++) {\n    _loop2(i);\n  }\n  return result;\n}\nexport function getTicks(props, fontSize, letterSpacing) {\n  var {\n    tick,\n    ticks,\n    viewBox,\n    minTickGap,\n    orientation,\n    interval,\n    tickFormatter,\n    unit,\n    angle\n  } = props;\n  if (!ticks || !ticks.length || !tick) {\n    return [];\n  }\n  if (isNumber(interval) || Global.isSsr) {\n    var _getNumberIntervalTic;\n    return (_getNumberIntervalTic = getNumberIntervalTicks(ticks, isNumber(interval) ? interval : 0)) !== null && _getNumberIntervalTic !== void 0 ? _getNumberIntervalTic : [];\n  }\n  var candidates = [];\n  var sizeKey = orientation === 'top' || orientation === 'bottom' ? 'width' : 'height';\n  var unitSize = unit && sizeKey === 'width' ? getStringSize(unit, {\n    fontSize,\n    letterSpacing\n  }) : {\n    width: 0,\n    height: 0\n  };\n  var getTickSize = (content, index) => {\n    var value = typeof tickFormatter === 'function' ? tickFormatter(content.value, index) : content.value;\n    // Recharts only supports angles when sizeKey === 'width'\n    return sizeKey === 'width' ? getAngledTickWidth(getStringSize(value, {\n      fontSize,\n      letterSpacing\n    }), unitSize, angle) : getStringSize(value, {\n      fontSize,\n      letterSpacing\n    })[sizeKey];\n  };\n  var sign = ticks.length >= 2 ? mathSign(ticks[1].coordinate - ticks[0].coordinate) : 1;\n  var boundaries = getTickBoundaries(viewBox, sign, sizeKey);\n  if (interval === 'equidistantPreserveStart') {\n    return getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  if (interval === 'preserveStart' || interval === 'preserveStartEnd') {\n    candidates = getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, interval === 'preserveStartEnd');\n  } else {\n    candidates = getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  return candidates.filter(entry => entry.isShow);\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,kBAAkB,QAAQ,mBAAmB;AAC5G,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,WAAWA,CAACC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACrE,IAAIC,MAAM,GAAG,CAACF,KAAK,IAAI,EAAE,EAAEG,KAAK,CAAC,CAAC;EAClC,IAAIC,GAAG,GAAGF,MAAM,CAACjC,MAAM;EACvB,IAAI;IACFoC;EACF,CAAC,GAAGP,UAAU;EACd,IAAI;IACFQ;EACF,CAAC,GAAGR,UAAU;EACd,IAAIS,KAAK,GAAG,SAASA,KAAKA,CAAC5B,CAAC,EAAE;IAC5B,IAAI6B,KAAK,GAAGN,MAAM,CAACvB,CAAC,CAAC;IACrB,IAAI8B,IAAI;IACR,IAAIC,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAID,IAAI,KAAKE,SAAS,EAAE;QACtBF,IAAI,GAAGV,WAAW,CAACS,KAAK,EAAE7B,CAAC,CAAC;MAC9B;MACA,OAAO8B,IAAI;IACb,CAAC;IACD,IAAI9B,CAAC,KAAKyB,GAAG,GAAG,CAAC,EAAE;MACjB,IAAIQ,GAAG,GAAGf,IAAI,IAAIW,KAAK,CAACK,UAAU,GAAGhB,IAAI,GAAGa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGJ,GAAG,CAAC;MAChEJ,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAGzC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEF,GAAG,GAAG,CAAC,GAAGJ,KAAK,CAACK,UAAU,GAAGD,GAAG,GAAGf,IAAI,GAAGW,KAAK,CAACK;MAC7D,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAGzC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEN,KAAK,CAACK;MACnB,CAAC,CAAC;IACJ;IACA,IAAIE,MAAM,GAAGxB,SAAS,CAACM,IAAI,EAAEW,KAAK,CAACM,SAAS,EAAEJ,OAAO,EAAEL,KAAK,EAAEC,GAAG,CAAC;IAClE,IAAIS,MAAM,EAAE;MACVT,GAAG,GAAGE,KAAK,CAACM,SAAS,GAAGjB,IAAI,IAAIa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGT,UAAU,CAAC;MAC3DC,MAAM,CAACvB,CAAC,CAAC,GAAGZ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACtDO,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,KAAK,IAAIpC,CAAC,GAAGyB,GAAG,GAAG,CAAC,EAAEzB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACjC4B,KAAK,CAAC5B,CAAC,CAAC;EACV;EACA,OAAOuB,MAAM;AACf;AACA,SAASc,aAAaA,CAACnB,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAEgB,WAAW,EAAE;EACpF;EACA,IAAIf,MAAM,GAAG,CAACF,KAAK,IAAI,EAAE,EAAEG,KAAK,CAAC,CAAC;EAClC,IAAIC,GAAG,GAAGF,MAAM,CAACjC,MAAM;EACvB,IAAI;IACFoC,KAAK;IACLC;EACF,CAAC,GAAGR,UAAU;EACd,IAAImB,WAAW,EAAE;IACf;IACA,IAAIC,IAAI,GAAGlB,KAAK,CAACI,GAAG,GAAG,CAAC,CAAC;IACzB,IAAIe,QAAQ,GAAGpB,WAAW,CAACmB,IAAI,EAAEd,GAAG,GAAG,CAAC,CAAC;IACzC,IAAIgB,OAAO,GAAGvB,IAAI,IAAIqB,IAAI,CAACL,UAAU,GAAGhB,IAAI,GAAGsB,QAAQ,GAAG,CAAC,GAAGb,GAAG,CAAC;IAClEJ,MAAM,CAACE,GAAG,GAAG,CAAC,CAAC,GAAGc,IAAI,GAAGnD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAClEJ,SAAS,EAAEM,OAAO,GAAG,CAAC,GAAGF,IAAI,CAACL,UAAU,GAAGO,OAAO,GAAGvB,IAAI,GAAGqB,IAAI,CAACL;IACnE,CAAC,CAAC;IACF,IAAIQ,UAAU,GAAG9B,SAAS,CAACM,IAAI,EAAEqB,IAAI,CAACJ,SAAS,EAAE,MAAMK,QAAQ,EAAEd,KAAK,EAAEC,GAAG,CAAC;IAC5E,IAAIe,UAAU,EAAE;MACdf,GAAG,GAAGY,IAAI,CAACJ,SAAS,GAAGjB,IAAI,IAAIsB,QAAQ,GAAG,CAAC,GAAGlB,UAAU,CAAC;MACzDC,MAAM,CAACE,GAAG,GAAG,CAAC,CAAC,GAAGrC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DH,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF;EACA,IAAIO,KAAK,GAAGL,WAAW,GAAGb,GAAG,GAAG,CAAC,GAAGA,GAAG;EACvC,IAAImB,MAAM,GAAG,SAASA,MAAMA,CAAC5C,CAAC,EAAE;IAC9B,IAAI6B,KAAK,GAAGN,MAAM,CAACvB,CAAC,CAAC;IACrB,IAAI8B,IAAI;IACR,IAAIC,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAID,IAAI,KAAKE,SAAS,EAAE;QACtBF,IAAI,GAAGV,WAAW,CAACS,KAAK,EAAE7B,CAAC,CAAC;MAC9B;MACA,OAAO8B,IAAI;IACb,CAAC;IACD,IAAI9B,CAAC,KAAK,CAAC,EAAE;MACX,IAAIiC,GAAG,GAAGf,IAAI,IAAIW,KAAK,CAACK,UAAU,GAAGhB,IAAI,GAAGa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGL,KAAK,CAAC;MAClEH,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAGzC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEF,GAAG,GAAG,CAAC,GAAGJ,KAAK,CAACK,UAAU,GAAGD,GAAG,GAAGf,IAAI,GAAGW,KAAK,CAACK;MAC7D,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAGzC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEN,KAAK,CAACK;MACnB,CAAC,CAAC;IACJ;IACA,IAAIE,MAAM,GAAGxB,SAAS,CAACM,IAAI,EAAEW,KAAK,CAACM,SAAS,EAAEJ,OAAO,EAAEL,KAAK,EAAEC,GAAG,CAAC;IAClE,IAAIS,MAAM,EAAE;MACVV,KAAK,GAAGG,KAAK,CAACM,SAAS,GAAGjB,IAAI,IAAIa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGT,UAAU,CAAC;MAC7DC,MAAM,CAACvB,CAAC,CAAC,GAAGZ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACtDO,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,KAAK,EAAE3C,CAAC,EAAE,EAAE;IAC9B4C,MAAM,CAAC5C,CAAC,CAAC;EACX;EACA,OAAOuB,MAAM;AACf;AACA,OAAO,SAASsB,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EACvD,IAAI;IACFC,IAAI;IACJ5B,KAAK;IACL6B,OAAO;IACP5B,UAAU;IACV6B,WAAW;IACXC,QAAQ;IACRC,aAAa;IACbC,IAAI;IACJC;EACF,CAAC,GAAGT,KAAK;EACT,IAAI,CAACzB,KAAK,IAAI,CAACA,KAAK,CAAC/B,MAAM,IAAI,CAAC2D,IAAI,EAAE;IACpC,OAAO,EAAE;EACX;EACA,IAAIxC,QAAQ,CAAC2C,QAAQ,CAAC,IAAIzC,MAAM,CAAC6C,KAAK,EAAE;IACtC,IAAIC,qBAAqB;IACzB,OAAO,CAACA,qBAAqB,GAAG3C,sBAAsB,CAACO,KAAK,EAAEZ,QAAQ,CAAC2C,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIK,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE;EAC7K;EACA,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,OAAO,GAAGR,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ,GAAG,OAAO,GAAG,QAAQ;EACpF,IAAIS,QAAQ,GAAGN,IAAI,IAAIK,OAAO,KAAK,OAAO,GAAGjD,aAAa,CAAC4C,IAAI,EAAE;IAC/DP,QAAQ;IACRC;EACF,CAAC,CAAC,GAAG;IACHa,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACD,IAAI1C,WAAW,GAAGA,CAAC2C,OAAO,EAAEC,KAAK,KAAK;IACpC,IAAInE,KAAK,GAAG,OAAOwD,aAAa,KAAK,UAAU,GAAGA,aAAa,CAACU,OAAO,CAAClE,KAAK,EAAEmE,KAAK,CAAC,GAAGD,OAAO,CAAClE,KAAK;IACrG;IACA,OAAO8D,OAAO,KAAK,OAAO,GAAG5C,kBAAkB,CAACL,aAAa,CAACb,KAAK,EAAE;MACnEkD,QAAQ;MACRC;IACF,CAAC,CAAC,EAAEY,QAAQ,EAAEL,KAAK,CAAC,GAAG7C,aAAa,CAACb,KAAK,EAAE;MAC1CkD,QAAQ;MACRC;IACF,CAAC,CAAC,CAACW,OAAO,CAAC;EACb,CAAC;EACD,IAAIzC,IAAI,GAAGG,KAAK,CAAC/B,MAAM,IAAI,CAAC,GAAGkB,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC,CAACa,UAAU,GAAGb,KAAK,CAAC,CAAC,CAAC,CAACa,UAAU,CAAC,GAAG,CAAC;EACtF,IAAIf,UAAU,GAAGN,iBAAiB,CAACqC,OAAO,EAAEhC,IAAI,EAAEyC,OAAO,CAAC;EAC1D,IAAIP,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,OAAOpC,mBAAmB,CAACE,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,CAAC;EAC9E;EACA,IAAI8B,QAAQ,KAAK,eAAe,IAAIA,QAAQ,KAAK,kBAAkB,EAAE;IACnEM,UAAU,GAAGrB,aAAa,CAACnB,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE8B,QAAQ,KAAK,kBAAkB,CAAC;EAC/G,CAAC,MAAM;IACLM,UAAU,GAAGzC,WAAW,CAACC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,CAAC;EAC5E;EACA,OAAOoC,UAAU,CAAC3E,MAAM,CAAC8C,KAAK,IAAIA,KAAK,CAACO,MAAM,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}