{"ast": null, "code": "import { useEffect } from 'react';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { setChartSize, setMargin } from '../state/layoutSlice';\nimport { selectChartOffset, selectChartViewBox } from '../state/selectors/selectChartOffset';\nimport { selectChartHeight, selectChartWidth } from '../state/selectors/containerSelectors';\nimport { useIsPanorama } from './PanoramaContext';\nimport { selectBrushDimensions, selectBrushSettings } from '../state/selectors/brushSelectors';\nexport var useViewBox = () => {\n  var _useAppSelector;\n  var panorama = useIsPanorama();\n  var rootViewBox = useAppSelector(selectChartViewBox);\n  var brushDimensions = useAppSelector(selectBrushDimensions);\n  var brushPadding = (_useAppSelector = useAppSelector(selectBrushSettings)) === null || _useAppSelector === void 0 ? void 0 : _useAppSelector.padding;\n  if (!panorama || !brushDimensions || !brushPadding) {\n    return rootViewBox;\n  }\n  return {\n    width: brushDimensions.width - brushPadding.left - brushPadding.right,\n    height: brushDimensions.height - brushPadding.top - brushPadding.bottom,\n    x: brushPadding.left,\n    y: brushPadding.top\n  };\n};\nvar manyComponentsThrowErrorsIfOffsetIsUndefined = {\n  top: 0,\n  bottom: 0,\n  left: 0,\n  right: 0,\n  width: 0,\n  height: 0,\n  brushBottom: 0\n};\nexport var useOffset = () => {\n  var _useAppSelector2;\n  return (_useAppSelector2 = useAppSelector(selectChartOffset)) !== null && _useAppSelector2 !== void 0 ? _useAppSelector2 : manyComponentsThrowErrorsIfOffsetIsUndefined;\n};\n\n/**\n * Returns the width of the chart in pixels.\n *\n * If you are using chart with hardcoded `width` prop, then the width returned will be the same\n * as the `width` prop on the main chart element.\n *\n * If you are using a chart with a `ResponsiveContainer`, the width will be the size of the chart\n * as the ResponsiveContainer has decided it would be.\n *\n * If the chart has any axes or legend, the `width` will be the size of the chart\n * including the axes and legend. Meaning: adding axes and legend will not change the width.\n *\n * The dimensions do not scale, meaning as user zoom in and out, the width number will not change\n * as the chart gets visually larger or smaller.\n *\n * Returns `undefined` if used outside a chart context.\n *\n * @returns {number | undefined} The width of the chart in pixels, or `undefined` if not in a chart context.\n */\nexport var useChartWidth = () => {\n  return useAppSelector(selectChartWidth);\n};\n\n/**\n * Returns the height of the chart in pixels.\n *\n * If you are using chart with hardcoded `height` props, then the height returned will be the same\n * as the `height` prop on the main chart element.\n *\n * If you are using a chart with a `ResponsiveContainer`, the height will be the size of the chart\n * as the ResponsiveContainer has decided it would be.\n *\n * If the chart has any axes or legend, the `height` will be the size of the chart\n * including the axes and legend. Meaning: adding axes and legend will not change the height.\n *\n * The dimensions do not scale, meaning as user zoom in and out, the height number will not change\n * as the chart gets visually larger or smaller.\n *\n * Returns `undefined` if used outside a chart context.\n *\n * @returns {number | undefined} The height of the chart in pixels, or `undefined` if not in a chart context.\n */\nexport var useChartHeight = () => {\n  return useAppSelector(selectChartHeight);\n};\nvar manyComponentsThrowErrorsIfMarginIsUndefined = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nexport var useMargin = () => {\n  var _useAppSelector3;\n  return (_useAppSelector3 = useAppSelector(state => state.layout.margin)) !== null && _useAppSelector3 !== void 0 ? _useAppSelector3 : manyComponentsThrowErrorsIfMarginIsUndefined;\n};\nexport var selectChartLayout = state => state.layout.layoutType;\nexport var useChartLayout = () => useAppSelector(selectChartLayout);\nexport var ReportChartSize = props => {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setChartSize(props));\n  }, [dispatch, props]);\n  return null;\n};\nexport var ReportChartMargin = _ref => {\n  var {\n    margin\n  } = _ref;\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setMargin(margin));\n  }, [dispatch, margin]);\n  return null;\n};", "map": {"version": 3, "names": ["useEffect", "useAppDispatch", "useAppSelector", "setChartSize", "<PERSON><PERSON><PERSON><PERSON>", "selectChartOffset", "selectChartViewBox", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useIsPanorama", "selectBrushDimensions", "selectBrushSettings", "useViewBox", "_useAppSelector", "panorama", "rootViewBox", "brushDimensions", "brushPadding", "padding", "width", "left", "right", "height", "top", "bottom", "x", "y", "manyComponentsThrowErrorsIfOffsetIsUndefined", "brushBottom", "useOffset", "_useAppSelector2", "<PERSON><PERSON><PERSON><PERSON><PERSON>th", "useChartHeight", "manyComponentsThrowErrorsIfMarginIsUndefined", "<PERSON><PERSON><PERSON><PERSON>", "_useAppSelector3", "state", "layout", "margin", "selectChartLayout", "layoutType", "useChartLayout", "ReportChartSize", "props", "dispatch", "ReportChart<PERSON><PERSON><PERSON>", "_ref"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/context/chartLayoutContext.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { setChartSize, setMargin } from '../state/layoutSlice';\nimport { selectChartOffset, selectChartViewBox } from '../state/selectors/selectChartOffset';\nimport { selectChartHeight, selectChartWidth } from '../state/selectors/containerSelectors';\nimport { useIsPanorama } from './PanoramaContext';\nimport { selectBrushDimensions, selectBrushSettings } from '../state/selectors/brushSelectors';\nexport var useViewBox = () => {\n  var _useAppSelector;\n  var panorama = useIsPanorama();\n  var rootViewBox = useAppSelector(selectChartViewBox);\n  var brushDimensions = useAppSelector(selectBrushDimensions);\n  var brushPadding = (_useAppSelector = useAppSelector(selectBrushSettings)) === null || _useAppSelector === void 0 ? void 0 : _useAppSelector.padding;\n  if (!panorama || !brushDimensions || !brushPadding) {\n    return rootViewBox;\n  }\n  return {\n    width: brushDimensions.width - brushPadding.left - brushPadding.right,\n    height: brushDimensions.height - brushPadding.top - brushPadding.bottom,\n    x: brushPadding.left,\n    y: brushPadding.top\n  };\n};\nvar manyComponentsThrowErrorsIfOffsetIsUndefined = {\n  top: 0,\n  bottom: 0,\n  left: 0,\n  right: 0,\n  width: 0,\n  height: 0,\n  brushBottom: 0\n};\nexport var useOffset = () => {\n  var _useAppSelector2;\n  return (_useAppSelector2 = useAppSelector(selectChartOffset)) !== null && _useAppSelector2 !== void 0 ? _useAppSelector2 : manyComponentsThrowErrorsIfOffsetIsUndefined;\n};\n\n/**\n * Returns the width of the chart in pixels.\n *\n * If you are using chart with hardcoded `width` prop, then the width returned will be the same\n * as the `width` prop on the main chart element.\n *\n * If you are using a chart with a `ResponsiveContainer`, the width will be the size of the chart\n * as the ResponsiveContainer has decided it would be.\n *\n * If the chart has any axes or legend, the `width` will be the size of the chart\n * including the axes and legend. Meaning: adding axes and legend will not change the width.\n *\n * The dimensions do not scale, meaning as user zoom in and out, the width number will not change\n * as the chart gets visually larger or smaller.\n *\n * Returns `undefined` if used outside a chart context.\n *\n * @returns {number | undefined} The width of the chart in pixels, or `undefined` if not in a chart context.\n */\nexport var useChartWidth = () => {\n  return useAppSelector(selectChartWidth);\n};\n\n/**\n * Returns the height of the chart in pixels.\n *\n * If you are using chart with hardcoded `height` props, then the height returned will be the same\n * as the `height` prop on the main chart element.\n *\n * If you are using a chart with a `ResponsiveContainer`, the height will be the size of the chart\n * as the ResponsiveContainer has decided it would be.\n *\n * If the chart has any axes or legend, the `height` will be the size of the chart\n * including the axes and legend. Meaning: adding axes and legend will not change the height.\n *\n * The dimensions do not scale, meaning as user zoom in and out, the height number will not change\n * as the chart gets visually larger or smaller.\n *\n * Returns `undefined` if used outside a chart context.\n *\n * @returns {number | undefined} The height of the chart in pixels, or `undefined` if not in a chart context.\n */\nexport var useChartHeight = () => {\n  return useAppSelector(selectChartHeight);\n};\nvar manyComponentsThrowErrorsIfMarginIsUndefined = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nexport var useMargin = () => {\n  var _useAppSelector3;\n  return (_useAppSelector3 = useAppSelector(state => state.layout.margin)) !== null && _useAppSelector3 !== void 0 ? _useAppSelector3 : manyComponentsThrowErrorsIfMarginIsUndefined;\n};\nexport var selectChartLayout = state => state.layout.layoutType;\nexport var useChartLayout = () => useAppSelector(selectChartLayout);\nexport var ReportChartSize = props => {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setChartSize(props));\n  }, [dispatch, props]);\n  return null;\n};\nexport var ReportChartMargin = _ref => {\n  var {\n    margin\n  } = _ref;\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setMargin(margin));\n  }, [dispatch, margin]);\n  return null;\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,SAASC,iBAAiB,EAAEC,kBAAkB,QAAQ,sCAAsC;AAC5F,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,uCAAuC;AAC3F,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,qBAAqB,EAAEC,mBAAmB,QAAQ,mCAAmC;AAC9F,OAAO,IAAIC,UAAU,GAAGA,CAAA,KAAM;EAC5B,IAAIC,eAAe;EACnB,IAAIC,QAAQ,GAAGL,aAAa,CAAC,CAAC;EAC9B,IAAIM,WAAW,GAAGb,cAAc,CAACI,kBAAkB,CAAC;EACpD,IAAIU,eAAe,GAAGd,cAAc,CAACQ,qBAAqB,CAAC;EAC3D,IAAIO,YAAY,GAAG,CAACJ,eAAe,GAAGX,cAAc,CAACS,mBAAmB,CAAC,MAAM,IAAI,IAAIE,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACK,OAAO;EACpJ,IAAI,CAACJ,QAAQ,IAAI,CAACE,eAAe,IAAI,CAACC,YAAY,EAAE;IAClD,OAAOF,WAAW;EACpB;EACA,OAAO;IACLI,KAAK,EAAEH,eAAe,CAACG,KAAK,GAAGF,YAAY,CAACG,IAAI,GAAGH,YAAY,CAACI,KAAK;IACrEC,MAAM,EAAEN,eAAe,CAACM,MAAM,GAAGL,YAAY,CAACM,GAAG,GAAGN,YAAY,CAACO,MAAM;IACvEC,CAAC,EAAER,YAAY,CAACG,IAAI;IACpBM,CAAC,EAAET,YAAY,CAACM;EAClB,CAAC;AACH,CAAC;AACD,IAAII,4CAA4C,GAAG;EACjDJ,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE,CAAC;EACTJ,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRF,KAAK,EAAE,CAAC;EACRG,MAAM,EAAE,CAAC;EACTM,WAAW,EAAE;AACf,CAAC;AACD,OAAO,IAAIC,SAAS,GAAGA,CAAA,KAAM;EAC3B,IAAIC,gBAAgB;EACpB,OAAO,CAACA,gBAAgB,GAAG5B,cAAc,CAACG,iBAAiB,CAAC,MAAM,IAAI,IAAIyB,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGH,4CAA4C;AACzK,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,aAAa,GAAGA,CAAA,KAAM;EAC/B,OAAO7B,cAAc,CAACM,gBAAgB,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIwB,cAAc,GAAGA,CAAA,KAAM;EAChC,OAAO9B,cAAc,CAACK,iBAAiB,CAAC;AAC1C,CAAC;AACD,IAAI0B,4CAA4C,GAAG;EACjDV,GAAG,EAAE,CAAC;EACNF,KAAK,EAAE,CAAC;EACRG,MAAM,EAAE,CAAC;EACTJ,IAAI,EAAE;AACR,CAAC;AACD,OAAO,IAAIc,SAAS,GAAGA,CAAA,KAAM;EAC3B,IAAIC,gBAAgB;EACpB,OAAO,CAACA,gBAAgB,GAAGjC,cAAc,CAACkC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACC,MAAM,CAAC,MAAM,IAAI,IAAIH,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGF,4CAA4C;AACpL,CAAC;AACD,OAAO,IAAIM,iBAAiB,GAAGH,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACG,UAAU;AAC/D,OAAO,IAAIC,cAAc,GAAGA,CAAA,KAAMvC,cAAc,CAACqC,iBAAiB,CAAC;AACnE,OAAO,IAAIG,eAAe,GAAGC,KAAK,IAAI;EACpC,IAAIC,QAAQ,GAAG3C,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACd4C,QAAQ,CAACzC,YAAY,CAACwC,KAAK,CAAC,CAAC;EAC/B,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIE,iBAAiB,GAAGC,IAAI,IAAI;EACrC,IAAI;IACFR;EACF,CAAC,GAAGQ,IAAI;EACR,IAAIF,QAAQ,GAAG3C,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACd4C,QAAQ,CAACxC,SAAS,CAACkC,MAAM,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACM,QAAQ,EAAEN,MAAM,CAAC,CAAC;EACtB,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}