{"ast": null, "code": "import { getAngledRectangleWidth } from './CartesianUtils';\nimport { getEveryNthWithCondition } from './getEveryNthWithCondition';\nexport function getAngledTickWidth(contentSize, unitSize, angle) {\n  var size = {\n    width: contentSize.width + unitSize.width,\n    height: contentSize.height + unitSize.height\n  };\n  return getAngledRectangleWidth(size, angle);\n}\nexport function getTickBoundaries(viewBox, sign, sizeKey) {\n  var isWidth = sizeKey === 'width';\n  var {\n    x,\n    y,\n    width,\n    height\n  } = viewBox;\n  if (sign === 1) {\n    return {\n      start: isWidth ? x : y,\n      end: isWidth ? x + width : y + height\n    };\n  }\n  return {\n    start: isWidth ? x + width : y + height,\n    end: isWidth ? x : y\n  };\n}\nexport function isVisible(sign, tickPosition, getSize, start, end) {\n  /* Since getSize() is expensive (it reads the ticks' size from the DOM), we do this check first to avoid calculating\n   * the tick's size. */\n  if (sign * tickPosition < sign * start || sign * tickPosition > sign * end) {\n    return false;\n  }\n  var size = getSize();\n  return sign * (tickPosition - sign * size / 2 - start) >= 0 && sign * (tickPosition + sign * size / 2 - end) <= 0;\n}\nexport function getNumberIntervalTicks(ticks, interval) {\n  return getEveryNthWithCondition(ticks, interval + 1);\n}", "map": {"version": 3, "names": ["getAngledRectangleWidth", "getEveryNthWithCondition", "getAngledTickWidth", "contentSize", "unitSize", "angle", "size", "width", "height", "getTickBoundaries", "viewBox", "sign", "sizeKey", "isWidth", "x", "y", "start", "end", "isVisible", "tickPosition", "getSize", "getNumberIntervalTicks", "ticks", "interval"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/util/TickUtils.js"], "sourcesContent": ["import { getAngledRectangleWidth } from './CartesianUtils';\nimport { getEveryNthWithCondition } from './getEveryNthWithCondition';\nexport function getAngledTickWidth(contentSize, unitSize, angle) {\n  var size = {\n    width: contentSize.width + unitSize.width,\n    height: contentSize.height + unitSize.height\n  };\n  return getAngledRectangleWidth(size, angle);\n}\nexport function getTickBoundaries(viewBox, sign, sizeKey) {\n  var isWidth = sizeKey === 'width';\n  var {\n    x,\n    y,\n    width,\n    height\n  } = viewBox;\n  if (sign === 1) {\n    return {\n      start: isWidth ? x : y,\n      end: isWidth ? x + width : y + height\n    };\n  }\n  return {\n    start: isWidth ? x + width : y + height,\n    end: isWidth ? x : y\n  };\n}\nexport function isVisible(sign, tickPosition, getSize, start, end) {\n  /* Since getSize() is expensive (it reads the ticks' size from the DOM), we do this check first to avoid calculating\n   * the tick's size. */\n  if (sign * tickPosition < sign * start || sign * tickPosition > sign * end) {\n    return false;\n  }\n  var size = getSize();\n  return sign * (tickPosition - sign * size / 2 - start) >= 0 && sign * (tickPosition + sign * size / 2 - end) <= 0;\n}\nexport function getNumberIntervalTicks(ticks, interval) {\n  return getEveryNthWithCondition(ticks, interval + 1);\n}"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,kBAAkB;AAC1D,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,OAAO,SAASC,kBAAkBA,CAACC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EAC/D,IAAIC,IAAI,GAAG;IACTC,KAAK,EAAEJ,WAAW,CAACI,KAAK,GAAGH,QAAQ,CAACG,KAAK;IACzCC,MAAM,EAAEL,WAAW,CAACK,MAAM,GAAGJ,QAAQ,CAACI;EACxC,CAAC;EACD,OAAOR,uBAAuB,CAACM,IAAI,EAAED,KAAK,CAAC;AAC7C;AACA,OAAO,SAASI,iBAAiBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACxD,IAAIC,OAAO,GAAGD,OAAO,KAAK,OAAO;EACjC,IAAI;IACFE,CAAC;IACDC,CAAC;IACDR,KAAK;IACLC;EACF,CAAC,GAAGE,OAAO;EACX,IAAIC,IAAI,KAAK,CAAC,EAAE;IACd,OAAO;MACLK,KAAK,EAAEH,OAAO,GAAGC,CAAC,GAAGC,CAAC;MACtBE,GAAG,EAAEJ,OAAO,GAAGC,CAAC,GAAGP,KAAK,GAAGQ,CAAC,GAAGP;IACjC,CAAC;EACH;EACA,OAAO;IACLQ,KAAK,EAAEH,OAAO,GAAGC,CAAC,GAAGP,KAAK,GAAGQ,CAAC,GAAGP,MAAM;IACvCS,GAAG,EAAEJ,OAAO,GAAGC,CAAC,GAAGC;EACrB,CAAC;AACH;AACA,OAAO,SAASG,SAASA,CAACP,IAAI,EAAEQ,YAAY,EAAEC,OAAO,EAAEJ,KAAK,EAAEC,GAAG,EAAE;EACjE;AACF;EACE,IAAIN,IAAI,GAAGQ,YAAY,GAAGR,IAAI,GAAGK,KAAK,IAAIL,IAAI,GAAGQ,YAAY,GAAGR,IAAI,GAAGM,GAAG,EAAE;IAC1E,OAAO,KAAK;EACd;EACA,IAAIX,IAAI,GAAGc,OAAO,CAAC,CAAC;EACpB,OAAOT,IAAI,IAAIQ,YAAY,GAAGR,IAAI,GAAGL,IAAI,GAAG,CAAC,GAAGU,KAAK,CAAC,IAAI,CAAC,IAAIL,IAAI,IAAIQ,YAAY,GAAGR,IAAI,GAAGL,IAAI,GAAG,CAAC,GAAGW,GAAG,CAAC,IAAI,CAAC;AACnH;AACA,OAAO,SAASI,sBAAsBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACtD,OAAOtB,wBAAwB,CAACqB,KAAK,EAAEC,QAAQ,GAAG,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}