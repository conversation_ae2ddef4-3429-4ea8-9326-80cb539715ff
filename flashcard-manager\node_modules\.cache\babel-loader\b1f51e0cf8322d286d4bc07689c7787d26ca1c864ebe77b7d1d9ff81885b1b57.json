{"ast": null, "code": "import { createSelector } from 'reselect';\nexport var selectAllXAxes = createSelector(state => state.cartesianAxis.xAxis, xAxisMap => {\n  return Object.values(xAxisMap);\n});\nexport var selectAllYAxes = createSelector(state => state.cartesianAxis.yAxis, yAxisMap => {\n  return Object.values(yAxisMap);\n});", "map": {"version": 3, "names": ["createSelector", "selectAllXAxes", "state", "cartesianAxis", "xAxis", "xAxisMap", "Object", "values", "selectAllYAxes", "yAxis", "yAxisMap"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/selectors/selectAllAxes.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nexport var selectAllXAxes = createSelector(state => state.cartesianAxis.xAxis, xAxisMap => {\n  return Object.values(xAxisMap);\n});\nexport var selectAllYAxes = createSelector(state => state.cartesianAxis.yAxis, yAxisMap => {\n  return Object.values(yAxisMap);\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,OAAO,IAAIC,cAAc,GAAGD,cAAc,CAACE,KAAK,IAAIA,KAAK,CAACC,aAAa,CAACC,KAAK,EAAEC,QAAQ,IAAI;EACzF,OAAOC,MAAM,CAACC,MAAM,CAACF,QAAQ,CAAC;AAChC,CAAC,CAAC;AACF,OAAO,IAAIG,cAAc,GAAGR,cAAc,CAACE,KAAK,IAAIA,KAAK,CAACC,aAAa,CAACM,KAAK,EAAEC,QAAQ,IAAI;EACzF,OAAOJ,MAAM,CAACC,MAAM,CAACG,QAAQ,CAAC;AAChC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}