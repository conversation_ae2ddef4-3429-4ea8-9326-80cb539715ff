{"ast": null, "code": "import { useEffect } from 'react';\nimport { setChartData, setComputedData } from '../state/chartDataSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from './PanoramaContext';\nexport var ChartDataContextProvider = props => {\n  var {\n    chartData\n  } = props;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      // Panorama mode reuses data from the main chart, so we must not overwrite it here.\n      return () => {\n        // there is nothing to clean up\n      };\n    }\n    dispatch(setChartData(chartData));\n    return () => {\n      dispatch(setChartData(undefined));\n    };\n  }, [chartData, dispatch, isPanorama]);\n  return null;\n};\nexport var SetComputedData = props => {\n  var {\n    computedData\n  } = props;\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setComputedData(computedData));\n    return () => {\n      dispatch(setChartData(undefined));\n    };\n  }, [computedData, dispatch]);\n  return null;\n};\nvar selectChartData = state => state.chartData.chartData;\n\n/**\n * \"data\" is the data of the chart - it has no type because this part of recharts is very flexible.\n * Basically it's an array of \"something\" and then there's the dataKey property in various places\n * that's meant to pull other things away from the data.\n *\n * Some charts have `data` defined on the chart root, and they will return the array through this hook.\n * For example: <ComposedChart data={data} />.\n *\n * Other charts, such as Pie, have data defined on individual graphical elements.\n * These charts will return `undefined` through this hook, and you need to read the data from children.\n * For example: <PieChart><Pie data={data} />\n *\n * Some charts also allow setting both - data on the parent, and data on the children at the same time!\n * However, this particular selector will only return the ones defined on the parent.\n *\n * @deprecated use one of the other selectors instead - which one, depends on how do you identify the applicable graphical items.\n *\n * @return data array for some charts and undefined for other\n */\nexport var useChartData = () => useAppSelector(selectChartData);\nvar selectDataIndex = state => {\n  var {\n    dataStartIndex,\n    dataEndIndex\n  } = state.chartData;\n  return {\n    startIndex: dataStartIndex,\n    endIndex: dataEndIndex\n  };\n};\n\n/**\n * startIndex and endIndex are data boundaries, set through Brush.\n *\n * @return object with startIndex and endIndex\n */\nexport var useDataIndex = () => {\n  return useAppSelector(selectDataIndex);\n};", "map": {"version": 3, "names": ["useEffect", "setChartData", "setComputedData", "useAppDispatch", "useAppSelector", "useIsPanorama", "ChartDataContextProvider", "props", "chartData", "dispatch", "isPanorama", "undefined", "SetComputedData", "computedData", "selectChartData", "state", "useChartData", "selectDataIndex", "dataStartIndex", "dataEndIndex", "startIndex", "endIndex", "useDataIndex"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/context/chartDataContext.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { setChartData, setComputedData } from '../state/chartDataSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from './PanoramaContext';\nexport var ChartDataContextProvider = props => {\n  var {\n    chartData\n  } = props;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      // Panorama mode reuses data from the main chart, so we must not overwrite it here.\n      return () => {\n        // there is nothing to clean up\n      };\n    }\n    dispatch(setChartData(chartData));\n    return () => {\n      dispatch(setChartData(undefined));\n    };\n  }, [chartData, dispatch, isPanorama]);\n  return null;\n};\nexport var SetComputedData = props => {\n  var {\n    computedData\n  } = props;\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setComputedData(computedData));\n    return () => {\n      dispatch(setChartData(undefined));\n    };\n  }, [computedData, dispatch]);\n  return null;\n};\nvar selectChartData = state => state.chartData.chartData;\n\n/**\n * \"data\" is the data of the chart - it has no type because this part of recharts is very flexible.\n * Basically it's an array of \"something\" and then there's the dataKey property in various places\n * that's meant to pull other things away from the data.\n *\n * Some charts have `data` defined on the chart root, and they will return the array through this hook.\n * For example: <ComposedChart data={data} />.\n *\n * Other charts, such as Pie, have data defined on individual graphical elements.\n * These charts will return `undefined` through this hook, and you need to read the data from children.\n * For example: <PieChart><Pie data={data} />\n *\n * Some charts also allow setting both - data on the parent, and data on the children at the same time!\n * However, this particular selector will only return the ones defined on the parent.\n *\n * @deprecated use one of the other selectors instead - which one, depends on how do you identify the applicable graphical items.\n *\n * @return data array for some charts and undefined for other\n */\nexport var useChartData = () => useAppSelector(selectChartData);\nvar selectDataIndex = state => {\n  var {\n    dataStartIndex,\n    dataEndIndex\n  } = state.chartData;\n  return {\n    startIndex: dataStartIndex,\n    endIndex: dataEndIndex\n  };\n};\n\n/**\n * startIndex and endIndex are data boundaries, set through Brush.\n *\n * @return object with startIndex and endIndex\n */\nexport var useDataIndex = () => {\n  return useAppSelector(selectDataIndex);\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,YAAY,EAAEC,eAAe,QAAQ,yBAAyB;AACvE,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAO,IAAIC,wBAAwB,GAAGC,KAAK,IAAI;EAC7C,IAAI;IACFC;EACF,CAAC,GAAGD,KAAK;EACT,IAAIE,QAAQ,GAAGN,cAAc,CAAC,CAAC;EAC/B,IAAIO,UAAU,GAAGL,aAAa,CAAC,CAAC;EAChCL,SAAS,CAAC,MAAM;IACd,IAAIU,UAAU,EAAE;MACd;MACA,OAAO,MAAM;QACX;MAAA,CACD;IACH;IACAD,QAAQ,CAACR,YAAY,CAACO,SAAS,CAAC,CAAC;IACjC,OAAO,MAAM;MACXC,QAAQ,CAACR,YAAY,CAACU,SAAS,CAAC,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,CAACH,SAAS,EAAEC,QAAQ,EAAEC,UAAU,CAAC,CAAC;EACrC,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIE,eAAe,GAAGL,KAAK,IAAI;EACpC,IAAI;IACFM;EACF,CAAC,GAAGN,KAAK;EACT,IAAIE,QAAQ,GAAGN,cAAc,CAAC,CAAC;EAC/BH,SAAS,CAAC,MAAM;IACdS,QAAQ,CAACP,eAAe,CAACW,YAAY,CAAC,CAAC;IACvC,OAAO,MAAM;MACXJ,QAAQ,CAACR,YAAY,CAACU,SAAS,CAAC,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,CAACE,YAAY,EAAEJ,QAAQ,CAAC,CAAC;EAC5B,OAAO,IAAI;AACb,CAAC;AACD,IAAIK,eAAe,GAAGC,KAAK,IAAIA,KAAK,CAACP,SAAS,CAACA,SAAS;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIQ,YAAY,GAAGA,CAAA,KAAMZ,cAAc,CAACU,eAAe,CAAC;AAC/D,IAAIG,eAAe,GAAGF,KAAK,IAAI;EAC7B,IAAI;IACFG,cAAc;IACdC;EACF,CAAC,GAAGJ,KAAK,CAACP,SAAS;EACnB,OAAO;IACLY,UAAU,EAAEF,cAAc;IAC1BG,QAAQ,EAAEF;EACZ,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,YAAY,GAAGA,CAAA,KAAM;EAC9B,OAAOlB,cAAc,CAACa,eAAe,CAAC;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}