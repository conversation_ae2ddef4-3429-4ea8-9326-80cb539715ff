{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * From all Brush properties, only height has a default value and will always be defined.\n * Other properties are nullable and will be computed from offsets and margins if they are not set.\n */\n\nvar initialState = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  padding: {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }\n};\nexport var brushSlice = createSlice({\n  name: 'brush',\n  initialState,\n  reducers: {\n    setBrushSettings(_state, action) {\n      if (action.payload == null) {\n        return initialState;\n      }\n      return action.payload;\n    }\n  }\n});\nexport var {\n  setBrushSettings\n} = brushSlice.actions;\nexport var brushReducer = brushSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "x", "y", "width", "height", "padding", "top", "right", "bottom", "left", "brushSlice", "name", "reducers", "setBrushSettings", "_state", "action", "payload", "actions", "brushReducer", "reducer"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/brushSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * From all Brush properties, only height has a default value and will always be defined.\n * Other properties are nullable and will be computed from offsets and margins if they are not set.\n */\n\nvar initialState = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  padding: {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }\n};\nexport var brushSlice = createSlice({\n  name: 'brush',\n  initialState,\n  reducers: {\n    setBrushSettings(_state, action) {\n      if (action.payload == null) {\n        return initialState;\n      }\n      return action.payload;\n    }\n  }\n});\nexport var {\n  setBrushSettings\n} = brushSlice.actions;\nexport var brushReducer = brushSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG;EACjBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE;IACPC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR;AACF,CAAC;AACD,OAAO,IAAIC,UAAU,GAAGX,WAAW,CAAC;EAClCY,IAAI,EAAE,OAAO;EACbX,YAAY;EACZY,QAAQ,EAAE;IACRC,gBAAgBA,CAACC,MAAM,EAAEC,MAAM,EAAE;MAC/B,IAAIA,MAAM,CAACC,OAAO,IAAI,IAAI,EAAE;QAC1B,OAAOhB,YAAY;MACrB;MACA,OAAOe,MAAM,CAACC,OAAO;IACvB;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTH;AACF,CAAC,GAAGH,UAAU,CAACO,OAAO;AACtB,OAAO,IAAIC,YAAY,GAAGR,UAAU,CAACS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}