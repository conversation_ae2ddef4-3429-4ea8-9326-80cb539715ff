{"ast": null, "code": "import { point } from \"./cardinal.js\";\nexport function CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\nCardinalOpen.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);\n        break;\n      case 3:\n        this._point = 4;\n      // falls through\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(tension) {\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n  cardinal.tension = function (tension) {\n    return custom(+tension);\n  };\n  return cardinal;\n})(0);", "map": {"version": 3, "names": ["point", "<PERSON><PERSON><PERSON>", "context", "tension", "_context", "_k", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_x0", "_x1", "_x2", "_y0", "_y1", "_y2", "_point", "lineEnd", "closePath", "x", "y", "lineTo", "moveTo", "custom", "cardinal"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-shape/src/curve/cardinalOpen.js"], "sourcesContent": ["import {point} from \"./cardinal.js\";\n\nexport function CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,eAAe;AAEnC,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC7C,IAAI,CAACC,QAAQ,GAAGF,OAAO;EACvB,IAAI,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGF,OAAO,IAAI,CAAC;AAC7B;AAEAF,YAAY,CAACK,SAAS,GAAG;EACvBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAC9B,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGP,GAAG;IACpC,IAAI,CAACQ,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,IAAI,CAACX,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACU,MAAM,KAAK,CAAE,EAAE,IAAI,CAACd,QAAQ,CAACgB,SAAS,CAAC,CAAC;IACpF,IAAI,CAACZ,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDR,KAAK,EAAE,SAAAA,CAASqB,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,QAAQ,IAAI,CAACJ,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACV,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAACmB,MAAM,CAAC,IAAI,CAACT,GAAG,EAAE,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACb,QAAQ,CAACoB,MAAM,CAAC,IAAI,CAACV,GAAG,EAAE,IAAI,CAACG,GAAG,CAAC;QAAE;MAC3H,KAAK,CAAC;QAAE,IAAI,CAACC,MAAM,GAAG,CAAC;MAAE;MACzB;QAASlB,KAAK,CAAC,IAAI,EAAEqB,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IACA,IAAI,CAACV,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGO,CAAC;IACtD,IAAI,CAACN,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGK,CAAC;EACxD;AACF,CAAC;AAED,eAAe,CAAC,SAASG,MAAMA,CAACtB,OAAO,EAAE;EAEvC,SAASuB,QAAQA,CAACxB,OAAO,EAAE;IACzB,OAAO,IAAID,YAAY,CAACC,OAAO,EAAEC,OAAO,CAAC;EAC3C;EAEAuB,QAAQ,CAACvB,OAAO,GAAG,UAASA,OAAO,EAAE;IACnC,OAAOsB,MAAM,CAAC,CAACtB,OAAO,CAAC;EACzB,CAAC;EAED,OAAOuB,QAAQ;AACjB,CAAC,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}