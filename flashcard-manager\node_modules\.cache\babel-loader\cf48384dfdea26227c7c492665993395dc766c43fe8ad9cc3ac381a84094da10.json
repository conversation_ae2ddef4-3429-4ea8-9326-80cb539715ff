{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction last(arr) {\n  return arr[arr.length - 1];\n}\nexports.last = last;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "last", "arr", "length"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/dist/array/last.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction last(arr) {\n    return arr[arr.length - 1];\n}\n\nexports.last = last;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,IAAIA,CAACC,GAAG,EAAE;EACf,OAAOA,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;AAC9B;AAEAN,OAAO,CAACI,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}