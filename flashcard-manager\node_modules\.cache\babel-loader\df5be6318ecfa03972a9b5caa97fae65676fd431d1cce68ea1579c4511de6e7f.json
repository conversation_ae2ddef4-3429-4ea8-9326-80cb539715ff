{"ast": null, "code": "var _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\", \"type\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as React from 'react';\nimport { PureComponent } from 'react';\nimport omit from 'es-toolkit/compat/omit';\nimport get from 'es-toolkit/compat/get';\nimport { Layer } from '../container/Layer';\nimport { Surface } from '../container/Surface';\nimport { Polygon } from '../shape/Polygon';\nimport { Rectangle } from '../shape/Rectangle';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { COLOR_PANEL } from '../util/Constants';\nimport { isNan, uniqueId } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { ReportChartMargin, ReportChartSize } from '../context/chartLayoutContext';\nimport { TooltipPortalContext } from '../context/tooltipPortalContext';\nimport { RechartsWrapper } from './RechartsWrapper';\nimport { setActiveClickItemIndex, setActiveMouseOverItemIndex } from '../state/tooltipSlice';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { RechartsStoreProvider } from '../state/RechartsStoreProvider';\nimport { useAppDispatch } from '../state/hooks';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nimport { Animate } from '../animation/Animate';\nvar NODE_VALUE_KEY = 'value';\n\n/**\n * This is what end users defines as `data` on Treemap.\n */\n\n/**\n * This is what is returned from `squarify`, the final treemap data structure\n * that gets rendered and is stored in\n */\n\nexport var treemapPayloadSearcher = (data, activeIndex) => {\n  return get(data, activeIndex);\n};\nexport var addToTreemapNodeIndex = function addToTreemapNodeIndex(indexInChildrenArr) {\n  var activeTooltipIndexSoFar = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  return \"\".concat(activeTooltipIndexSoFar, \"children[\").concat(indexInChildrenArr, \"]\");\n};\nvar options = {\n  chartName: 'Treemap',\n  defaultTooltipEventType: 'item',\n  validateTooltipEventTypes: ['item'],\n  tooltipPayloadSearcher: treemapPayloadSearcher,\n  eventEmitter: undefined\n};\nexport var computeNode = _ref => {\n  var {\n    depth,\n    node,\n    index,\n    dataKey,\n    nameKey,\n    nestedActiveTooltipIndex\n  } = _ref;\n  var currentTooltipIndex = depth === 0 ? '' : addToTreemapNodeIndex(index, nestedActiveTooltipIndex);\n  var {\n    children\n  } = node;\n  var childDepth = depth + 1;\n  var computedChildren = children && children.length ? children.map((child, i) => computeNode({\n    depth: childDepth,\n    node: child,\n    index: i,\n    dataKey,\n    nameKey,\n    nestedActiveTooltipIndex: currentTooltipIndex\n  })) : null;\n  var nodeValue;\n  if (children && children.length) {\n    nodeValue = computedChildren.reduce((result, child) => result + child[NODE_VALUE_KEY], 0);\n  } else {\n    // TODO need to verify dataKey\n    nodeValue = isNan(node[dataKey]) || node[dataKey] <= 0 ? 0 : node[dataKey];\n  }\n  return _objectSpread(_objectSpread({}, node), {}, {\n    children: computedChildren,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    name: getValueByDataKey(node, nameKey, ''),\n    [NODE_VALUE_KEY]: nodeValue,\n    depth,\n    index,\n    tooltipIndex: currentTooltipIndex\n  });\n};\nvar filterRect = node => ({\n  x: node.x,\n  y: node.y,\n  width: node.width,\n  height: node.height\n});\n\n// Compute the area for each child based on value & scale.\nvar getAreaOfChildren = (children, areaValueRatio) => {\n  var ratio = areaValueRatio < 0 ? 0 : areaValueRatio;\n  return children.map(child => {\n    var area = child[NODE_VALUE_KEY] * ratio;\n    return _objectSpread(_objectSpread({}, child), {}, {\n      area: isNan(area) || area <= 0 ? 0 : area\n    });\n  });\n};\n\n// Computes the score for the specified row, as the worst aspect ratio.\nvar getWorstScore = (row, parentSize, aspectRatio) => {\n  var parentArea = parentSize * parentSize;\n  var rowArea = row.area * row.area;\n  var {\n    min,\n    max\n  } = row.reduce((result, child) => ({\n    min: Math.min(result.min, child.area),\n    max: Math.max(result.max, child.area)\n  }), {\n    min: Infinity,\n    max: 0\n  });\n  return rowArea ? Math.max(parentArea * max * aspectRatio / rowArea, rowArea / (parentArea * min * aspectRatio)) : Infinity;\n};\nvar horizontalPosition = (row, parentSize, parentRect, isFlush) => {\n  var rowHeight = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowHeight > parentRect.height) {\n    rowHeight = parentRect.height;\n  }\n  var curX = parentRect.x;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = curX;\n    child.y = parentRect.y;\n    child.height = rowHeight;\n    child.width = Math.min(rowHeight ? Math.round(child.area / rowHeight) : 0, parentRect.x + parentRect.width - curX);\n    curX += child.width;\n  }\n  // add the remain x to the last one of row\n  child.width += parentRect.x + parentRect.width - curX;\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    y: parentRect.y + rowHeight,\n    height: parentRect.height - rowHeight\n  });\n};\nvar verticalPosition = (row, parentSize, parentRect, isFlush) => {\n  var rowWidth = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowWidth > parentRect.width) {\n    rowWidth = parentRect.width;\n  }\n  var curY = parentRect.y;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = parentRect.x;\n    child.y = curY;\n    child.width = rowWidth;\n    child.height = Math.min(rowWidth ? Math.round(child.area / rowWidth) : 0, parentRect.y + parentRect.height - curY);\n    curY += child.height;\n  }\n  if (child) {\n    child.height += parentRect.y + parentRect.height - curY;\n  }\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    x: parentRect.x + rowWidth,\n    width: parentRect.width - rowWidth\n  });\n};\nvar position = (row, parentSize, parentRect, isFlush) => {\n  if (parentSize === parentRect.width) {\n    return horizontalPosition(row, parentSize, parentRect, isFlush);\n  }\n  return verticalPosition(row, parentSize, parentRect, isFlush);\n};\n\n// Recursively arranges the specified node's children into squarified rows.\nvar squarify = (node, aspectRatio) => {\n  var {\n    children\n  } = node;\n  if (children && children.length) {\n    var rect = filterRect(node);\n    // maybe a bug\n    var row = [];\n    var best = Infinity; // the best row score so far\n    var child, score; // the current row score\n    var size = Math.min(rect.width, rect.height); // initial orientation\n    var scaleChildren = getAreaOfChildren(children, rect.width * rect.height / node[NODE_VALUE_KEY]);\n    var tempChildren = scaleChildren.slice();\n    row.area = 0;\n    while (tempChildren.length > 0) {\n      // row first\n      // eslint-disable-next-line prefer-destructuring\n      row.push(child = tempChildren[0]);\n      row.area += child.area;\n      score = getWorstScore(row, size, aspectRatio);\n      if (score <= best) {\n        // continue with this orientation\n        tempChildren.shift();\n        best = score;\n      } else {\n        // abort, and try a different orientation\n        row.area -= row.pop().area;\n        rect = position(row, size, rect, false);\n        size = Math.min(rect.width, rect.height);\n        row.length = row.area = 0;\n        best = Infinity;\n      }\n    }\n    if (row.length) {\n      rect = position(row, size, rect, true);\n      row.length = row.area = 0;\n    }\n    return _objectSpread(_objectSpread({}, node), {}, {\n      children: scaleChildren.map(c => squarify(c, aspectRatio))\n    });\n  }\n  return node;\n};\nvar defaultState = {\n  isAnimationFinished: false,\n  formatRoot: null,\n  currentRoot: null,\n  nestIndex: []\n};\nfunction ContentItem(_ref2) {\n  var {\n    content,\n    nodeProps,\n    type,\n    colorPanel,\n    onMouseEnter,\n    onMouseLeave,\n    onClick\n  } = _ref2;\n  if (/*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.createElement(Layer, {\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick\n    }, /*#__PURE__*/React.cloneElement(content, nodeProps));\n  }\n  if (typeof content === 'function') {\n    return /*#__PURE__*/React.createElement(Layer, {\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick\n    }, content(nodeProps));\n  }\n  // optimize default shape\n  var {\n    x,\n    y,\n    width,\n    height,\n    index\n  } = nodeProps;\n  var arrow = null;\n  if (width > 10 && height > 10 && nodeProps.children && type === 'nest') {\n    arrow = /*#__PURE__*/React.createElement(Polygon, {\n      points: [{\n        x: x + 2,\n        y: y + height / 2\n      }, {\n        x: x + 6,\n        y: y + height / 2 + 3\n      }, {\n        x: x + 2,\n        y: y + height / 2 + 6\n      }]\n    });\n  }\n  var text = null;\n  var nameSize = getStringSize(nodeProps.name);\n  if (width > 20 && height > 20 && nameSize.width < width && nameSize.height < height) {\n    text = /*#__PURE__*/React.createElement(\"text\", {\n      x: x + 8,\n      y: y + height / 2 + 7,\n      fontSize: 14\n    }, nodeProps.name);\n  }\n  var colors = colorPanel || COLOR_PANEL;\n  return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(Rectangle, _extends({\n    fill: nodeProps.depth < 2 ? colors[index % colors.length] : 'rgba(255,255,255,0)',\n    stroke: \"#fff\"\n  }, omit(nodeProps, ['children']), {\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    \"data-recharts-item-index\": nodeProps.tooltipIndex\n  })), arrow, text);\n}\nfunction ContentItemWithEvents(props) {\n  var dispatch = useAppDispatch();\n  var activeCoordinate = props.nodeProps ? {\n    x: props.nodeProps.x + props.nodeProps.width / 2,\n    y: props.nodeProps.y + props.nodeProps.height / 2\n  } : null;\n  var onMouseEnter = () => {\n    dispatch(setActiveMouseOverItemIndex({\n      activeIndex: props.nodeProps.tooltipIndex,\n      activeDataKey: props.dataKey,\n      activeCoordinate\n    }));\n  };\n  var onMouseLeave = () => {\n    // clearing state on mouseLeaveItem causes re-rendering issues\n    // we don't actually want to do this for TreeMap - we clear state when we leave the entire chart instead\n  };\n  var onClick = () => {\n    dispatch(setActiveClickItemIndex({\n      activeIndex: props.nodeProps.tooltipIndex,\n      activeDataKey: props.dataKey,\n      activeCoordinate\n    }));\n  };\n  return /*#__PURE__*/React.createElement(ContentItem, _extends({}, props, {\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick\n  }));\n}\nfunction getTooltipEntrySettings(_ref3) {\n  var {\n    props,\n    currentRoot\n  } = _ref3;\n  var {\n    dataKey,\n    nameKey,\n    stroke,\n    fill\n  } = props;\n  return {\n    dataDefinedOnItem: currentRoot,\n    positions: undefined,\n    // TODO I think Treemap has the capability of computing positions and supporting defaultIndex? Except it doesn't yet\n    settings: {\n      stroke,\n      strokeWidth: undefined,\n      fill,\n      dataKey,\n      nameKey,\n      name: undefined,\n      // Each TreemapNode has its own name\n      hide: false,\n      type: undefined,\n      color: fill,\n      unit: ''\n    }\n  };\n}\n\n// Why is margin not a treemap prop? No clue. Probably it should be\nvar defaultTreemapMargin = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nclass TreemapWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", _objectSpread({}, defaultState));\n    _defineProperty(this, \"handleAnimationEnd\", () => {\n      var {\n        onAnimationEnd\n      } = this.props;\n      this.setState({\n        isAnimationFinished: true\n      });\n      if (typeof onAnimationEnd === 'function') {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(this, \"handleAnimationStart\", () => {\n      var {\n        onAnimationStart\n      } = this.props;\n      this.setState({\n        isAnimationFinished: false\n      });\n      if (typeof onAnimationStart === 'function') {\n        onAnimationStart();\n      }\n    });\n    _defineProperty(this, \"handleTouchMove\", (_state, e) => {\n      var touchEvent = e.touches[0];\n      var target = document.elementFromPoint(touchEvent.clientX, touchEvent.clientY);\n      if (!target || !target.getAttribute) {\n        return;\n      }\n      var itemIndex = target.getAttribute('data-recharts-item-index');\n      var activeNode = treemapPayloadSearcher(this.state.formatRoot, itemIndex);\n      if (!activeNode) {\n        return;\n      }\n      var {\n        dataKey,\n        dispatch\n      } = this.props;\n      var activeCoordinate = {\n        x: activeNode.x + activeNode.width / 2,\n        y: activeNode.y + activeNode.height / 2\n      };\n\n      // Treemap does not support onTouchMove prop, but it could\n      // onTouchMove?.(activeNode, Number(itemIndex), e);\n      dispatch(setActiveMouseOverItemIndex({\n        activeIndex: itemIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n    });\n  }\n  static getDerivedStateFromProps(nextProps, prevState) {\n    if (nextProps.data !== prevState.prevData || nextProps.type !== prevState.prevType || nextProps.width !== prevState.prevWidth || nextProps.height !== prevState.prevHeight || nextProps.dataKey !== prevState.prevDataKey || nextProps.aspectRatio !== prevState.prevAspectRatio) {\n      var root = computeNode({\n        depth: 0,\n        // @ts-expect-error missing properties\n        node: {\n          children: nextProps.data,\n          x: 0,\n          y: 0,\n          width: nextProps.width,\n          height: nextProps.height\n        },\n        index: 0,\n        dataKey: nextProps.dataKey,\n        nameKey: nextProps.nameKey\n      });\n      var formatRoot = squarify(root, nextProps.aspectRatio);\n      return _objectSpread(_objectSpread({}, prevState), {}, {\n        formatRoot,\n        currentRoot: root,\n        nestIndex: [root],\n        prevAspectRatio: nextProps.aspectRatio,\n        prevData: nextProps.data,\n        prevWidth: nextProps.width,\n        prevHeight: nextProps.height,\n        prevDataKey: nextProps.dataKey,\n        prevType: nextProps.type\n      });\n    }\n    return null;\n  }\n  handleMouseEnter(node, e) {\n    e.persist();\n    var {\n      onMouseEnter\n    } = this.props;\n    if (onMouseEnter) {\n      onMouseEnter(node, e);\n    }\n  }\n  handleMouseLeave(node, e) {\n    e.persist();\n    var {\n      onMouseLeave\n    } = this.props;\n    if (onMouseLeave) {\n      onMouseLeave(node, e);\n    }\n  }\n  handleClick(node) {\n    var {\n      onClick,\n      type\n    } = this.props;\n    if (type === 'nest' && node.children) {\n      var {\n        width,\n        height,\n        dataKey,\n        nameKey,\n        aspectRatio\n      } = this.props;\n      var root = computeNode({\n        depth: 0,\n        node: _objectSpread(_objectSpread({}, node), {}, {\n          x: 0,\n          y: 0,\n          width,\n          height\n        }),\n        index: 0,\n        dataKey,\n        nameKey,\n        // with Treemap nesting, should this continue nesting the index or start from empty string?\n        nestedActiveTooltipIndex: node.tooltipIndex\n      });\n      var formatRoot = squarify(root, aspectRatio);\n      var {\n        nestIndex\n      } = this.state;\n      nestIndex.push(node);\n      this.setState({\n        formatRoot,\n        currentRoot: root,\n        nestIndex\n      });\n    }\n    if (onClick) {\n      onClick(node);\n    }\n  }\n  handleNestIndex(node, i) {\n    var {\n      nestIndex\n    } = this.state;\n    var {\n      width,\n      height,\n      dataKey,\n      nameKey,\n      aspectRatio\n    } = this.props;\n    var root = computeNode({\n      depth: 0,\n      node: _objectSpread(_objectSpread({}, node), {}, {\n        x: 0,\n        y: 0,\n        width,\n        height\n      }),\n      index: 0,\n      dataKey,\n      nameKey,\n      // with Treemap nesting, should this continue nesting the index or start from empty string?\n      nestedActiveTooltipIndex: node.tooltipIndex\n    });\n    var formatRoot = squarify(root, aspectRatio);\n    nestIndex = nestIndex.slice(0, i + 1);\n    this.setState({\n      formatRoot,\n      currentRoot: node,\n      nestIndex\n    });\n  }\n  renderItem(content, nodeProps, isLeaf) {\n    var {\n      isAnimationActive,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      isUpdateAnimationActive,\n      type,\n      animationId,\n      colorPanel,\n      dataKey\n    } = this.props;\n    var {\n      isAnimationFinished\n    } = this.state;\n    var {\n      width,\n      height,\n      x,\n      y,\n      depth\n    } = nodeProps;\n    var translateX = parseInt(\"\".concat((Math.random() * 2 - 1) * width), 10);\n    var event = {};\n    if (isLeaf || type === 'nest') {\n      event = {\n        onMouseEnter: this.handleMouseEnter.bind(this, nodeProps),\n        onMouseLeave: this.handleMouseLeave.bind(this, nodeProps),\n        onClick: this.handleClick.bind(this, nodeProps)\n      };\n    }\n    if (!isAnimationActive) {\n      return /*#__PURE__*/React.createElement(Layer, event, /*#__PURE__*/React.createElement(ContentItemWithEvents, {\n        content: content,\n        dataKey: dataKey,\n        nodeProps: _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive: false,\n          isUpdateAnimationActive: false,\n          width,\n          height,\n          x,\n          y\n        }),\n        type: type,\n        colorPanel: colorPanel\n      }));\n    }\n    return /*#__PURE__*/React.createElement(Animate, {\n      begin: animationBegin,\n      duration: animationDuration,\n      isActive: isAnimationActive,\n      easing: animationEasing,\n      key: \"treemap-\".concat(animationId),\n      from: {\n        x,\n        y,\n        width,\n        height\n      },\n      to: {\n        x,\n        y,\n        width,\n        height\n      },\n      onAnimationStart: this.handleAnimationStart,\n      onAnimationEnd: this.handleAnimationEnd\n    }, _ref4 => {\n      var {\n        x: currX,\n        y: currY,\n        width: currWidth,\n        height: currHeight\n      } = _ref4;\n      return /*#__PURE__*/React.createElement(Animate\n      // @ts-expect-error TODO - fix the type error\n      , {\n        from: \"translate(\".concat(translateX, \"px, \").concat(translateX, \"px)\")\n        // @ts-expect-error TODO - fix the type error\n        ,\n\n        to: \"translate(0, 0)\",\n        attributeName: \"transform\",\n        begin: animationBegin,\n        easing: animationEasing,\n        isActive: isAnimationActive,\n        duration: animationDuration\n      }, /*#__PURE__*/React.createElement(Layer, event, depth > 2 && !isAnimationFinished ? null : /*#__PURE__*/React.createElement(ContentItemWithEvents, {\n        content: content,\n        dataKey: dataKey,\n        nodeProps: _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive,\n          isUpdateAnimationActive: !isUpdateAnimationActive,\n          width: currWidth,\n          height: currHeight,\n          x: currX,\n          y: currY\n        }),\n        type: type,\n        colorPanel: colorPanel\n      })));\n    });\n  }\n  renderNode(root, node) {\n    var {\n      content,\n      type\n    } = this.props;\n    var nodeProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props, false)), node), {}, {\n      root\n    });\n    var isLeaf = !node.children || !node.children.length;\n    var {\n      currentRoot\n    } = this.state;\n    var isCurrentRootChild = (currentRoot.children || []).filter(item => item.depth === node.depth && item.name === node.name);\n    if (!isCurrentRootChild.length && root.depth && type === 'nest') {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      key: \"recharts-treemap-node-\".concat(nodeProps.x, \"-\").concat(nodeProps.y, \"-\").concat(nodeProps.name),\n      className: \"recharts-treemap-depth-\".concat(node.depth)\n    }, this.renderItem(content, nodeProps, isLeaf), node.children && node.children.length ? node.children.map(child => this.renderNode(node, child)) : null);\n  }\n  renderAllNodes() {\n    var {\n      formatRoot\n    } = this.state;\n    if (!formatRoot) {\n      return null;\n    }\n    return this.renderNode(formatRoot, formatRoot);\n  }\n\n  // render nest treemap\n  renderNestIndex() {\n    var {\n      nameKey,\n      nestIndexContent\n    } = this.props;\n    var {\n      nestIndex\n    } = this.state;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"recharts-treemap-nest-index-wrapper\",\n      style: {\n        marginTop: '8px',\n        textAlign: 'center'\n      }\n    }, nestIndex.map((item, i) => {\n      // TODO need to verify nameKey type\n      var name = get(item, nameKey, 'root');\n      var content = null;\n      if (/*#__PURE__*/React.isValidElement(nestIndexContent)) {\n        content = /*#__PURE__*/React.cloneElement(nestIndexContent, item, i);\n      }\n      if (typeof nestIndexContent === 'function') {\n        content = nestIndexContent(item, i);\n      } else {\n        content = name;\n      }\n      return (/*#__PURE__*/\n        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions\n        React.createElement(\"div\", {\n          onClick: this.handleNestIndex.bind(this, item, i),\n          key: \"nest-index-\".concat(uniqueId()),\n          className: \"recharts-treemap-nest-index-box\",\n          style: {\n            cursor: 'pointer',\n            display: 'inline-block',\n            padding: '0 7px',\n            background: '#000',\n            color: '#fff',\n            marginRight: '3px'\n          }\n        }, content)\n      );\n    }));\n  }\n  render() {\n    var _this$props = this.props,\n      {\n        width,\n        height,\n        className,\n        style,\n        children,\n        type\n      } = _this$props,\n      others = _objectWithoutProperties(_this$props, _excluded);\n    var attrs = filterProps(others, false);\n    return /*#__PURE__*/React.createElement(TooltipPortalContext.Provider, {\n      value: this.state.tooltipPortal\n    }, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: {\n        props: this.props,\n        currentRoot: this.state.currentRoot\n      }\n    }), /*#__PURE__*/React.createElement(RechartsWrapper, {\n      className: className,\n      style: style,\n      width: width,\n      height: height,\n      ref: node => {\n        if (this.state.tooltipPortal == null) {\n          this.setState({\n            tooltipPortal: node\n          });\n        }\n      },\n      onMouseEnter: undefined,\n      onMouseLeave: undefined,\n      onClick: undefined,\n      onMouseMove: undefined,\n      onMouseDown: undefined,\n      onMouseUp: undefined,\n      onContextMenu: undefined,\n      onDoubleClick: undefined,\n      onTouchStart: undefined,\n      onTouchMove: this.handleTouchMove,\n      onTouchEnd: undefined\n    }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n      width: width,\n      height: type === 'nest' ? height - 30 : height\n    }), this.renderAllNodes(), children), type === 'nest' && this.renderNestIndex()));\n  }\n}\n_defineProperty(TreemapWithState, \"displayName\", 'Treemap');\n_defineProperty(TreemapWithState, \"defaultProps\", {\n  aspectRatio: 0.5 * (1 + Math.sqrt(5)),\n  dataKey: 'value',\n  nameKey: 'name',\n  type: 'flat',\n  isAnimationActive: !Global.isSsr,\n  isUpdateAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'linear'\n});\nfunction TreemapDispatchInject(props) {\n  var dispatch = useAppDispatch();\n  return /*#__PURE__*/React.createElement(TreemapWithState, _extends({}, props, {\n    dispatch: dispatch\n  }));\n}\nexport function Treemap(props) {\n  var _props$className;\n  var {\n    width,\n    height\n  } = props;\n  if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(RechartsStoreProvider, {\n    preloadedState: {\n      options\n    },\n    reduxStoreName: (_props$className = props.className) !== null && _props$className !== void 0 ? _props$className : 'Treemap'\n  }, /*#__PURE__*/React.createElement(ReportChartSize, {\n    width: width,\n    height: height\n  }), /*#__PURE__*/React.createElement(ReportChartMargin, {\n    margin: defaultTreemapMargin\n  }), /*#__PURE__*/React.createElement(TreemapDispatchInject, props));\n}", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "PureComponent", "omit", "get", "Layer", "Surface", "Polygon", "Rectangle", "getValueByDataKey", "COLOR_PANEL", "isNan", "uniqueId", "getStringSize", "Global", "filterProps", "ReportChart<PERSON><PERSON><PERSON>", "ReportChartSize", "TooltipPortalContext", "RechartsWrapper", "setActiveClickItemIndex", "setActiveMouseOverItemIndex", "SetTooltipEntrySettings", "RechartsStoreProvider", "useAppDispatch", "isPositiveNumber", "Animate", "NODE_VALUE_KEY", "treemapPayloadSearcher", "data", "activeIndex", "addToTreemapNodeIndex", "indexInChildrenArr", "activeTooltipIndexSoFar", "undefined", "concat", "options", "chartName", "defaultTooltipEventType", "validateTooltipEventTypes", "tooltipPayloadSearcher", "eventEmitter", "computeNode", "_ref", "depth", "node", "index", "dataKey", "<PERSON><PERSON><PERSON>", "nestedActiveTooltipIndex", "currentTooltipIndex", "children", "<PERSON><PERSON><PERSON><PERSON>", "computed<PERSON><PERSON><PERSON>n", "map", "child", "nodeValue", "reduce", "result", "name", "tooltipIndex", "filterRect", "x", "y", "width", "height", "getAreaOfChildren", "areaValueRatio", "ratio", "area", "getWorstScore", "row", "parentSize", "aspectRatio", "parentArea", "rowArea", "min", "max", "Math", "Infinity", "horizontalPosition", "parentRect", "isFlush", "rowHeight", "round", "curX", "len", "verticalPosition", "row<PERSON>id<PERSON>", "curY", "position", "squarify", "rect", "best", "score", "size", "scaleChildren", "tempC<PERSON><PERSON>n", "slice", "shift", "pop", "c", "defaultState", "isAnimationFinished", "formatRoot", "currentRoot", "nestIndex", "ContentItem", "_ref2", "content", "nodeProps", "type", "colorPanel", "onMouseEnter", "onMouseLeave", "onClick", "isValidElement", "createElement", "cloneElement", "arrow", "points", "text", "nameSize", "fontSize", "colors", "fill", "stroke", "ContentItemWithEvents", "props", "dispatch", "activeCoordinate", "activeDataKey", "getTooltipEntrySettings", "_ref3", "dataDefinedOnItem", "positions", "settings", "strokeWidth", "hide", "color", "unit", "defaultTreemapMargin", "top", "right", "bottom", "left", "TreemapWithState", "constructor", "onAnimationEnd", "setState", "onAnimationStart", "_state", "touchEvent", "touches", "target", "document", "elementFromPoint", "clientX", "clientY", "getAttribute", "itemIndex", "activeNode", "state", "getDerivedStateFromProps", "nextProps", "prevState", "prevData", "prevType", "prevWidth", "prevHeight", "prevDataKey", "prevAspectRatio", "root", "handleMouseEnter", "persist", "handleMouseLeave", "handleClick", "handleNestIndex", "renderItem", "<PERSON><PERSON><PERSON><PERSON>", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "isUpdateAnimationActive", "animationId", "translateX", "parseInt", "random", "event", "begin", "duration", "isActive", "easing", "key", "from", "to", "handleAnimationStart", "handleAnimationEnd", "_ref4", "currX", "currY", "currWidth", "currHeight", "attributeName", "renderNode", "isCurrentRootChild", "item", "className", "renderAllNodes", "renderNestIndex", "nestIndexContent", "style", "marginTop", "textAlign", "cursor", "display", "padding", "background", "marginRight", "render", "_this$props", "others", "attrs", "Provider", "tooltipPortal", "fn", "args", "ref", "onMouseMove", "onMouseDown", "onMouseUp", "onContextMenu", "onDoubleClick", "onTouchStart", "onTouchMove", "handleTouchMove", "onTouchEnd", "sqrt", "isSsr", "TreemapDispatchInject", "Treemap", "_props$className", "preloadedState", "reduxStoreName", "margin"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/chart/Treemap.js"], "sourcesContent": ["var _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\", \"type\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { PureComponent } from 'react';\nimport omit from 'es-toolkit/compat/omit';\nimport get from 'es-toolkit/compat/get';\nimport { Layer } from '../container/Layer';\nimport { Surface } from '../container/Surface';\nimport { Polygon } from '../shape/Polygon';\nimport { Rectangle } from '../shape/Rectangle';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { COLOR_PANEL } from '../util/Constants';\nimport { isNan, uniqueId } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { ReportChartMargin, ReportChartSize } from '../context/chartLayoutContext';\nimport { TooltipPortalContext } from '../context/tooltipPortalContext';\nimport { RechartsWrapper } from './RechartsWrapper';\nimport { setActiveClickItemIndex, setActiveMouseOverItemIndex } from '../state/tooltipSlice';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { RechartsStoreProvider } from '../state/RechartsStoreProvider';\nimport { useAppDispatch } from '../state/hooks';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nimport { Animate } from '../animation/Animate';\nvar NODE_VALUE_KEY = 'value';\n\n/**\n * This is what end users defines as `data` on Treemap.\n */\n\n/**\n * This is what is returned from `squarify`, the final treemap data structure\n * that gets rendered and is stored in\n */\n\nexport var treemapPayloadSearcher = (data, activeIndex) => {\n  return get(data, activeIndex);\n};\nexport var addToTreemapNodeIndex = function addToTreemapNodeIndex(indexInChildrenArr) {\n  var activeTooltipIndexSoFar = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  return \"\".concat(activeTooltipIndexSoFar, \"children[\").concat(indexInChildrenArr, \"]\");\n};\nvar options = {\n  chartName: 'Treemap',\n  defaultTooltipEventType: 'item',\n  validateTooltipEventTypes: ['item'],\n  tooltipPayloadSearcher: treemapPayloadSearcher,\n  eventEmitter: undefined\n};\nexport var computeNode = _ref => {\n  var {\n    depth,\n    node,\n    index,\n    dataKey,\n    nameKey,\n    nestedActiveTooltipIndex\n  } = _ref;\n  var currentTooltipIndex = depth === 0 ? '' : addToTreemapNodeIndex(index, nestedActiveTooltipIndex);\n  var {\n    children\n  } = node;\n  var childDepth = depth + 1;\n  var computedChildren = children && children.length ? children.map((child, i) => computeNode({\n    depth: childDepth,\n    node: child,\n    index: i,\n    dataKey,\n    nameKey,\n    nestedActiveTooltipIndex: currentTooltipIndex\n  })) : null;\n  var nodeValue;\n  if (children && children.length) {\n    nodeValue = computedChildren.reduce((result, child) => result + child[NODE_VALUE_KEY], 0);\n  } else {\n    // TODO need to verify dataKey\n    nodeValue = isNan(node[dataKey]) || node[dataKey] <= 0 ? 0 : node[dataKey];\n  }\n  return _objectSpread(_objectSpread({}, node), {}, {\n    children: computedChildren,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    name: getValueByDataKey(node, nameKey, ''),\n    [NODE_VALUE_KEY]: nodeValue,\n    depth,\n    index,\n    tooltipIndex: currentTooltipIndex\n  });\n};\nvar filterRect = node => ({\n  x: node.x,\n  y: node.y,\n  width: node.width,\n  height: node.height\n});\n\n// Compute the area for each child based on value & scale.\nvar getAreaOfChildren = (children, areaValueRatio) => {\n  var ratio = areaValueRatio < 0 ? 0 : areaValueRatio;\n  return children.map(child => {\n    var area = child[NODE_VALUE_KEY] * ratio;\n    return _objectSpread(_objectSpread({}, child), {}, {\n      area: isNan(area) || area <= 0 ? 0 : area\n    });\n  });\n};\n\n// Computes the score for the specified row, as the worst aspect ratio.\nvar getWorstScore = (row, parentSize, aspectRatio) => {\n  var parentArea = parentSize * parentSize;\n  var rowArea = row.area * row.area;\n  var {\n    min,\n    max\n  } = row.reduce((result, child) => ({\n    min: Math.min(result.min, child.area),\n    max: Math.max(result.max, child.area)\n  }), {\n    min: Infinity,\n    max: 0\n  });\n  return rowArea ? Math.max(parentArea * max * aspectRatio / rowArea, rowArea / (parentArea * min * aspectRatio)) : Infinity;\n};\nvar horizontalPosition = (row, parentSize, parentRect, isFlush) => {\n  var rowHeight = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowHeight > parentRect.height) {\n    rowHeight = parentRect.height;\n  }\n  var curX = parentRect.x;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = curX;\n    child.y = parentRect.y;\n    child.height = rowHeight;\n    child.width = Math.min(rowHeight ? Math.round(child.area / rowHeight) : 0, parentRect.x + parentRect.width - curX);\n    curX += child.width;\n  }\n  // add the remain x to the last one of row\n  child.width += parentRect.x + parentRect.width - curX;\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    y: parentRect.y + rowHeight,\n    height: parentRect.height - rowHeight\n  });\n};\nvar verticalPosition = (row, parentSize, parentRect, isFlush) => {\n  var rowWidth = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowWidth > parentRect.width) {\n    rowWidth = parentRect.width;\n  }\n  var curY = parentRect.y;\n  var child;\n  for (var i = 0, len = row.length; i < len; i++) {\n    child = row[i];\n    child.x = parentRect.x;\n    child.y = curY;\n    child.width = rowWidth;\n    child.height = Math.min(rowWidth ? Math.round(child.area / rowWidth) : 0, parentRect.y + parentRect.height - curY);\n    curY += child.height;\n  }\n  if (child) {\n    child.height += parentRect.y + parentRect.height - curY;\n  }\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    x: parentRect.x + rowWidth,\n    width: parentRect.width - rowWidth\n  });\n};\nvar position = (row, parentSize, parentRect, isFlush) => {\n  if (parentSize === parentRect.width) {\n    return horizontalPosition(row, parentSize, parentRect, isFlush);\n  }\n  return verticalPosition(row, parentSize, parentRect, isFlush);\n};\n\n// Recursively arranges the specified node's children into squarified rows.\nvar squarify = (node, aspectRatio) => {\n  var {\n    children\n  } = node;\n  if (children && children.length) {\n    var rect = filterRect(node);\n    // maybe a bug\n    var row = [];\n    var best = Infinity; // the best row score so far\n    var child, score; // the current row score\n    var size = Math.min(rect.width, rect.height); // initial orientation\n    var scaleChildren = getAreaOfChildren(children, rect.width * rect.height / node[NODE_VALUE_KEY]);\n    var tempChildren = scaleChildren.slice();\n    row.area = 0;\n    while (tempChildren.length > 0) {\n      // row first\n      // eslint-disable-next-line prefer-destructuring\n      row.push(child = tempChildren[0]);\n      row.area += child.area;\n      score = getWorstScore(row, size, aspectRatio);\n      if (score <= best) {\n        // continue with this orientation\n        tempChildren.shift();\n        best = score;\n      } else {\n        // abort, and try a different orientation\n        row.area -= row.pop().area;\n        rect = position(row, size, rect, false);\n        size = Math.min(rect.width, rect.height);\n        row.length = row.area = 0;\n        best = Infinity;\n      }\n    }\n    if (row.length) {\n      rect = position(row, size, rect, true);\n      row.length = row.area = 0;\n    }\n    return _objectSpread(_objectSpread({}, node), {}, {\n      children: scaleChildren.map(c => squarify(c, aspectRatio))\n    });\n  }\n  return node;\n};\nvar defaultState = {\n  isAnimationFinished: false,\n  formatRoot: null,\n  currentRoot: null,\n  nestIndex: []\n};\nfunction ContentItem(_ref2) {\n  var {\n    content,\n    nodeProps,\n    type,\n    colorPanel,\n    onMouseEnter,\n    onMouseLeave,\n    onClick\n  } = _ref2;\n  if (/*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.createElement(Layer, {\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick\n    }, /*#__PURE__*/React.cloneElement(content, nodeProps));\n  }\n  if (typeof content === 'function') {\n    return /*#__PURE__*/React.createElement(Layer, {\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick\n    }, content(nodeProps));\n  }\n  // optimize default shape\n  var {\n    x,\n    y,\n    width,\n    height,\n    index\n  } = nodeProps;\n  var arrow = null;\n  if (width > 10 && height > 10 && nodeProps.children && type === 'nest') {\n    arrow = /*#__PURE__*/React.createElement(Polygon, {\n      points: [{\n        x: x + 2,\n        y: y + height / 2\n      }, {\n        x: x + 6,\n        y: y + height / 2 + 3\n      }, {\n        x: x + 2,\n        y: y + height / 2 + 6\n      }]\n    });\n  }\n  var text = null;\n  var nameSize = getStringSize(nodeProps.name);\n  if (width > 20 && height > 20 && nameSize.width < width && nameSize.height < height) {\n    text = /*#__PURE__*/React.createElement(\"text\", {\n      x: x + 8,\n      y: y + height / 2 + 7,\n      fontSize: 14\n    }, nodeProps.name);\n  }\n  var colors = colorPanel || COLOR_PANEL;\n  return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(Rectangle, _extends({\n    fill: nodeProps.depth < 2 ? colors[index % colors.length] : 'rgba(255,255,255,0)',\n    stroke: \"#fff\"\n  }, omit(nodeProps, ['children']), {\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    \"data-recharts-item-index\": nodeProps.tooltipIndex\n  })), arrow, text);\n}\nfunction ContentItemWithEvents(props) {\n  var dispatch = useAppDispatch();\n  var activeCoordinate = props.nodeProps ? {\n    x: props.nodeProps.x + props.nodeProps.width / 2,\n    y: props.nodeProps.y + props.nodeProps.height / 2\n  } : null;\n  var onMouseEnter = () => {\n    dispatch(setActiveMouseOverItemIndex({\n      activeIndex: props.nodeProps.tooltipIndex,\n      activeDataKey: props.dataKey,\n      activeCoordinate\n    }));\n  };\n  var onMouseLeave = () => {\n    // clearing state on mouseLeaveItem causes re-rendering issues\n    // we don't actually want to do this for TreeMap - we clear state when we leave the entire chart instead\n  };\n  var onClick = () => {\n    dispatch(setActiveClickItemIndex({\n      activeIndex: props.nodeProps.tooltipIndex,\n      activeDataKey: props.dataKey,\n      activeCoordinate\n    }));\n  };\n  return /*#__PURE__*/React.createElement(ContentItem, _extends({}, props, {\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick\n  }));\n}\nfunction getTooltipEntrySettings(_ref3) {\n  var {\n    props,\n    currentRoot\n  } = _ref3;\n  var {\n    dataKey,\n    nameKey,\n    stroke,\n    fill\n  } = props;\n  return {\n    dataDefinedOnItem: currentRoot,\n    positions: undefined,\n    // TODO I think Treemap has the capability of computing positions and supporting defaultIndex? Except it doesn't yet\n    settings: {\n      stroke,\n      strokeWidth: undefined,\n      fill,\n      dataKey,\n      nameKey,\n      name: undefined,\n      // Each TreemapNode has its own name\n      hide: false,\n      type: undefined,\n      color: fill,\n      unit: ''\n    }\n  };\n}\n\n// Why is margin not a treemap prop? No clue. Probably it should be\nvar defaultTreemapMargin = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nclass TreemapWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", _objectSpread({}, defaultState));\n    _defineProperty(this, \"handleAnimationEnd\", () => {\n      var {\n        onAnimationEnd\n      } = this.props;\n      this.setState({\n        isAnimationFinished: true\n      });\n      if (typeof onAnimationEnd === 'function') {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(this, \"handleAnimationStart\", () => {\n      var {\n        onAnimationStart\n      } = this.props;\n      this.setState({\n        isAnimationFinished: false\n      });\n      if (typeof onAnimationStart === 'function') {\n        onAnimationStart();\n      }\n    });\n    _defineProperty(this, \"handleTouchMove\", (_state, e) => {\n      var touchEvent = e.touches[0];\n      var target = document.elementFromPoint(touchEvent.clientX, touchEvent.clientY);\n      if (!target || !target.getAttribute) {\n        return;\n      }\n      var itemIndex = target.getAttribute('data-recharts-item-index');\n      var activeNode = treemapPayloadSearcher(this.state.formatRoot, itemIndex);\n      if (!activeNode) {\n        return;\n      }\n      var {\n        dataKey,\n        dispatch\n      } = this.props;\n      var activeCoordinate = {\n        x: activeNode.x + activeNode.width / 2,\n        y: activeNode.y + activeNode.height / 2\n      };\n\n      // Treemap does not support onTouchMove prop, but it could\n      // onTouchMove?.(activeNode, Number(itemIndex), e);\n      dispatch(setActiveMouseOverItemIndex({\n        activeIndex: itemIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n    });\n  }\n  static getDerivedStateFromProps(nextProps, prevState) {\n    if (nextProps.data !== prevState.prevData || nextProps.type !== prevState.prevType || nextProps.width !== prevState.prevWidth || nextProps.height !== prevState.prevHeight || nextProps.dataKey !== prevState.prevDataKey || nextProps.aspectRatio !== prevState.prevAspectRatio) {\n      var root = computeNode({\n        depth: 0,\n        // @ts-expect-error missing properties\n        node: {\n          children: nextProps.data,\n          x: 0,\n          y: 0,\n          width: nextProps.width,\n          height: nextProps.height\n        },\n        index: 0,\n        dataKey: nextProps.dataKey,\n        nameKey: nextProps.nameKey\n      });\n      var formatRoot = squarify(root, nextProps.aspectRatio);\n      return _objectSpread(_objectSpread({}, prevState), {}, {\n        formatRoot,\n        currentRoot: root,\n        nestIndex: [root],\n        prevAspectRatio: nextProps.aspectRatio,\n        prevData: nextProps.data,\n        prevWidth: nextProps.width,\n        prevHeight: nextProps.height,\n        prevDataKey: nextProps.dataKey,\n        prevType: nextProps.type\n      });\n    }\n    return null;\n  }\n  handleMouseEnter(node, e) {\n    e.persist();\n    var {\n      onMouseEnter\n    } = this.props;\n    if (onMouseEnter) {\n      onMouseEnter(node, e);\n    }\n  }\n  handleMouseLeave(node, e) {\n    e.persist();\n    var {\n      onMouseLeave\n    } = this.props;\n    if (onMouseLeave) {\n      onMouseLeave(node, e);\n    }\n  }\n  handleClick(node) {\n    var {\n      onClick,\n      type\n    } = this.props;\n    if (type === 'nest' && node.children) {\n      var {\n        width,\n        height,\n        dataKey,\n        nameKey,\n        aspectRatio\n      } = this.props;\n      var root = computeNode({\n        depth: 0,\n        node: _objectSpread(_objectSpread({}, node), {}, {\n          x: 0,\n          y: 0,\n          width,\n          height\n        }),\n        index: 0,\n        dataKey,\n        nameKey,\n        // with Treemap nesting, should this continue nesting the index or start from empty string?\n        nestedActiveTooltipIndex: node.tooltipIndex\n      });\n      var formatRoot = squarify(root, aspectRatio);\n      var {\n        nestIndex\n      } = this.state;\n      nestIndex.push(node);\n      this.setState({\n        formatRoot,\n        currentRoot: root,\n        nestIndex\n      });\n    }\n    if (onClick) {\n      onClick(node);\n    }\n  }\n  handleNestIndex(node, i) {\n    var {\n      nestIndex\n    } = this.state;\n    var {\n      width,\n      height,\n      dataKey,\n      nameKey,\n      aspectRatio\n    } = this.props;\n    var root = computeNode({\n      depth: 0,\n      node: _objectSpread(_objectSpread({}, node), {}, {\n        x: 0,\n        y: 0,\n        width,\n        height\n      }),\n      index: 0,\n      dataKey,\n      nameKey,\n      // with Treemap nesting, should this continue nesting the index or start from empty string?\n      nestedActiveTooltipIndex: node.tooltipIndex\n    });\n    var formatRoot = squarify(root, aspectRatio);\n    nestIndex = nestIndex.slice(0, i + 1);\n    this.setState({\n      formatRoot,\n      currentRoot: node,\n      nestIndex\n    });\n  }\n  renderItem(content, nodeProps, isLeaf) {\n    var {\n      isAnimationActive,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      isUpdateAnimationActive,\n      type,\n      animationId,\n      colorPanel,\n      dataKey\n    } = this.props;\n    var {\n      isAnimationFinished\n    } = this.state;\n    var {\n      width,\n      height,\n      x,\n      y,\n      depth\n    } = nodeProps;\n    var translateX = parseInt(\"\".concat((Math.random() * 2 - 1) * width), 10);\n    var event = {};\n    if (isLeaf || type === 'nest') {\n      event = {\n        onMouseEnter: this.handleMouseEnter.bind(this, nodeProps),\n        onMouseLeave: this.handleMouseLeave.bind(this, nodeProps),\n        onClick: this.handleClick.bind(this, nodeProps)\n      };\n    }\n    if (!isAnimationActive) {\n      return /*#__PURE__*/React.createElement(Layer, event, /*#__PURE__*/React.createElement(ContentItemWithEvents, {\n        content: content,\n        dataKey: dataKey,\n        nodeProps: _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive: false,\n          isUpdateAnimationActive: false,\n          width,\n          height,\n          x,\n          y\n        }),\n        type: type,\n        colorPanel: colorPanel\n      }));\n    }\n    return /*#__PURE__*/React.createElement(Animate, {\n      begin: animationBegin,\n      duration: animationDuration,\n      isActive: isAnimationActive,\n      easing: animationEasing,\n      key: \"treemap-\".concat(animationId),\n      from: {\n        x,\n        y,\n        width,\n        height\n      },\n      to: {\n        x,\n        y,\n        width,\n        height\n      },\n      onAnimationStart: this.handleAnimationStart,\n      onAnimationEnd: this.handleAnimationEnd\n    }, _ref4 => {\n      var {\n        x: currX,\n        y: currY,\n        width: currWidth,\n        height: currHeight\n      } = _ref4;\n      return /*#__PURE__*/React.createElement(Animate\n      // @ts-expect-error TODO - fix the type error\n      , {\n        from: \"translate(\".concat(translateX, \"px, \").concat(translateX, \"px)\")\n        // @ts-expect-error TODO - fix the type error\n        ,\n        to: \"translate(0, 0)\",\n        attributeName: \"transform\",\n        begin: animationBegin,\n        easing: animationEasing,\n        isActive: isAnimationActive,\n        duration: animationDuration\n      }, /*#__PURE__*/React.createElement(Layer, event, depth > 2 && !isAnimationFinished ? null : /*#__PURE__*/React.createElement(ContentItemWithEvents, {\n        content: content,\n        dataKey: dataKey,\n        nodeProps: _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive,\n          isUpdateAnimationActive: !isUpdateAnimationActive,\n          width: currWidth,\n          height: currHeight,\n          x: currX,\n          y: currY\n        }),\n        type: type,\n        colorPanel: colorPanel\n      })));\n    });\n  }\n  renderNode(root, node) {\n    var {\n      content,\n      type\n    } = this.props;\n    var nodeProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props, false)), node), {}, {\n      root\n    });\n    var isLeaf = !node.children || !node.children.length;\n    var {\n      currentRoot\n    } = this.state;\n    var isCurrentRootChild = (currentRoot.children || []).filter(item => item.depth === node.depth && item.name === node.name);\n    if (!isCurrentRootChild.length && root.depth && type === 'nest') {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      key: \"recharts-treemap-node-\".concat(nodeProps.x, \"-\").concat(nodeProps.y, \"-\").concat(nodeProps.name),\n      className: \"recharts-treemap-depth-\".concat(node.depth)\n    }, this.renderItem(content, nodeProps, isLeaf), node.children && node.children.length ? node.children.map(child => this.renderNode(node, child)) : null);\n  }\n  renderAllNodes() {\n    var {\n      formatRoot\n    } = this.state;\n    if (!formatRoot) {\n      return null;\n    }\n    return this.renderNode(formatRoot, formatRoot);\n  }\n\n  // render nest treemap\n  renderNestIndex() {\n    var {\n      nameKey,\n      nestIndexContent\n    } = this.props;\n    var {\n      nestIndex\n    } = this.state;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"recharts-treemap-nest-index-wrapper\",\n      style: {\n        marginTop: '8px',\n        textAlign: 'center'\n      }\n    }, nestIndex.map((item, i) => {\n      // TODO need to verify nameKey type\n      var name = get(item, nameKey, 'root');\n      var content = null;\n      if (/*#__PURE__*/React.isValidElement(nestIndexContent)) {\n        content = /*#__PURE__*/React.cloneElement(nestIndexContent, item, i);\n      }\n      if (typeof nestIndexContent === 'function') {\n        content = nestIndexContent(item, i);\n      } else {\n        content = name;\n      }\n      return (\n        /*#__PURE__*/\n        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions\n        React.createElement(\"div\", {\n          onClick: this.handleNestIndex.bind(this, item, i),\n          key: \"nest-index-\".concat(uniqueId()),\n          className: \"recharts-treemap-nest-index-box\",\n          style: {\n            cursor: 'pointer',\n            display: 'inline-block',\n            padding: '0 7px',\n            background: '#000',\n            color: '#fff',\n            marginRight: '3px'\n          }\n        }, content)\n      );\n    }));\n  }\n  render() {\n    var _this$props = this.props,\n      {\n        width,\n        height,\n        className,\n        style,\n        children,\n        type\n      } = _this$props,\n      others = _objectWithoutProperties(_this$props, _excluded);\n    var attrs = filterProps(others, false);\n    return /*#__PURE__*/React.createElement(TooltipPortalContext.Provider, {\n      value: this.state.tooltipPortal\n    }, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: {\n        props: this.props,\n        currentRoot: this.state.currentRoot\n      }\n    }), /*#__PURE__*/React.createElement(RechartsWrapper, {\n      className: className,\n      style: style,\n      width: width,\n      height: height,\n      ref: node => {\n        if (this.state.tooltipPortal == null) {\n          this.setState({\n            tooltipPortal: node\n          });\n        }\n      },\n      onMouseEnter: undefined,\n      onMouseLeave: undefined,\n      onClick: undefined,\n      onMouseMove: undefined,\n      onMouseDown: undefined,\n      onMouseUp: undefined,\n      onContextMenu: undefined,\n      onDoubleClick: undefined,\n      onTouchStart: undefined,\n      onTouchMove: this.handleTouchMove,\n      onTouchEnd: undefined\n    }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n      width: width,\n      height: type === 'nest' ? height - 30 : height\n    }), this.renderAllNodes(), children), type === 'nest' && this.renderNestIndex()));\n  }\n}\n_defineProperty(TreemapWithState, \"displayName\", 'Treemap');\n_defineProperty(TreemapWithState, \"defaultProps\", {\n  aspectRatio: 0.5 * (1 + Math.sqrt(5)),\n  dataKey: 'value',\n  nameKey: 'name',\n  type: 'flat',\n  isAnimationActive: !Global.isSsr,\n  isUpdateAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'linear'\n});\nfunction TreemapDispatchInject(props) {\n  var dispatch = useAppDispatch();\n  return /*#__PURE__*/React.createElement(TreemapWithState, _extends({}, props, {\n    dispatch: dispatch\n  }));\n}\nexport function Treemap(props) {\n  var _props$className;\n  var {\n    width,\n    height\n  } = props;\n  if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(RechartsStoreProvider, {\n    preloadedState: {\n      options\n    },\n    reduxStoreName: (_props$className = props.className) !== null && _props$className !== void 0 ? _props$className : 'Treemap'\n  }, /*#__PURE__*/React.createElement(ReportChartSize, {\n    width: width,\n    height: height\n  }), /*#__PURE__*/React.createElement(ReportChartMargin, {\n    margin: defaultTreemapMargin\n  }), /*#__PURE__*/React.createElement(TreemapDispatchInject, props));\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC;AAC7E,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUR,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,CAACR,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGgB,SAAS,CAACjB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAEM,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AACnR,SAASE,OAAOA,CAACnB,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACc,IAAI,CAACpB,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACmB,MAAM,CAAC,UAAUlB,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACgB,wBAAwB,CAACtB,CAAC,EAAEG,CAAC,CAAC,CAACoB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEtB,CAAC,CAACuB,IAAI,CAACN,KAAK,CAACjB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASwB,aAAaA,CAACzB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,SAAS,CAACR,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIgB,SAAS,CAACd,CAAC,CAAC,GAAGc,SAAS,CAACd,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGgB,OAAO,CAACb,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC,UAAUvB,CAAC,EAAE;MAAEwB,eAAe,CAAC3B,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACsB,yBAAyB,GAAGtB,MAAM,CAACuB,gBAAgB,CAAC7B,CAAC,EAAEM,MAAM,CAACsB,yBAAyB,CAAC3B,CAAC,CAAC,CAAC,GAAGkB,OAAO,CAACb,MAAM,CAACL,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC,UAAUvB,CAAC,EAAE;MAAEG,MAAM,CAACwB,cAAc,CAAC9B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACgB,wBAAwB,CAACrB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAAS2B,eAAeA,CAAC3B,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAG4B,cAAc,CAAC5B,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACwB,cAAc,CAAC9B,CAAC,EAAEG,CAAC,EAAE;IAAE6B,KAAK,EAAE/B,CAAC;IAAEsB,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGlC,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS+B,cAAcA,CAAC9B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG+B,YAAY,CAAClC,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS+B,YAAYA,CAAClC,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACmC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKrC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIkC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKnC,CAAC,GAAGoC,MAAM,GAAGC,MAAM,EAAEvC,CAAC,CAAC;AAAE;AACvT,OAAO,KAAKwC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,OAAO;AACrC,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,KAAK,EAAEC,QAAQ,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,+BAA+B;AAClF,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,uBAAuB,EAAEC,2BAA2B,QAAQ,uBAAuB;AAC5F,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,IAAIC,cAAc,GAAG,OAAO;;AAE5B;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,OAAO,IAAIC,sBAAsB,GAAGA,CAACC,IAAI,EAAEC,WAAW,KAAK;EACzD,OAAO1B,GAAG,CAACyB,IAAI,EAAEC,WAAW,CAAC;AAC/B,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,kBAAkB,EAAE;EACpF,IAAIC,uBAAuB,GAAGxD,SAAS,CAACR,MAAM,GAAG,CAAC,IAAIQ,SAAS,CAAC,CAAC,CAAC,KAAKyD,SAAS,GAAGzD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACpG,OAAO,EAAE,CAAC0D,MAAM,CAACF,uBAAuB,EAAE,WAAW,CAAC,CAACE,MAAM,CAACH,kBAAkB,EAAE,GAAG,CAAC;AACxF,CAAC;AACD,IAAII,OAAO,GAAG;EACZC,SAAS,EAAE,SAAS;EACpBC,uBAAuB,EAAE,MAAM;EAC/BC,yBAAyB,EAAE,CAAC,MAAM,CAAC;EACnCC,sBAAsB,EAAEZ,sBAAsB;EAC9Ca,YAAY,EAAEP;AAChB,CAAC;AACD,OAAO,IAAIQ,WAAW,GAAGC,IAAI,IAAI;EAC/B,IAAI;IACFC,KAAK;IACLC,IAAI;IACJC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC,GAAGN,IAAI;EACR,IAAIO,mBAAmB,GAAGN,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGb,qBAAqB,CAACe,KAAK,EAAEG,wBAAwB,CAAC;EACnG,IAAI;IACFE;EACF,CAAC,GAAGN,IAAI;EACR,IAAIO,UAAU,GAAGR,KAAK,GAAG,CAAC;EAC1B,IAAIS,gBAAgB,GAAGF,QAAQ,IAAIA,QAAQ,CAAClF,MAAM,GAAGkF,QAAQ,CAACG,GAAG,CAAC,CAACC,KAAK,EAAE3F,CAAC,KAAK8E,WAAW,CAAC;IAC1FE,KAAK,EAAEQ,UAAU;IACjBP,IAAI,EAAEU,KAAK;IACXT,KAAK,EAAElF,CAAC;IACRmF,OAAO;IACPC,OAAO;IACPC,wBAAwB,EAAEC;EAC5B,CAAC,CAAC,CAAC,GAAG,IAAI;EACV,IAAIM,SAAS;EACb,IAAIL,QAAQ,IAAIA,QAAQ,CAAClF,MAAM,EAAE;IAC/BuF,SAAS,GAAGH,gBAAgB,CAACI,MAAM,CAAC,CAACC,MAAM,EAAEH,KAAK,KAAKG,MAAM,GAAGH,KAAK,CAAC5B,cAAc,CAAC,EAAE,CAAC,CAAC;EAC3F,CAAC,MAAM;IACL;IACA6B,SAAS,GAAG7C,KAAK,CAACkC,IAAI,CAACE,OAAO,CAAC,CAAC,IAAIF,IAAI,CAACE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGF,IAAI,CAACE,OAAO,CAAC;EAC5E;EACA,OAAO9D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAChDM,QAAQ,EAAEE,gBAAgB;IAC1B;IACAM,IAAI,EAAElD,iBAAiB,CAACoC,IAAI,EAAEG,OAAO,EAAE,EAAE,CAAC;IAC1C,CAACrB,cAAc,GAAG6B,SAAS;IAC3BZ,KAAK;IACLE,KAAK;IACLc,YAAY,EAAEV;EAChB,CAAC,CAAC;AACJ,CAAC;AACD,IAAIW,UAAU,GAAGhB,IAAI,KAAK;EACxBiB,CAAC,EAAEjB,IAAI,CAACiB,CAAC;EACTC,CAAC,EAAElB,IAAI,CAACkB,CAAC;EACTC,KAAK,EAAEnB,IAAI,CAACmB,KAAK;EACjBC,MAAM,EAAEpB,IAAI,CAACoB;AACf,CAAC,CAAC;;AAEF;AACA,IAAIC,iBAAiB,GAAGA,CAACf,QAAQ,EAAEgB,cAAc,KAAK;EACpD,IAAIC,KAAK,GAAGD,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc;EACnD,OAAOhB,QAAQ,CAACG,GAAG,CAACC,KAAK,IAAI;IAC3B,IAAIc,IAAI,GAAGd,KAAK,CAAC5B,cAAc,CAAC,GAAGyC,KAAK;IACxC,OAAOnF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDc,IAAI,EAAE1D,KAAK,CAAC0D,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGA;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,IAAIC,aAAa,GAAGA,CAACC,GAAG,EAAEC,UAAU,EAAEC,WAAW,KAAK;EACpD,IAAIC,UAAU,GAAGF,UAAU,GAAGA,UAAU;EACxC,IAAIG,OAAO,GAAGJ,GAAG,CAACF,IAAI,GAAGE,GAAG,CAACF,IAAI;EACjC,IAAI;IACFO,GAAG;IACHC;EACF,CAAC,GAAGN,GAAG,CAACd,MAAM,CAAC,CAACC,MAAM,EAAEH,KAAK,MAAM;IACjCqB,GAAG,EAAEE,IAAI,CAACF,GAAG,CAAClB,MAAM,CAACkB,GAAG,EAAErB,KAAK,CAACc,IAAI,CAAC;IACrCQ,GAAG,EAAEC,IAAI,CAACD,GAAG,CAACnB,MAAM,CAACmB,GAAG,EAAEtB,KAAK,CAACc,IAAI;EACtC,CAAC,CAAC,EAAE;IACFO,GAAG,EAAEG,QAAQ;IACbF,GAAG,EAAE;EACP,CAAC,CAAC;EACF,OAAOF,OAAO,GAAGG,IAAI,CAACD,GAAG,CAACH,UAAU,GAAGG,GAAG,GAAGJ,WAAW,GAAGE,OAAO,EAAEA,OAAO,IAAID,UAAU,GAAGE,GAAG,GAAGH,WAAW,CAAC,CAAC,GAAGM,QAAQ;AAC5H,CAAC;AACD,IAAIC,kBAAkB,GAAGA,CAACT,GAAG,EAAEC,UAAU,EAAES,UAAU,EAAEC,OAAO,KAAK;EACjE,IAAIC,SAAS,GAAGX,UAAU,GAAGM,IAAI,CAACM,KAAK,CAACb,GAAG,CAACF,IAAI,GAAGG,UAAU,CAAC,GAAG,CAAC;EAClE,IAAIU,OAAO,IAAIC,SAAS,GAAGF,UAAU,CAAChB,MAAM,EAAE;IAC5CkB,SAAS,GAAGF,UAAU,CAAChB,MAAM;EAC/B;EACA,IAAIoB,IAAI,GAAGJ,UAAU,CAACnB,CAAC;EACvB,IAAIP,KAAK;EACT,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAE0H,GAAG,GAAGf,GAAG,CAACtG,MAAM,EAAEL,CAAC,GAAG0H,GAAG,EAAE1H,CAAC,EAAE,EAAE;IAC9C2F,KAAK,GAAGgB,GAAG,CAAC3G,CAAC,CAAC;IACd2F,KAAK,CAACO,CAAC,GAAGuB,IAAI;IACd9B,KAAK,CAACQ,CAAC,GAAGkB,UAAU,CAAClB,CAAC;IACtBR,KAAK,CAACU,MAAM,GAAGkB,SAAS;IACxB5B,KAAK,CAACS,KAAK,GAAGc,IAAI,CAACF,GAAG,CAACO,SAAS,GAAGL,IAAI,CAACM,KAAK,CAAC7B,KAAK,CAACc,IAAI,GAAGc,SAAS,CAAC,GAAG,CAAC,EAAEF,UAAU,CAACnB,CAAC,GAAGmB,UAAU,CAACjB,KAAK,GAAGqB,IAAI,CAAC;IAClHA,IAAI,IAAI9B,KAAK,CAACS,KAAK;EACrB;EACA;EACAT,KAAK,CAACS,KAAK,IAAIiB,UAAU,CAACnB,CAAC,GAAGmB,UAAU,CAACjB,KAAK,GAAGqB,IAAI;EACrD,OAAOpG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgG,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDlB,CAAC,EAAEkB,UAAU,CAAClB,CAAC,GAAGoB,SAAS;IAC3BlB,MAAM,EAAEgB,UAAU,CAAChB,MAAM,GAAGkB;EAC9B,CAAC,CAAC;AACJ,CAAC;AACD,IAAII,gBAAgB,GAAGA,CAAChB,GAAG,EAAEC,UAAU,EAAES,UAAU,EAAEC,OAAO,KAAK;EAC/D,IAAIM,QAAQ,GAAGhB,UAAU,GAAGM,IAAI,CAACM,KAAK,CAACb,GAAG,CAACF,IAAI,GAAGG,UAAU,CAAC,GAAG,CAAC;EACjE,IAAIU,OAAO,IAAIM,QAAQ,GAAGP,UAAU,CAACjB,KAAK,EAAE;IAC1CwB,QAAQ,GAAGP,UAAU,CAACjB,KAAK;EAC7B;EACA,IAAIyB,IAAI,GAAGR,UAAU,CAAClB,CAAC;EACvB,IAAIR,KAAK;EACT,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAE0H,GAAG,GAAGf,GAAG,CAACtG,MAAM,EAAEL,CAAC,GAAG0H,GAAG,EAAE1H,CAAC,EAAE,EAAE;IAC9C2F,KAAK,GAAGgB,GAAG,CAAC3G,CAAC,CAAC;IACd2F,KAAK,CAACO,CAAC,GAAGmB,UAAU,CAACnB,CAAC;IACtBP,KAAK,CAACQ,CAAC,GAAG0B,IAAI;IACdlC,KAAK,CAACS,KAAK,GAAGwB,QAAQ;IACtBjC,KAAK,CAACU,MAAM,GAAGa,IAAI,CAACF,GAAG,CAACY,QAAQ,GAAGV,IAAI,CAACM,KAAK,CAAC7B,KAAK,CAACc,IAAI,GAAGmB,QAAQ,CAAC,GAAG,CAAC,EAAEP,UAAU,CAAClB,CAAC,GAAGkB,UAAU,CAAChB,MAAM,GAAGwB,IAAI,CAAC;IAClHA,IAAI,IAAIlC,KAAK,CAACU,MAAM;EACtB;EACA,IAAIV,KAAK,EAAE;IACTA,KAAK,CAACU,MAAM,IAAIgB,UAAU,CAAClB,CAAC,GAAGkB,UAAU,CAAChB,MAAM,GAAGwB,IAAI;EACzD;EACA,OAAOxG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgG,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDnB,CAAC,EAAEmB,UAAU,CAACnB,CAAC,GAAG0B,QAAQ;IAC1BxB,KAAK,EAAEiB,UAAU,CAACjB,KAAK,GAAGwB;EAC5B,CAAC,CAAC;AACJ,CAAC;AACD,IAAIE,QAAQ,GAAGA,CAACnB,GAAG,EAAEC,UAAU,EAAES,UAAU,EAAEC,OAAO,KAAK;EACvD,IAAIV,UAAU,KAAKS,UAAU,CAACjB,KAAK,EAAE;IACnC,OAAOgB,kBAAkB,CAACT,GAAG,EAAEC,UAAU,EAAES,UAAU,EAAEC,OAAO,CAAC;EACjE;EACA,OAAOK,gBAAgB,CAAChB,GAAG,EAAEC,UAAU,EAAES,UAAU,EAAEC,OAAO,CAAC;AAC/D,CAAC;;AAED;AACA,IAAIS,QAAQ,GAAGA,CAAC9C,IAAI,EAAE4B,WAAW,KAAK;EACpC,IAAI;IACFtB;EACF,CAAC,GAAGN,IAAI;EACR,IAAIM,QAAQ,IAAIA,QAAQ,CAAClF,MAAM,EAAE;IAC/B,IAAI2H,IAAI,GAAG/B,UAAU,CAAChB,IAAI,CAAC;IAC3B;IACA,IAAI0B,GAAG,GAAG,EAAE;IACZ,IAAIsB,IAAI,GAAGd,QAAQ,CAAC,CAAC;IACrB,IAAIxB,KAAK,EAAEuC,KAAK,CAAC,CAAC;IAClB,IAAIC,IAAI,GAAGjB,IAAI,CAACF,GAAG,CAACgB,IAAI,CAAC5B,KAAK,EAAE4B,IAAI,CAAC3B,MAAM,CAAC,CAAC,CAAC;IAC9C,IAAI+B,aAAa,GAAG9B,iBAAiB,CAACf,QAAQ,EAAEyC,IAAI,CAAC5B,KAAK,GAAG4B,IAAI,CAAC3B,MAAM,GAAGpB,IAAI,CAAClB,cAAc,CAAC,CAAC;IAChG,IAAIsE,YAAY,GAAGD,aAAa,CAACE,KAAK,CAAC,CAAC;IACxC3B,GAAG,CAACF,IAAI,GAAG,CAAC;IACZ,OAAO4B,YAAY,CAAChI,MAAM,GAAG,CAAC,EAAE;MAC9B;MACA;MACAsG,GAAG,CAACvF,IAAI,CAACuE,KAAK,GAAG0C,YAAY,CAAC,CAAC,CAAC,CAAC;MACjC1B,GAAG,CAACF,IAAI,IAAId,KAAK,CAACc,IAAI;MACtByB,KAAK,GAAGxB,aAAa,CAACC,GAAG,EAAEwB,IAAI,EAAEtB,WAAW,CAAC;MAC7C,IAAIqB,KAAK,IAAID,IAAI,EAAE;QACjB;QACAI,YAAY,CAACE,KAAK,CAAC,CAAC;QACpBN,IAAI,GAAGC,KAAK;MACd,CAAC,MAAM;QACL;QACAvB,GAAG,CAACF,IAAI,IAAIE,GAAG,CAAC6B,GAAG,CAAC,CAAC,CAAC/B,IAAI;QAC1BuB,IAAI,GAAGF,QAAQ,CAACnB,GAAG,EAAEwB,IAAI,EAAEH,IAAI,EAAE,KAAK,CAAC;QACvCG,IAAI,GAAGjB,IAAI,CAACF,GAAG,CAACgB,IAAI,CAAC5B,KAAK,EAAE4B,IAAI,CAAC3B,MAAM,CAAC;QACxCM,GAAG,CAACtG,MAAM,GAAGsG,GAAG,CAACF,IAAI,GAAG,CAAC;QACzBwB,IAAI,GAAGd,QAAQ;MACjB;IACF;IACA,IAAIR,GAAG,CAACtG,MAAM,EAAE;MACd2H,IAAI,GAAGF,QAAQ,CAACnB,GAAG,EAAEwB,IAAI,EAAEH,IAAI,EAAE,IAAI,CAAC;MACtCrB,GAAG,CAACtG,MAAM,GAAGsG,GAAG,CAACF,IAAI,GAAG,CAAC;IAC3B;IACA,OAAOpF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDM,QAAQ,EAAE6C,aAAa,CAAC1C,GAAG,CAAC+C,CAAC,IAAIV,QAAQ,CAACU,CAAC,EAAE5B,WAAW,CAAC;IAC3D,CAAC,CAAC;EACJ;EACA,OAAO5B,IAAI;AACb,CAAC;AACD,IAAIyD,YAAY,GAAG;EACjBC,mBAAmB,EAAE,KAAK;EAC1BC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE;AACb,CAAC;AACD,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAI;IACFC,OAAO;IACPC,SAAS;IACTC,IAAI;IACJC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZC;EACF,CAAC,GAAGP,KAAK;EACT,IAAI,aAAa3G,KAAK,CAACmH,cAAc,CAACP,OAAO,CAAC,EAAE;IAC9C,OAAO,aAAa5G,KAAK,CAACoH,aAAa,CAAChH,KAAK,EAAE;MAC7C4G,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA;IACX,CAAC,EAAE,aAAalH,KAAK,CAACqH,YAAY,CAACT,OAAO,EAAEC,SAAS,CAAC,CAAC;EACzD;EACA,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;IACjC,OAAO,aAAa5G,KAAK,CAACoH,aAAa,CAAChH,KAAK,EAAE;MAC7C4G,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BC,OAAO,EAAEA;IACX,CAAC,EAAEN,OAAO,CAACC,SAAS,CAAC,CAAC;EACxB;EACA;EACA,IAAI;IACFhD,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNnB;EACF,CAAC,GAAGgE,SAAS;EACb,IAAIS,KAAK,GAAG,IAAI;EAChB,IAAIvD,KAAK,GAAG,EAAE,IAAIC,MAAM,GAAG,EAAE,IAAI6C,SAAS,CAAC3D,QAAQ,IAAI4D,IAAI,KAAK,MAAM,EAAE;IACtEQ,KAAK,GAAG,aAAatH,KAAK,CAACoH,aAAa,CAAC9G,OAAO,EAAE;MAChDiH,MAAM,EAAE,CAAC;QACP1D,CAAC,EAAEA,CAAC,GAAG,CAAC;QACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG;MAClB,CAAC,EAAE;QACDH,CAAC,EAAEA,CAAC,GAAG,CAAC;QACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG;MACtB,CAAC,EAAE;QACDH,CAAC,EAAEA,CAAC,GAAG,CAAC;QACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG;MACtB,CAAC;IACH,CAAC,CAAC;EACJ;EACA,IAAIwD,IAAI,GAAG,IAAI;EACf,IAAIC,QAAQ,GAAG7G,aAAa,CAACiG,SAAS,CAACnD,IAAI,CAAC;EAC5C,IAAIK,KAAK,GAAG,EAAE,IAAIC,MAAM,GAAG,EAAE,IAAIyD,QAAQ,CAAC1D,KAAK,GAAGA,KAAK,IAAI0D,QAAQ,CAACzD,MAAM,GAAGA,MAAM,EAAE;IACnFwD,IAAI,GAAG,aAAaxH,KAAK,CAACoH,aAAa,CAAC,MAAM,EAAE;MAC9CvD,CAAC,EAAEA,CAAC,GAAG,CAAC;MACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MACrB0D,QAAQ,EAAE;IACZ,CAAC,EAAEb,SAAS,CAACnD,IAAI,CAAC;EACpB;EACA,IAAIiE,MAAM,GAAGZ,UAAU,IAAItG,WAAW;EACtC,OAAO,aAAaT,KAAK,CAACoH,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAapH,KAAK,CAACoH,aAAa,CAAC7G,SAAS,EAAElC,QAAQ,CAAC;IACtGuJ,IAAI,EAAEf,SAAS,CAAClE,KAAK,GAAG,CAAC,GAAGgF,MAAM,CAAC9E,KAAK,GAAG8E,MAAM,CAAC3J,MAAM,CAAC,GAAG,qBAAqB;IACjF6J,MAAM,EAAE;EACV,CAAC,EAAE3H,IAAI,CAAC2G,SAAS,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE;IAChCG,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA,OAAO;IAChB,0BAA0B,EAAEL,SAAS,CAAClD;EACxC,CAAC,CAAC,CAAC,EAAE2D,KAAK,EAAEE,IAAI,CAAC;AACnB;AACA,SAASM,qBAAqBA,CAACC,KAAK,EAAE;EACpC,IAAIC,QAAQ,GAAGzG,cAAc,CAAC,CAAC;EAC/B,IAAI0G,gBAAgB,GAAGF,KAAK,CAAClB,SAAS,GAAG;IACvChD,CAAC,EAAEkE,KAAK,CAAClB,SAAS,CAAChD,CAAC,GAAGkE,KAAK,CAAClB,SAAS,CAAC9C,KAAK,GAAG,CAAC;IAChDD,CAAC,EAAEiE,KAAK,CAAClB,SAAS,CAAC/C,CAAC,GAAGiE,KAAK,CAAClB,SAAS,CAAC7C,MAAM,GAAG;EAClD,CAAC,GAAG,IAAI;EACR,IAAIgD,YAAY,GAAGA,CAAA,KAAM;IACvBgB,QAAQ,CAAC5G,2BAA2B,CAAC;MACnCS,WAAW,EAAEkG,KAAK,CAAClB,SAAS,CAAClD,YAAY;MACzCuE,aAAa,EAAEH,KAAK,CAACjF,OAAO;MAC5BmF;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EACD,IAAIhB,YAAY,GAAGA,CAAA,KAAM;IACvB;IACA;EAAA,CACD;EACD,IAAIC,OAAO,GAAGA,CAAA,KAAM;IAClBc,QAAQ,CAAC7G,uBAAuB,CAAC;MAC/BU,WAAW,EAAEkG,KAAK,CAAClB,SAAS,CAAClD,YAAY;MACzCuE,aAAa,EAAEH,KAAK,CAACjF,OAAO;MAC5BmF;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EACD,OAAO,aAAajI,KAAK,CAACoH,aAAa,CAACV,WAAW,EAAErI,QAAQ,CAAC,CAAC,CAAC,EAAE0J,KAAK,EAAE;IACvEf,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC;AACL;AACA,SAASiB,uBAAuBA,CAACC,KAAK,EAAE;EACtC,IAAI;IACFL,KAAK;IACLvB;EACF,CAAC,GAAG4B,KAAK;EACT,IAAI;IACFtF,OAAO;IACPC,OAAO;IACP8E,MAAM;IACND;EACF,CAAC,GAAGG,KAAK;EACT,OAAO;IACLM,iBAAiB,EAAE7B,WAAW;IAC9B8B,SAAS,EAAErG,SAAS;IACpB;IACAsG,QAAQ,EAAE;MACRV,MAAM;MACNW,WAAW,EAAEvG,SAAS;MACtB2F,IAAI;MACJ9E,OAAO;MACPC,OAAO;MACPW,IAAI,EAAEzB,SAAS;MACf;MACAwG,IAAI,EAAE,KAAK;MACX3B,IAAI,EAAE7E,SAAS;MACfyG,KAAK,EAAEd,IAAI;MACXe,IAAI,EAAE;IACR;EACF,CAAC;AACH;;AAEA;AACA,IAAIC,oBAAoB,GAAG;EACzBC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,SAAShJ,aAAa,CAAC;EAC3CiJ,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAG1K,SAAS,CAAC;IACnBU,eAAe,CAAC,IAAI,EAAE,OAAO,EAAEF,aAAa,CAAC,CAAC,CAAC,EAAEqH,YAAY,CAAC,CAAC;IAC/DnH,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,MAAM;MAChD,IAAI;QACFiK;MACF,CAAC,GAAG,IAAI,CAACpB,KAAK;MACd,IAAI,CAACqB,QAAQ,CAAC;QACZ9C,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI,OAAO6C,cAAc,KAAK,UAAU,EAAE;QACxCA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFjK,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,MAAM;MAClD,IAAI;QACFmK;MACF,CAAC,GAAG,IAAI,CAACtB,KAAK;MACd,IAAI,CAACqB,QAAQ,CAAC;QACZ9C,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI,OAAO+C,gBAAgB,KAAK,UAAU,EAAE;QAC1CA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACFnK,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAACoK,MAAM,EAAE/L,CAAC,KAAK;MACtD,IAAIgM,UAAU,GAAGhM,CAAC,CAACiM,OAAO,CAAC,CAAC,CAAC;MAC7B,IAAIC,MAAM,GAAGC,QAAQ,CAACC,gBAAgB,CAACJ,UAAU,CAACK,OAAO,EAAEL,UAAU,CAACM,OAAO,CAAC;MAC9E,IAAI,CAACJ,MAAM,IAAI,CAACA,MAAM,CAACK,YAAY,EAAE;QACnC;MACF;MACA,IAAIC,SAAS,GAAGN,MAAM,CAACK,YAAY,CAAC,0BAA0B,CAAC;MAC/D,IAAIE,UAAU,GAAGrI,sBAAsB,CAAC,IAAI,CAACsI,KAAK,CAAC1D,UAAU,EAAEwD,SAAS,CAAC;MACzE,IAAI,CAACC,UAAU,EAAE;QACf;MACF;MACA,IAAI;QACFlH,OAAO;QACPkF;MACF,CAAC,GAAG,IAAI,CAACD,KAAK;MACd,IAAIE,gBAAgB,GAAG;QACrBpE,CAAC,EAAEmG,UAAU,CAACnG,CAAC,GAAGmG,UAAU,CAACjG,KAAK,GAAG,CAAC;QACtCD,CAAC,EAAEkG,UAAU,CAAClG,CAAC,GAAGkG,UAAU,CAAChG,MAAM,GAAG;MACxC,CAAC;;MAED;MACA;MACAgE,QAAQ,CAAC5G,2BAA2B,CAAC;QACnCS,WAAW,EAAEkI,SAAS;QACtB7B,aAAa,EAAEpF,OAAO;QACtBmF;MACF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;EACA,OAAOiC,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACpD,IAAID,SAAS,CAACvI,IAAI,KAAKwI,SAAS,CAACC,QAAQ,IAAIF,SAAS,CAACrD,IAAI,KAAKsD,SAAS,CAACE,QAAQ,IAAIH,SAAS,CAACpG,KAAK,KAAKqG,SAAS,CAACG,SAAS,IAAIJ,SAAS,CAACnG,MAAM,KAAKoG,SAAS,CAACI,UAAU,IAAIL,SAAS,CAACrH,OAAO,KAAKsH,SAAS,CAACK,WAAW,IAAIN,SAAS,CAAC3F,WAAW,KAAK4F,SAAS,CAACM,eAAe,EAAE;MAChR,IAAIC,IAAI,GAAGlI,WAAW,CAAC;QACrBE,KAAK,EAAE,CAAC;QACR;QACAC,IAAI,EAAE;UACJM,QAAQ,EAAEiH,SAAS,CAACvI,IAAI;UACxBiC,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJC,KAAK,EAAEoG,SAAS,CAACpG,KAAK;UACtBC,MAAM,EAAEmG,SAAS,CAACnG;QACpB,CAAC;QACDnB,KAAK,EAAE,CAAC;QACRC,OAAO,EAAEqH,SAAS,CAACrH,OAAO;QAC1BC,OAAO,EAAEoH,SAAS,CAACpH;MACrB,CAAC,CAAC;MACF,IAAIwD,UAAU,GAAGb,QAAQ,CAACiF,IAAI,EAAER,SAAS,CAAC3F,WAAW,CAAC;MACtD,OAAOxF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoL,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QACrD7D,UAAU;QACVC,WAAW,EAAEmE,IAAI;QACjBlE,SAAS,EAAE,CAACkE,IAAI,CAAC;QACjBD,eAAe,EAAEP,SAAS,CAAC3F,WAAW;QACtC6F,QAAQ,EAAEF,SAAS,CAACvI,IAAI;QACxB2I,SAAS,EAAEJ,SAAS,CAACpG,KAAK;QAC1ByG,UAAU,EAAEL,SAAS,CAACnG,MAAM;QAC5ByG,WAAW,EAAEN,SAAS,CAACrH,OAAO;QAC9BwH,QAAQ,EAAEH,SAAS,CAACrD;MACtB,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACA8D,gBAAgBA,CAAChI,IAAI,EAAErF,CAAC,EAAE;IACxBA,CAAC,CAACsN,OAAO,CAAC,CAAC;IACX,IAAI;MACF7D;IACF,CAAC,GAAG,IAAI,CAACe,KAAK;IACd,IAAIf,YAAY,EAAE;MAChBA,YAAY,CAACpE,IAAI,EAAErF,CAAC,CAAC;IACvB;EACF;EACAuN,gBAAgBA,CAAClI,IAAI,EAAErF,CAAC,EAAE;IACxBA,CAAC,CAACsN,OAAO,CAAC,CAAC;IACX,IAAI;MACF5D;IACF,CAAC,GAAG,IAAI,CAACc,KAAK;IACd,IAAId,YAAY,EAAE;MAChBA,YAAY,CAACrE,IAAI,EAAErF,CAAC,CAAC;IACvB;EACF;EACAwN,WAAWA,CAACnI,IAAI,EAAE;IAChB,IAAI;MACFsE,OAAO;MACPJ;IACF,CAAC,GAAG,IAAI,CAACiB,KAAK;IACd,IAAIjB,IAAI,KAAK,MAAM,IAAIlE,IAAI,CAACM,QAAQ,EAAE;MACpC,IAAI;QACFa,KAAK;QACLC,MAAM;QACNlB,OAAO;QACPC,OAAO;QACPyB;MACF,CAAC,GAAG,IAAI,CAACuD,KAAK;MACd,IAAI4C,IAAI,GAAGlI,WAAW,CAAC;QACrBE,KAAK,EAAE,CAAC;QACRC,IAAI,EAAE5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/CiB,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJC,KAAK;UACLC;QACF,CAAC,CAAC;QACFnB,KAAK,EAAE,CAAC;QACRC,OAAO;QACPC,OAAO;QACP;QACAC,wBAAwB,EAAEJ,IAAI,CAACe;MACjC,CAAC,CAAC;MACF,IAAI4C,UAAU,GAAGb,QAAQ,CAACiF,IAAI,EAAEnG,WAAW,CAAC;MAC5C,IAAI;QACFiC;MACF,CAAC,GAAG,IAAI,CAACwD,KAAK;MACdxD,SAAS,CAAC1H,IAAI,CAAC6D,IAAI,CAAC;MACpB,IAAI,CAACwG,QAAQ,CAAC;QACZ7C,UAAU;QACVC,WAAW,EAAEmE,IAAI;QACjBlE;MACF,CAAC,CAAC;IACJ;IACA,IAAIS,OAAO,EAAE;MACXA,OAAO,CAACtE,IAAI,CAAC;IACf;EACF;EACAoI,eAAeA,CAACpI,IAAI,EAAEjF,CAAC,EAAE;IACvB,IAAI;MACF8I;IACF,CAAC,GAAG,IAAI,CAACwD,KAAK;IACd,IAAI;MACFlG,KAAK;MACLC,MAAM;MACNlB,OAAO;MACPC,OAAO;MACPyB;IACF,CAAC,GAAG,IAAI,CAACuD,KAAK;IACd,IAAI4C,IAAI,GAAGlI,WAAW,CAAC;MACrBE,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/CiB,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,KAAK;QACLC;MACF,CAAC,CAAC;MACFnB,KAAK,EAAE,CAAC;MACRC,OAAO;MACPC,OAAO;MACP;MACAC,wBAAwB,EAAEJ,IAAI,CAACe;IACjC,CAAC,CAAC;IACF,IAAI4C,UAAU,GAAGb,QAAQ,CAACiF,IAAI,EAAEnG,WAAW,CAAC;IAC5CiC,SAAS,GAAGA,SAAS,CAACR,KAAK,CAAC,CAAC,EAAEtI,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,CAACyL,QAAQ,CAAC;MACZ7C,UAAU;MACVC,WAAW,EAAE5D,IAAI;MACjB6D;IACF,CAAC,CAAC;EACJ;EACAwE,UAAUA,CAACrE,OAAO,EAAEC,SAAS,EAAEqE,MAAM,EAAE;IACrC,IAAI;MACFC,iBAAiB;MACjBC,cAAc;MACdC,iBAAiB;MACjBC,eAAe;MACfC,uBAAuB;MACvBzE,IAAI;MACJ0E,WAAW;MACXzE,UAAU;MACVjE;IACF,CAAC,GAAG,IAAI,CAACiF,KAAK;IACd,IAAI;MACFzB;IACF,CAAC,GAAG,IAAI,CAAC2D,KAAK;IACd,IAAI;MACFlG,KAAK;MACLC,MAAM;MACNH,CAAC;MACDC,CAAC;MACDnB;IACF,CAAC,GAAGkE,SAAS;IACb,IAAI4E,UAAU,GAAGC,QAAQ,CAAC,EAAE,CAACxJ,MAAM,CAAC,CAAC2C,IAAI,CAAC8G,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI5H,KAAK,CAAC,EAAE,EAAE,CAAC;IACzE,IAAI6H,KAAK,GAAG,CAAC,CAAC;IACd,IAAIV,MAAM,IAAIpE,IAAI,KAAK,MAAM,EAAE;MAC7B8E,KAAK,GAAG;QACN5E,YAAY,EAAE,IAAI,CAAC4D,gBAAgB,CAACrM,IAAI,CAAC,IAAI,EAAEsI,SAAS,CAAC;QACzDI,YAAY,EAAE,IAAI,CAAC6D,gBAAgB,CAACvM,IAAI,CAAC,IAAI,EAAEsI,SAAS,CAAC;QACzDK,OAAO,EAAE,IAAI,CAAC6D,WAAW,CAACxM,IAAI,CAAC,IAAI,EAAEsI,SAAS;MAChD,CAAC;IACH;IACA,IAAI,CAACsE,iBAAiB,EAAE;MACtB,OAAO,aAAanL,KAAK,CAACoH,aAAa,CAAChH,KAAK,EAAEwL,KAAK,EAAE,aAAa5L,KAAK,CAACoH,aAAa,CAACU,qBAAqB,EAAE;QAC5GlB,OAAO,EAAEA,OAAO;QAChB9D,OAAO,EAAEA,OAAO;QAChB+D,SAAS,EAAE7H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6H,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACzDsE,iBAAiB,EAAE,KAAK;UACxBI,uBAAuB,EAAE,KAAK;UAC9BxH,KAAK;UACLC,MAAM;UACNH,CAAC;UACDC;QACF,CAAC,CAAC;QACFgD,IAAI,EAAEA,IAAI;QACVC,UAAU,EAAEA;MACd,CAAC,CAAC,CAAC;IACL;IACA,OAAO,aAAa/G,KAAK,CAACoH,aAAa,CAAC3F,OAAO,EAAE;MAC/CoK,KAAK,EAAET,cAAc;MACrBU,QAAQ,EAAET,iBAAiB;MAC3BU,QAAQ,EAAEZ,iBAAiB;MAC3Ba,MAAM,EAAEV,eAAe;MACvBW,GAAG,EAAE,UAAU,CAAC/J,MAAM,CAACsJ,WAAW,CAAC;MACnCU,IAAI,EAAE;QACJrI,CAAC;QACDC,CAAC;QACDC,KAAK;QACLC;MACF,CAAC;MACDmI,EAAE,EAAE;QACFtI,CAAC;QACDC,CAAC;QACDC,KAAK;QACLC;MACF,CAAC;MACDqF,gBAAgB,EAAE,IAAI,CAAC+C,oBAAoB;MAC3CjD,cAAc,EAAE,IAAI,CAACkD;IACvB,CAAC,EAAEC,KAAK,IAAI;MACV,IAAI;QACFzI,CAAC,EAAE0I,KAAK;QACRzI,CAAC,EAAE0I,KAAK;QACRzI,KAAK,EAAE0I,SAAS;QAChBzI,MAAM,EAAE0I;MACV,CAAC,GAAGJ,KAAK;MACT,OAAO,aAAatM,KAAK,CAACoH,aAAa,CAAC3F;MACxC;MAAA,EACE;QACAyK,IAAI,EAAE,YAAY,CAAChK,MAAM,CAACuJ,UAAU,EAAE,MAAM,CAAC,CAACvJ,MAAM,CAACuJ,UAAU,EAAE,KAAK;QACtE;QAAA;;QAEAU,EAAE,EAAE,iBAAiB;QACrBQ,aAAa,EAAE,WAAW;QAC1Bd,KAAK,EAAET,cAAc;QACrBY,MAAM,EAAEV,eAAe;QACvBS,QAAQ,EAAEZ,iBAAiB;QAC3BW,QAAQ,EAAET;MACZ,CAAC,EAAE,aAAarL,KAAK,CAACoH,aAAa,CAAChH,KAAK,EAAEwL,KAAK,EAAEjJ,KAAK,GAAG,CAAC,IAAI,CAAC2D,mBAAmB,GAAG,IAAI,GAAG,aAAatG,KAAK,CAACoH,aAAa,CAACU,qBAAqB,EAAE;QACnJlB,OAAO,EAAEA,OAAO;QAChB9D,OAAO,EAAEA,OAAO;QAChB+D,SAAS,EAAE7H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6H,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACzDsE,iBAAiB;UACjBI,uBAAuB,EAAE,CAACA,uBAAuB;UACjDxH,KAAK,EAAE0I,SAAS;UAChBzI,MAAM,EAAE0I,UAAU;UAClB7I,CAAC,EAAE0I,KAAK;UACRzI,CAAC,EAAE0I;QACL,CAAC,CAAC;QACF1F,IAAI,EAAEA,IAAI;QACVC,UAAU,EAAEA;MACd,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EACA6F,UAAUA,CAACjC,IAAI,EAAE/H,IAAI,EAAE;IACrB,IAAI;MACFgE,OAAO;MACPE;IACF,CAAC,GAAG,IAAI,CAACiB,KAAK;IACd,IAAIlB,SAAS,GAAG7H,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8B,WAAW,CAAC,IAAI,CAACiH,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEnF,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACxG+H;IACF,CAAC,CAAC;IACF,IAAIO,MAAM,GAAG,CAACtI,IAAI,CAACM,QAAQ,IAAI,CAACN,IAAI,CAACM,QAAQ,CAAClF,MAAM;IACpD,IAAI;MACFwI;IACF,CAAC,GAAG,IAAI,CAACyD,KAAK;IACd,IAAI4C,kBAAkB,GAAG,CAACrG,WAAW,CAACtD,QAAQ,IAAI,EAAE,EAAEtE,MAAM,CAACkO,IAAI,IAAIA,IAAI,CAACnK,KAAK,KAAKC,IAAI,CAACD,KAAK,IAAImK,IAAI,CAACpJ,IAAI,KAAKd,IAAI,CAACc,IAAI,CAAC;IAC1H,IAAI,CAACmJ,kBAAkB,CAAC7O,MAAM,IAAI2M,IAAI,CAAChI,KAAK,IAAImE,IAAI,KAAK,MAAM,EAAE;MAC/D,OAAO,IAAI;IACb;IACA,OAAO,aAAa9G,KAAK,CAACoH,aAAa,CAAChH,KAAK,EAAE;MAC7C6L,GAAG,EAAE,wBAAwB,CAAC/J,MAAM,CAAC2E,SAAS,CAAChD,CAAC,EAAE,GAAG,CAAC,CAAC3B,MAAM,CAAC2E,SAAS,CAAC/C,CAAC,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAAC2E,SAAS,CAACnD,IAAI,CAAC;MACtGqJ,SAAS,EAAE,yBAAyB,CAAC7K,MAAM,CAACU,IAAI,CAACD,KAAK;IACxD,CAAC,EAAE,IAAI,CAACsI,UAAU,CAACrE,OAAO,EAAEC,SAAS,EAAEqE,MAAM,CAAC,EAAEtI,IAAI,CAACM,QAAQ,IAAIN,IAAI,CAACM,QAAQ,CAAClF,MAAM,GAAG4E,IAAI,CAACM,QAAQ,CAACG,GAAG,CAACC,KAAK,IAAI,IAAI,CAACsJ,UAAU,CAAChK,IAAI,EAAEU,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;EAC1J;EACA0J,cAAcA,CAAA,EAAG;IACf,IAAI;MACFzG;IACF,CAAC,GAAG,IAAI,CAAC0D,KAAK;IACd,IAAI,CAAC1D,UAAU,EAAE;MACf,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAACqG,UAAU,CAACrG,UAAU,EAAEA,UAAU,CAAC;EAChD;;EAEA;EACA0G,eAAeA,CAAA,EAAG;IAChB,IAAI;MACFlK,OAAO;MACPmK;IACF,CAAC,GAAG,IAAI,CAACnF,KAAK;IACd,IAAI;MACFtB;IACF,CAAC,GAAG,IAAI,CAACwD,KAAK;IACd,OAAO,aAAajK,KAAK,CAACoH,aAAa,CAAC,KAAK,EAAE;MAC7C2F,SAAS,EAAE,qCAAqC;MAChDI,KAAK,EAAE;QACLC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE;MACb;IACF,CAAC,EAAE5G,SAAS,CAACpD,GAAG,CAAC,CAACyJ,IAAI,EAAEnP,CAAC,KAAK;MAC5B;MACA,IAAI+F,IAAI,GAAGvD,GAAG,CAAC2M,IAAI,EAAE/J,OAAO,EAAE,MAAM,CAAC;MACrC,IAAI6D,OAAO,GAAG,IAAI;MAClB,IAAI,aAAa5G,KAAK,CAACmH,cAAc,CAAC+F,gBAAgB,CAAC,EAAE;QACvDtG,OAAO,GAAG,aAAa5G,KAAK,CAACqH,YAAY,CAAC6F,gBAAgB,EAAEJ,IAAI,EAAEnP,CAAC,CAAC;MACtE;MACA,IAAI,OAAOuP,gBAAgB,KAAK,UAAU,EAAE;QAC1CtG,OAAO,GAAGsG,gBAAgB,CAACJ,IAAI,EAAEnP,CAAC,CAAC;MACrC,CAAC,MAAM;QACLiJ,OAAO,GAAGlD,IAAI;MAChB;MACA,QACE;QACA;QACA1D,KAAK,CAACoH,aAAa,CAAC,KAAK,EAAE;UACzBF,OAAO,EAAE,IAAI,CAAC8D,eAAe,CAACzM,IAAI,CAAC,IAAI,EAAEuO,IAAI,EAAEnP,CAAC,CAAC;UACjDsO,GAAG,EAAE,aAAa,CAAC/J,MAAM,CAACvB,QAAQ,CAAC,CAAC,CAAC;UACrCoM,SAAS,EAAE,iCAAiC;UAC5CI,KAAK,EAAE;YACLG,MAAM,EAAE,SAAS;YACjBC,OAAO,EAAE,cAAc;YACvBC,OAAO,EAAE,OAAO;YAChBC,UAAU,EAAE,MAAM;YAClB/E,KAAK,EAAE,MAAM;YACbgF,WAAW,EAAE;UACf;QACF,CAAC,EAAE9G,OAAO;MAAC;IAEf,CAAC,CAAC,CAAC;EACL;EACA+G,MAAMA,CAAA,EAAG;IACP,IAAIC,WAAW,GAAG,IAAI,CAAC7F,KAAK;MAC1B;QACEhE,KAAK;QACLC,MAAM;QACN+I,SAAS;QACTI,KAAK;QACLjK,QAAQ;QACR4D;MACF,CAAC,GAAG8G,WAAW;MACfC,MAAM,GAAGvQ,wBAAwB,CAACsQ,WAAW,EAAEvQ,SAAS,CAAC;IAC3D,IAAIyQ,KAAK,GAAGhN,WAAW,CAAC+M,MAAM,EAAE,KAAK,CAAC;IACtC,OAAO,aAAa7N,KAAK,CAACoH,aAAa,CAACnG,oBAAoB,CAAC8M,QAAQ,EAAE;MACrExO,KAAK,EAAE,IAAI,CAAC0K,KAAK,CAAC+D;IACpB,CAAC,EAAE,aAAahO,KAAK,CAACoH,aAAa,CAAC/F,uBAAuB,EAAE;MAC3D4M,EAAE,EAAE9F,uBAAuB;MAC3B+F,IAAI,EAAE;QACJnG,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBvB,WAAW,EAAE,IAAI,CAACyD,KAAK,CAACzD;MAC1B;IACF,CAAC,CAAC,EAAE,aAAaxG,KAAK,CAACoH,aAAa,CAAClG,eAAe,EAAE;MACpD6L,SAAS,EAAEA,SAAS;MACpBI,KAAK,EAAEA,KAAK;MACZpJ,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdmK,GAAG,EAAEvL,IAAI,IAAI;QACX,IAAI,IAAI,CAACqH,KAAK,CAAC+D,aAAa,IAAI,IAAI,EAAE;UACpC,IAAI,CAAC5E,QAAQ,CAAC;YACZ4E,aAAa,EAAEpL;UACjB,CAAC,CAAC;QACJ;MACF,CAAC;MACDoE,YAAY,EAAE/E,SAAS;MACvBgF,YAAY,EAAEhF,SAAS;MACvBiF,OAAO,EAAEjF,SAAS;MAClBmM,WAAW,EAAEnM,SAAS;MACtBoM,WAAW,EAAEpM,SAAS;MACtBqM,SAAS,EAAErM,SAAS;MACpBsM,aAAa,EAAEtM,SAAS;MACxBuM,aAAa,EAAEvM,SAAS;MACxBwM,YAAY,EAAExM,SAAS;MACvByM,WAAW,EAAE,IAAI,CAACC,eAAe;MACjCC,UAAU,EAAE3M;IACd,CAAC,EAAE,aAAajC,KAAK,CAACoH,aAAa,CAAC/G,OAAO,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAEyP,KAAK,EAAE;MAC/D/J,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAE8C,IAAI,KAAK,MAAM,GAAG9C,MAAM,GAAG,EAAE,GAAGA;IAC1C,CAAC,CAAC,EAAE,IAAI,CAACgJ,cAAc,CAAC,CAAC,EAAE9J,QAAQ,CAAC,EAAE4D,IAAI,KAAK,MAAM,IAAI,IAAI,CAACmG,eAAe,CAAC,CAAC,CAAC,CAAC;EACnF;AACF;AACA/N,eAAe,CAAC+J,gBAAgB,EAAE,aAAa,EAAE,SAAS,CAAC;AAC3D/J,eAAe,CAAC+J,gBAAgB,EAAE,cAAc,EAAE;EAChDzE,WAAW,EAAE,GAAG,IAAI,CAAC,GAAGK,IAAI,CAACgK,IAAI,CAAC,CAAC,CAAC,CAAC;EACrC/L,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,MAAM;EACf+D,IAAI,EAAE,MAAM;EACZqE,iBAAiB,EAAE,CAACtK,MAAM,CAACiO,KAAK;EAChCvD,uBAAuB,EAAE,CAAC1K,MAAM,CAACiO,KAAK;EACtC1D,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF,SAASyD,qBAAqBA,CAAChH,KAAK,EAAE;EACpC,IAAIC,QAAQ,GAAGzG,cAAc,CAAC,CAAC;EAC/B,OAAO,aAAavB,KAAK,CAACoH,aAAa,CAAC6B,gBAAgB,EAAE5K,QAAQ,CAAC,CAAC,CAAC,EAAE0J,KAAK,EAAE;IAC5EC,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL;AACA,OAAO,SAASgH,OAAOA,CAACjH,KAAK,EAAE;EAC7B,IAAIkH,gBAAgB;EACpB,IAAI;IACFlL,KAAK;IACLC;EACF,CAAC,GAAG+D,KAAK;EACT,IAAI,CAACvG,gBAAgB,CAACuC,KAAK,CAAC,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,CAAC,EAAE;IACzD,OAAO,IAAI;EACb;EACA,OAAO,aAAahE,KAAK,CAACoH,aAAa,CAAC9F,qBAAqB,EAAE;IAC7D4N,cAAc,EAAE;MACd/M;IACF,CAAC;IACDgN,cAAc,EAAE,CAACF,gBAAgB,GAAGlH,KAAK,CAACgF,SAAS,MAAM,IAAI,IAAIkC,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG;EACpH,CAAC,EAAE,aAAajP,KAAK,CAACoH,aAAa,CAACpG,eAAe,EAAE;IACnD+C,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC,CAAC,EAAE,aAAahE,KAAK,CAACoH,aAAa,CAACrG,iBAAiB,EAAE;IACtDqO,MAAM,EAAExG;EACV,CAAC,CAAC,EAAE,aAAa5I,KAAK,CAACoH,aAAa,CAAC2H,qBAAqB,EAAEhH,KAAK,CAAC,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}