{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { computeArea } from '../../cartesian/Area';\nimport { selectAxisWithScale, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { getBandSizeOfAxis, getNormalizedStackId, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar selectGraphicalItemStackedData = (state, xAxisId, yAxisId, isPanorama, areaSettings) => {\n  var _stackGroups$stackId;\n  var layout = selectChartLayout(state);\n  var isXAxisCategorical = isCategoricalAxis(layout, 'xAxis');\n  var stackGroups;\n  if (isXAxisCategorical) {\n    stackGroups = selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  } else {\n    stackGroups = selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n  }\n  if (stackGroups == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    stackId\n  } = areaSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var groups = (_stackGroups$stackId = stackGroups[stackId]) === null || _stackGroups$stackId === void 0 ? void 0 : _stackGroups$stackId.stackedData;\n  return groups === null || groups === void 0 ? void 0 : groups.find(v => v.key === dataKey);\n};\nvar pickAreaSettings = (_state, _xAxisId, _yAxisId, _isPanorama, areaSettings) => areaSettings;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedAreaSettings = createSelector([selectUnfilteredCartesianItems, pickAreaSettings], (graphicalItems, areaSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'area' && areaSettingsFromProps.dataKey === cgis.dataKey && getNormalizedStackId(areaSettingsFromProps.stackId) === cgis.stackId && areaSettingsFromProps.data === cgis.data)) {\n    /*\n     * now, at least one of the areas has the same dataKey as the one in props.\n     * Is this a perfect match? Maybe not because we could theoretically have two different Areas with the same dataKey\n     * and the same stackId and the same data but still different areas, yes,\n     * but the chances of that happening are ... lowish.\n     *\n     * A proper fix would be to store the areaSettings in a state too, and compare references directly instead of enumerating the properties.\n     */\n    return areaSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectArea = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectGraphicalItemStackedData, selectChartDataWithIndexesIfNotInPanorama, selectBandSize, selectSynchronisedAreaSettings], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, stackedData, _ref, bandSize, areaSettings) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (areaSettings == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = areaSettings;\n  var displayedData;\n  if (data && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n\n  // Where is this supposed to come from? No charts have that as a prop.\n  var chartBaseValue = undefined;\n  return computeArea({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataStartIndex,\n    areaSettings,\n    stackedData,\n    displayedData,\n    chartBaseValue,\n    bandSize\n  });\n});", "map": {"version": 3, "names": ["createSelector", "computeArea", "selectAxisWithScale", "selectStackGroups", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "selectChartLayout", "selectChartDataWithIndexesIfNotInPanorama", "getBandSizeOfAxis", "getNormalizedStackId", "isCategoricalAxis", "selectXAxisWithScale", "state", "xAxisId", "_yAxisId", "isPanorama", "selectXAxisTicks", "selectYAxisWithScale", "_xAxisId", "yAxisId", "selectYAxisTicks", "selectBandSize", "layout", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "selectGraphicalItemStackedData", "areaSettings", "_stackGroups$stackId", "isXAxisCategorical", "stackGroups", "undefined", "dataKey", "stackId", "groups", "stackedData", "find", "v", "key", "pickAreaSettings", "_state", "_isPanorama", "selectSynchronisedAreaSettings", "graphicalItems", "areaSettingsFromProps", "some", "cgis", "type", "data", "selectArea", "_ref", "bandSize", "chartData", "dataStartIndex", "dataEndIndex", "length", "displayedData", "slice", "chartBaseValue"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/recharts/es6/state/selectors/areaSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeArea } from '../../cartesian/Area';\nimport { selectAxisWithScale, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { getBandSizeOfAxis, getNormalizedStackId, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar selectGraphicalItemStackedData = (state, xAxisId, yAxisId, isPanorama, areaSettings) => {\n  var _stackGroups$stackId;\n  var layout = selectChartLayout(state);\n  var isXAxisCategorical = isCategoricalAxis(layout, 'xAxis');\n  var stackGroups;\n  if (isXAxisCategorical) {\n    stackGroups = selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  } else {\n    stackGroups = selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n  }\n  if (stackGroups == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    stackId\n  } = areaSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var groups = (_stackGroups$stackId = stackGroups[stackId]) === null || _stackGroups$stackId === void 0 ? void 0 : _stackGroups$stackId.stackedData;\n  return groups === null || groups === void 0 ? void 0 : groups.find(v => v.key === dataKey);\n};\nvar pickAreaSettings = (_state, _xAxisId, _yAxisId, _isPanorama, areaSettings) => areaSettings;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedAreaSettings = createSelector([selectUnfilteredCartesianItems, pickAreaSettings], (graphicalItems, areaSettingsFromProps) => {\n  if (graphicalItems.some(cgis => cgis.type === 'area' && areaSettingsFromProps.dataKey === cgis.dataKey && getNormalizedStackId(areaSettingsFromProps.stackId) === cgis.stackId && areaSettingsFromProps.data === cgis.data)) {\n    /*\n     * now, at least one of the areas has the same dataKey as the one in props.\n     * Is this a perfect match? Maybe not because we could theoretically have two different Areas with the same dataKey\n     * and the same stackId and the same data but still different areas, yes,\n     * but the chances of that happening are ... lowish.\n     *\n     * A proper fix would be to store the areaSettings in a state too, and compare references directly instead of enumerating the properties.\n     */\n    return areaSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectArea = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectGraphicalItemStackedData, selectChartDataWithIndexesIfNotInPanorama, selectBandSize, selectSynchronisedAreaSettings], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, stackedData, _ref, bandSize, areaSettings) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (areaSettings == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = areaSettings;\n  var displayedData;\n  if (data && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n\n  // Where is this supposed to come from? No charts have that as a prop.\n  var chartBaseValue = undefined;\n  return computeArea({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataStartIndex,\n    areaSettings,\n    stackedData,\n    displayedData,\n    chartBaseValue,\n    bandSize\n  });\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,mBAAmB,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,8BAA8B,QAAQ,iBAAiB;AACpI,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,iBAAiB,EAAEC,oBAAoB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAClG,IAAIC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKb,mBAAmB,CAACU,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAC7H,IAAIC,gBAAgB,GAAGA,CAACJ,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKX,0BAA0B,CAACQ,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAChI,IAAIE,oBAAoB,GAAGA,CAACL,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKb,mBAAmB,CAACU,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAC7H,IAAIK,gBAAgB,GAAGA,CAACR,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKX,0BAA0B,CAACQ,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAChI,IAAIM,cAAc,GAAGrB,cAAc,CAAC,CAACM,iBAAiB,EAAEK,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,CAAC,EAAE,CAACE,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,KAAK;EACzL,IAAIhB,iBAAiB,CAACY,MAAM,EAAE,OAAO,CAAC,EAAE;IACtC,OAAOd,iBAAiB,CAACe,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;EACpD;EACA,OAAOjB,iBAAiB,CAACgB,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;AACpD,CAAC,CAAC;AACF,IAAIC,8BAA8B,GAAGA,CAACf,KAAK,EAAEC,OAAO,EAAEM,OAAO,EAAEJ,UAAU,EAAEa,YAAY,KAAK;EAC1F,IAAIC,oBAAoB;EACxB,IAAIP,MAAM,GAAGhB,iBAAiB,CAACM,KAAK,CAAC;EACrC,IAAIkB,kBAAkB,GAAGpB,iBAAiB,CAACY,MAAM,EAAE,OAAO,CAAC;EAC3D,IAAIS,WAAW;EACf,IAAID,kBAAkB,EAAE;IACtBC,WAAW,GAAG5B,iBAAiB,CAACS,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;EACtE,CAAC,MAAM;IACLgB,WAAW,GAAG5B,iBAAiB,CAACS,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;EACtE;EACA,IAAIgB,WAAW,IAAI,IAAI,EAAE;IACvB,OAAOC,SAAS;EAClB;EACA,IAAI;IACFC,OAAO;IACPC;EACF,CAAC,GAAGN,YAAY;EAChB,IAAIM,OAAO,IAAI,IAAI,EAAE;IACnB,OAAOF,SAAS;EAClB;EACA,IAAIG,MAAM,GAAG,CAACN,oBAAoB,GAAGE,WAAW,CAACG,OAAO,CAAC,MAAM,IAAI,IAAIL,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACO,WAAW;EAClJ,OAAOD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKN,OAAO,CAAC;AAC5F,CAAC;AACD,IAAIO,gBAAgB,GAAGA,CAACC,MAAM,EAAEvB,QAAQ,EAAEJ,QAAQ,EAAE4B,WAAW,EAAEd,YAAY,KAAKA,YAAY;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIe,8BAA8B,GAAG3C,cAAc,CAAC,CAACK,8BAA8B,EAAEmC,gBAAgB,CAAC,EAAE,CAACI,cAAc,EAAEC,qBAAqB,KAAK;EACjJ,IAAID,cAAc,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,MAAM,IAAIH,qBAAqB,CAACZ,OAAO,KAAKc,IAAI,CAACd,OAAO,IAAIxB,oBAAoB,CAACoC,qBAAqB,CAACX,OAAO,CAAC,KAAKa,IAAI,CAACb,OAAO,IAAIW,qBAAqB,CAACI,IAAI,KAAKF,IAAI,CAACE,IAAI,CAAC,EAAE;IAC3N;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,OAAOJ,qBAAqB;EAC9B;EACA,OAAOb,SAAS;AAClB,CAAC,CAAC;AACF,OAAO,IAAIkB,UAAU,GAAGlD,cAAc,CAAC,CAACM,iBAAiB,EAAEK,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,EAAEO,8BAA8B,EAAEpB,yCAAyC,EAAEc,cAAc,EAAEsB,8BAA8B,CAAC,EAAE,CAACrB,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEU,WAAW,EAAEe,IAAI,EAAEC,QAAQ,EAAExB,YAAY,KAAK;EAClW,IAAI;IACFyB,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGJ,IAAI;EACR,IAAIvB,YAAY,IAAI,IAAI,IAAIN,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAIC,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAID,UAAU,CAAC+B,MAAM,KAAK,CAAC,IAAI9B,UAAU,CAAC8B,MAAM,KAAK,CAAC,IAAIJ,QAAQ,IAAI,IAAI,EAAE;IACpO,OAAOpB,SAAS;EAClB;EACA,IAAI;IACFiB;EACF,CAAC,GAAGrB,YAAY;EAChB,IAAI6B,aAAa;EACjB,IAAIR,IAAI,IAAIA,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;IAC3BC,aAAa,GAAGR,IAAI;EACtB,CAAC,MAAM;IACLQ,aAAa,GAAGJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,KAAK,CAACJ,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIE,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOzB,SAAS;EAClB;;EAEA;EACA,IAAI2B,cAAc,GAAG3B,SAAS;EAC9B,OAAO/B,WAAW,CAAC;IACjBqB,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACV4B,cAAc;IACd1B,YAAY;IACZQ,WAAW;IACXqB,aAAa;IACbE,cAAc;IACdP;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}