{"ast": null, "code": "module.exports = require('../dist/compat/predicate/isPlainObject.js').isPlainObject;", "map": {"version": 3, "names": ["module", "exports", "require", "isPlainObject"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/compat/isPlainObject.js"], "sourcesContent": ["module.exports = require('../dist/compat/predicate/isPlainObject.js').isPlainObject;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,2CAA2C,CAAC,CAACC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}