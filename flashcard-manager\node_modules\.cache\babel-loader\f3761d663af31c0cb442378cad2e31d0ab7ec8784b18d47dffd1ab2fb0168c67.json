{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst last$1 = require('../../array/last.js');\nconst toArray = require('../_internal/toArray.js');\nconst isArrayLike = require('../predicate/isArrayLike.js');\nfunction last(array) {\n  if (!isArrayLike.isArrayLike(array)) {\n    return undefined;\n  }\n  return last$1.last(toArray.toArray(array));\n}\nexports.last = last;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "last$1", "require", "toArray", "isArrayLike", "last", "array", "undefined"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/es-toolkit/dist/compat/array/last.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst last$1 = require('../../array/last.js');\nconst toArray = require('../_internal/toArray.js');\nconst isArrayLike = require('../predicate/isArrayLike.js');\n\nfunction last(array) {\n    if (!isArrayLike.isArrayLike(array)) {\n        return undefined;\n    }\n    return last$1.last(toArray.toArray(array));\n}\n\nexports.last = last;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC7C,MAAMC,OAAO,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAClD,MAAME,WAAW,GAAGF,OAAO,CAAC,6BAA6B,CAAC;AAE1D,SAASG,IAAIA,CAACC,KAAK,EAAE;EACjB,IAAI,CAACF,WAAW,CAACA,WAAW,CAACE,KAAK,CAAC,EAAE;IACjC,OAAOC,SAAS;EACpB;EACA,OAAON,MAAM,CAACI,IAAI,CAACF,OAAO,CAACA,OAAO,CAACG,KAAK,CAAC,CAAC;AAC9C;AAEAT,OAAO,CAACQ,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}