{"ast": null, "code": "export default function (series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, j = 0, d, dy, yp, yn, n, m = series[order[0]].length; j < m; ++j) {\n    for (yp = yn = 0, i = 0; i < n; ++i) {\n      if ((dy = (d = series[order[i]][j])[1] - d[0]) > 0) {\n        d[0] = yp, d[1] = yp += dy;\n      } else if (dy < 0) {\n        d[1] = yn, d[0] = yn += dy;\n      } else {\n        d[0] = 0, d[1] = dy;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["series", "order", "n", "length", "i", "j", "d", "dy", "yp", "yn", "m"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-shape/src/offset/diverging.js"], "sourcesContent": ["export default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, j = 0, d, dy, yp, yn, n, m = series[order[0]].length; j < m; ++j) {\n    for (yp = yn = 0, i = 0; i < n; ++i) {\n      if ((dy = (d = series[order[i]][j])[1] - d[0]) > 0) {\n        d[0] = yp, d[1] = yp += dy;\n      } else if (dy < 0) {\n        d[1] = yn, d[0] = yn += dy;\n      } else {\n        d[0] = 0, d[1] = dy;\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAEC,KAAK,EAAE;EACrC,IAAI,EAAE,CAACC,CAAC,GAAGF,MAAM,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;EAChC,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEP,CAAC,EAAEQ,CAAC,GAAG<PERSON>,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACE,MAAM,EAAEE,CAAC,GAAGK,CAAC,EAAE,EAAEL,CAAC,EAAE;IAC5E,KAAKG,EAAE,GAAGC,EAAE,GAAG,CAAC,EAAEL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MACnC,IAAI,CAACG,EAAE,GAAG,<PERSON>ACD,CAAC,GAAGN,MAAM,CAACC,KAAK,CAACG,CAAC,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;QAClDA,CAAC,CAAC,CAAC,CAAC,GAAGE,EAAE,EAAEF,CAAC,CAAC,CAAC,CAAC,GAAGE,EAAE,IAAID,EAAE;MAC5B,CAAC,MAAM,IAAIA,EAAE,GAAG,CAAC,EAAE;QACjBD,CAAC,CAAC,CAAC,CAAC,GAAGG,EAAE,EAAEH,CAAC,CAAC,CAAC,CAAC,GAAGG,EAAE,IAAIF,EAAE;MAC5B,CAAC,MAAM;QACLD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAGC,EAAE;MACrB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}