{"ast": null, "code": "import { linearish } from \"./linear.js\";\nimport { copy, identity, transformer } from \"./continuous.js\";\nimport { initRange } from \"./init.js\";\nfunction transformPow(exponent) {\n  return function (x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\nexport function powish(transform) {\n  var scale = transform(identity, identity),\n    exponent = 1;\n  function rescale() {\n    return exponent === 1 ? transform(identity, identity) : exponent === 0.5 ? transform(transformSqrt, transformSquare) : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n  scale.exponent = function (_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n  return linearish(scale);\n}\nexport default function pow() {\n  var scale = powish(transformer());\n  scale.copy = function () {\n    return copy(scale, pow()).exponent(scale.exponent());\n  };\n  initRange.apply(scale, arguments);\n  return scale;\n}\nexport function sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}", "map": {"version": 3, "names": ["linearish", "copy", "identity", "transformer", "initRange", "transformPow", "exponent", "x", "Math", "pow", "transformSqrt", "sqrt", "transformSquare", "powish", "transform", "scale", "rescale", "_", "arguments", "length", "apply"], "sources": ["D:/FlashCardManager/flashcard-manager/node_modules/d3-scale/src/pow.js"], "sourcesContent": ["import {linearish} from \"./linear.js\";\nimport {copy, identity, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformPow(exponent) {\n  return function(x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\n\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\n\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\n\nexport function powish(transform) {\n  var scale = transform(identity, identity),\n      exponent = 1;\n\n  function rescale() {\n    return exponent === 1 ? transform(identity, identity)\n        : exponent === 0.5 ? transform(transformSqrt, transformSquare)\n        : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n\n  scale.exponent = function(_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n\n  return linearish(scale);\n}\n\nexport default function pow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, pow()).exponent(scale.exponent());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n\nexport function sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,aAAa;AACrC,SAAQC,IAAI,EAAEC,QAAQ,EAAEC,WAAW,QAAO,iBAAiB;AAC3D,SAAQC,SAAS,QAAO,WAAW;AAEnC,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC9B,OAAO,UAASC,CAAC,EAAE;IACjB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAACC,IAAI,CAACC,GAAG,CAAC,CAACF,CAAC,EAAED,QAAQ,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACF,CAAC,EAAED,QAAQ,CAAC;EAChE,CAAC;AACH;AAEA,SAASI,aAAaA,CAACH,CAAC,EAAE;EACxB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAACC,IAAI,CAACG,IAAI,CAAC,CAACJ,CAAC,CAAC,GAAGC,IAAI,CAACG,IAAI,CAACJ,CAAC,CAAC;AAC9C;AAEA,SAASK,eAAeA,CAACL,CAAC,EAAE;EAC1B,OAAOA,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAC/B;AAEA,OAAO,SAASM,MAAMA,CAACC,SAAS,EAAE;EAChC,IAAIC,KAAK,GAAGD,SAAS,CAACZ,QAAQ,EAAEA,QAAQ,CAAC;IACrCI,QAAQ,GAAG,CAAC;EAEhB,SAASU,OAAOA,CAAA,EAAG;IACjB,OAAOV,QAAQ,KAAK,CAAC,GAAGQ,SAAS,CAACZ,QAAQ,EAAEA,QAAQ,CAAC,GAC/CI,QAAQ,KAAK,GAAG,GAAGQ,SAAS,CAACJ,aAAa,EAAEE,eAAe,CAAC,GAC5DE,SAAS,CAACT,YAAY,CAACC,QAAQ,CAAC,EAAED,YAAY,CAAC,CAAC,GAAGC,QAAQ,CAAC,CAAC;EACrE;EAEAS,KAAK,CAACT,QAAQ,GAAG,UAASW,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAIb,QAAQ,GAAG,CAACW,CAAC,EAAED,OAAO,CAAC,CAAC,IAAIV,QAAQ;EACjE,CAAC;EAED,OAAON,SAAS,CAACe,KAAK,CAAC;AACzB;AAEA,eAAe,SAASN,GAAGA,CAAA,EAAG;EAC5B,IAAIM,KAAK,GAAGF,MAAM,CAACV,WAAW,CAAC,CAAC,CAAC;EAEjCY,KAAK,CAACd,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACc,KAAK,EAAEN,GAAG,CAAC,CAAC,CAAC,CAACH,QAAQ,CAACS,KAAK,CAACT,QAAQ,CAAC,CAAC,CAAC;EACtD,CAAC;EAEDF,SAAS,CAACgB,KAAK,CAACL,KAAK,EAAEG,SAAS,CAAC;EAEjC,OAAOH,KAAK;AACd;AAEA,OAAO,SAASJ,IAAIA,CAAA,EAAG;EACrB,OAAOF,GAAG,CAACW,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,CAACZ,QAAQ,CAAC,GAAG,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}