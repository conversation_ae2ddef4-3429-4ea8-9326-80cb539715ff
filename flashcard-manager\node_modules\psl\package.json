{"name": "psl", "version": "1.15.0", "description": "Domain name parser based on the Public Suffix List", "repository": {"type": "git", "url": "**************:lupomontero/psl.git"}, "type": "module", "main": "./dist/psl.cjs", "exports": {".": {"import": "./dist/psl.mjs", "require": "./dist/psl.cjs"}}, "types": "types/index.d.ts", "scripts": {"lint": "eslint .", "test": "mocha test/*.spec.js", "test:browserstack": "browserstack-node-sdk playwright test", "watch": "mocha test/*.spec.js --watch", "update-rules": "./scripts/update-rules.js", "build": "vite build", "postbuild": "ln -s ./psl.umd.cjs dist/psl.js && ln -s ./psl.umd.cjs dist/psl.min.js", "benchmark": "node --experimental-vm-modules --no-warnings benchmark/suite.js", "changelog": "git log $(git describe --tags --abbrev=0)..HEAD --oneline --format=\"%h %s (%an <%ae>)\""}, "keywords": ["publicsuffix", "publicsuffixlist"], "author": "<PERSON><PERSON> <<EMAIL>> (https://lupomontero.com/)", "funding": "https://github.com/sponsors/lupomontero", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"@eslint/js": "^9.16.0", "@playwright/test": "^1.49.0", "@types/eslint__js": "^8.42.3", "benchmark": "^2.1.4", "browserstack-node-sdk": "^1.34.27", "eslint": "^9.16.0", "mocha": "^10.8.2", "typescript": "^5.7.2", "typescript-eslint": "^8.16.0", "vite": "^6.0.2"}}