import React, { useState, useEffect } from 'react';
import { Flashcard, Theme } from './types';
import { saveFlashcards, loadFlashcards, saveTheme, loadTheme } from './utils/storage';
import FlashcardManager from './components/FlashcardManager';
import QuizMode from './components/QuizMode';
import FlashcardStats from './components/FlashcardStats';
import ThemeToggle from './components/ThemeToggle';
import { FaBook, FaQuestionCircle, FaChartPie } from 'react-icons/fa';

function App() {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [activeTab, setActiveTab] = useState<'manager' | 'quiz' | 'stats'>('manager');
  const [theme, setTheme] = useState<Theme>('light');
  const [lastAnsweredId, setLastAnsweredId] = useState<string | undefined>();

  // Load data on component mount
  useEffect(() => {
    const savedFlashcards = loadFlashcards();
    const savedTheme = loadTheme();

    setFlashcards(savedFlashcards);
    setTheme(savedTheme);

    // Apply theme to document
    if (savedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    }
  }, []);

  // Save flashcards whenever they change
  useEffect(() => {
    saveFlashcards(flashcards);
  }, [flashcards]);

  const handleAddFlashcard = (question: string, answer: string) => {
    const newCard: Flashcard = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      question,
      answer,
      score: 0,
      createdAt: new Date(),
    };
    setFlashcards(prev => [...prev, newCard]);
  };

  const handleDeleteFlashcard = (id: string) => {
    setFlashcards(prev => prev.filter(card => card.id !== id));
  };

  const handleEditFlashcard = (id: string, question: string, answer: string) => {
    setFlashcards(prev =>
      prev.map(card =>
        card.id === id
          ? { ...card, question, answer }
          : card
      )
    );
  };

  const handleUpdateScore = (index: number, isCorrect: boolean) => {
    setFlashcards(prev => {
      const newFlashcards = [...prev];
      const card = newFlashcards[index];

      if (isCorrect) {
        card.score = Math.min(card.score + 1, 10); // Cap at 10
      } else {
        card.score = Math.max(card.score - 1, 0); // Floor at 0
      }

      card.lastAnswered = new Date();
      setLastAnsweredId(card.id);

      // Clear the highlight after 3 seconds
      setTimeout(() => {
        setLastAnsweredId(undefined);
      }, 3000);

      return newFlashcards;
    });
  };

  const handleToggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    saveTheme(newTheme);

    // Apply theme to document
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const tabs = [
    { id: 'manager' as const, label: 'Správa kartiček', icon: FaBook },
    { id: 'quiz' as const, label: 'Kvíz', icon: FaQuestionCircle },
    { id: 'stats' as const, label: 'Statistiky', icon: FaChartPie },
  ];

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 transition-colors duration-300">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                FlashCard Manager
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <ThemeToggle theme={theme} onToggle={handleToggleTheme} />
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  {React.createElement(Icon, { className: "w-4 h-4" })}
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'manager' && (
          <FlashcardManager
            flashcards={flashcards}
            onAddFlashcard={handleAddFlashcard}
            onDeleteFlashcard={handleDeleteFlashcard}
            onEditFlashcard={handleEditFlashcard}
            lastAnsweredId={lastAnsweredId}
          />
        )}

        {activeTab === 'quiz' && (
          <QuizMode
            flashcards={flashcards}
            onUpdateScore={handleUpdateScore}
          />
        )}

        {activeTab === 'stats' && (
          <FlashcardStats flashcards={flashcards} />
        )}
      </main>
    </div>
  );
}

export default App;
