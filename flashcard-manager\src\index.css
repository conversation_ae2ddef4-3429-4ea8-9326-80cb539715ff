@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom animations for feedback */
.animate-success {
  animation: success-flash 0.5s ease-in-out;
}

.animate-error {
  animation: error-flash 0.5s ease-in-out;
}

@keyframes success-flash {
  0% { background-color: rgb(34 197 94); }
  100% { background-color: transparent; }
}

@keyframes error-flash {
  0% { background-color: rgb(239 68 68); }
  100% { background-color: transparent; }
}
